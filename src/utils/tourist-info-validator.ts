/**
 * 游客信息格式验证工具
 */

// 手机号正则：11位数字，以1开头
const PHONE_REGEX = /^1\d{10}$/;

// 身份证号正则：18位，前17位为数字，最后一位可以是数字或X/x
const ID_CARD_REGEX = /^\d{17}[\dXx]$/;

// 分隔符正则：空格、换行、中英文逗号、中英文冒号等
const SEPARATOR_REGEX = /[\s\n,，:：]+/;

export interface ValidationResult {
  isValid: boolean;
  invalidPhones: string[];
  invalidIdCards: string[];
  message: string;
}

export interface HighlightInfo {
  text: string;
  isInvalid: boolean;
  type: 'phone' | 'idcard' | 'other';
}

/**
 * 从文本中提取手机号（包括格式不正确的）
 */
function extractPhones(text: string): string[] {
  // 先提取所有可能的手机号模式：
  // 1. 标准11位手机号：1开头的11位数字
  // 2. 不完整手机号：1开头的5-15位数字
  // 3. 其他可能的手机号模式
  const phonePatterns = [
    /\b1\d{4,14}\b/g, // 1开头的5-15位数字（涵盖各种长度的手机号）
  ];

  const allMatches = new Set<string>();

  phonePatterns.forEach((pattern) => {
    const matches = text.match(pattern);
    if (matches) {
      matches.forEach((match) => allMatches.add(match));
    }
  });

  const result = Array.from(allMatches);
  console.log('📱 [提取手机号] 输入文本:', text);
  console.log('📱 [提取手机号] 提取到的所有手机号模式:', result);
  return result;
}

/**
 * 从文本中提取身份证号（包括格式不正确的）
 */
function extractIdCards(text: string): string[] {
  // 提取所有可能的身份证号模式：
  // 1. 标准18位身份证号：17位数字+1位数字或X
  // 2. 不完整身份证号：15-20位数字（可能包含X）
  const idCardPatterns = [
    /\b\d{15,20}[Xx]?\b/g, // 15-20位数字，可选X结尾
    /\b\d{15,19}[Xx]\b/g, // 15-19位数字+X结尾
  ];

  const allMatches = new Set<string>();

  idCardPatterns.forEach((pattern) => {
    const matches = text.match(pattern);
    if (matches) {
      matches.forEach((match) => {
        // 过滤掉明显不是身份证号的数字串（比如太短的）
        if (match.length >= 15) {
          allMatches.add(match);
        }
      });
    }
  });

  const result = Array.from(allMatches);
  console.log('🆔 [提取身份证号] 输入文本:', text);
  console.log('🆔 [提取身份证号] 提取到的所有身份证号模式:', result);
  return result;
}

/**
 * 验证手机号格式
 */
function validatePhone(phone: string): boolean {
  return PHONE_REGEX.test(phone);
}

/**
 * 验证身份证号格式
 */
function validateIdCard(idCard: string): boolean {
  return ID_CARD_REGEX.test(idCard);
}

/**
 * 验证游客信息格式
 */
export function validateTouristInfo(text: string): ValidationResult {
  console.log('🔍 [游客信息验证] 开始验证:', text);

  if (!text || !text.trim()) {
    console.log('✅ [游客信息验证] 空值，返回有效');
    return {
      isValid: true,
      invalidPhones: [],
      invalidIdCards: [],
      message: '',
    };
  }

  const phones = extractPhones(text);
  const idCards = extractIdCards(text);

  console.log('📱 [游客信息验证] 提取到的手机号:', phones);
  console.log('🆔 [游客信息验证] 提取到的身份证号:', idCards);

  const invalidPhones = phones.filter((phone) => !validatePhone(phone));
  const invalidIdCards = idCards.filter((idCard) => !validateIdCard(idCard));

  console.log('❌ [游客信息验证] 无效手机号:', invalidPhones);
  console.log('❌ [游客信息验证] 无效身份证号:', invalidIdCards);

  // 检查是否有格式错误的数据
  const hasInvalidData = invalidPhones.length > 0 || invalidIdCards.length > 0;
  console.log('🚨 [游客信息验证] 是否有格式错误:', hasInvalidData);

  // 移除对缺少手机号或身份证号的验证，只检查格式错误

  let message = '';
  if (hasInvalidData) {
    const errorParts = [];

    // 处理无效手机号的详细错误信息
    if (invalidPhones.length > 0) {
      const phoneErrors = invalidPhones.map((phone) => {
        if (phone.length < 11) {
          return `${phone}（应为11位，当前${phone.length}位）`;
        }
        if (phone.length > 11) {
          return `${phone}（应为11位，当前${phone.length}位）`;
        }
        if (!phone.startsWith('1')) {
          return `${phone}（应以1开头）`;
        }
        return `${phone}（格式不正确）`;
      });
      errorParts.push(`手机号格式有误：${phoneErrors.join('、')}`);
    }

    // 处理无效身份证号的详细错误信息
    if (invalidIdCards.length > 0) {
      const idCardErrors = invalidIdCards.map((idCard) => {
        if (idCard.length < 18) {
          return `${idCard}（应为18位，当前${idCard.length}位）`;
        }
        if (idCard.length > 18) {
          return `${idCard}（应为18位，当前${idCard.length}位）`;
        }
        return `${idCard}（格式不正确）`;
      });
      errorParts.push(`身份证格式有误：${idCardErrors.join('、')}`);
    }

    message = errorParts.join('，');
    console.log('📝 [游客信息验证] 格式错误消息:', message);
  }

  // 只要没有格式错误就认为验证通过（不再检查是否缺少手机号或身份证号）
  const isValid = !hasInvalidData;

  console.log('🎯 [游客信息验证] 最终验证结果:', {
    isValid,
    hasInvalidData,
    message,
    invalidPhones,
    invalidIdCards,
  });

  return {
    isValid,
    invalidPhones,
    invalidIdCards,
    message,
  };
}

/**
 * 生成高亮信息，用于在文本中标记无效的手机号和身份证号
 */
export function generateHighlightInfo(text: string): HighlightInfo[] {
  if (!text || !text.trim()) {
    return [];
  }

  const result: HighlightInfo[] = [];
  const phones = extractPhones(text);
  const idCards = extractIdCards(text);

  // 创建所有需要高亮的项目映射
  const highlightMap = new Map<
    string,
    { type: 'phone' | 'idcard'; isInvalid: boolean }
  >();

  phones.forEach((phone) => {
    highlightMap.set(phone, {
      type: 'phone',
      isInvalid: !validatePhone(phone),
    });
  });

  idCards.forEach((idCard) => {
    highlightMap.set(idCard, {
      type: 'idcard',
      isInvalid: !validateIdCard(idCard),
    });
  });

  // 如果没有需要高亮的内容，返回原文本
  if (highlightMap.size === 0) {
    return [
      {
        text,
        isInvalid: false,
        type: 'other',
      },
    ];
  }

  // 按照在文本中出现的位置排序
  const sortedItems = Array.from(highlightMap.entries())
    .map(([item, info]) => ({
      item,
      ...info,
      index: text.indexOf(item),
    }))
    .filter((item) => item.index !== -1)
    .sort((a, b) => a.index - b.index);

  let lastIndex = 0;

  sortedItems.forEach(({ item, type, isInvalid, index }) => {
    // 添加前面的普通文本
    if (index > lastIndex) {
      const beforeText = text.substring(lastIndex, index);
      if (beforeText) {
        result.push({
          text: beforeText,
          isInvalid: false,
          type: 'other',
        });
      }
    }

    // 添加高亮项目
    result.push({
      text: item,
      isInvalid,
      type,
    });

    lastIndex = index + item.length;
  });

  // 添加剩余的普通文本
  if (lastIndex < text.length) {
    const remainingText = text.substring(lastIndex);
    if (remainingText) {
      result.push({
        text: remainingText,
        isInvalid: false,
        type: 'other',
      });
    }
  }

  return result;
}

/**
 * 简单的验证函数，只返回是否有格式错误
 */
export function hasTouristInfoError(text: string): boolean {
  const result = validateTouristInfo(text);
  return !result.isValid;
}
