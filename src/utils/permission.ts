import { useUserStore } from '@/store';
import { intersection } from 'lodash';

/**
 * 检查用户是否有权限访问指定功能
 * @param roles 角色权限数组
 * @param allowOutUser 是否允许外部用户访问
 * @returns 是否有权限
 */
export function hasPermission(
  roles: (string | number)[],
  allowOutUser?: boolean
): boolean {
  const userStore = useUserStore();

  // 基础角色权限检查
  const hasBasicPermission =
    roles.includes('*') ||
    intersection(roles, userStore.roles || []).length > 0;

  if (!hasBasicPermission) {
    return false;
  }

  // 三木且是电销角色 拥有更多权限 不再继续检查外部用户
  if (userStore.is_three_wood_customer_service) {
    return true;
  }

  // 外部用户额外权限检查
  if (userStore.out_user === 1) {
    return allowOutUser === true;
  }

  // 内部用户保持原有逻辑
  return true;
}

/**
 * 检查用户是否为外部用户
 * @returns 是否为外部用户
 */
export function isOutUser(): boolean {
  const userStore = useUserStore();
  return userStore.out_user === 1 && !userStore.is_three_wood_customer_service;
}

/**
 * 检查外部用户是否有权限访问指定功能
 * @param allowOutUser 是否允许外部用户访问
 * @returns 外部用户是否有权限
 */
export function hasOutUserPermission(allowOutUser?: boolean): boolean {
  if (isOutUser()) {
    return allowOutUser === true;
  }
  return true;
}

/**
 * 获取用户权限信息
 * @returns 用户权限信息
 */
export function getUserPermissionInfo() {
  const userStore = useUserStore();
  return {
    isOutUser: userStore.out_user === 1,
    roles: userStore.roles || [],
    roleId: userStore.role_id,
  };
}
