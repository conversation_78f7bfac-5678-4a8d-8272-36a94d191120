import { computed } from 'vue';
import { useUserStore } from '@/store';
import {
  hasPermission,
  isOutUser,
  hasOutUserPermission,
  getUserPermissionInfo,
} from '@/utils/permission';

/**
 * 权限管理 Hook
 * 提供统一的权限检查功能，支持外部用户权限控制
 */
export function usePermission() {
  const userStore = useUserStore();

  // 用户权限信息
  const userInfo = computed(() => getUserPermissionInfo());

  // 是否为外部用户
  const isExternalUser = computed(() => isOutUser());

  /**
   * 检查是否有指定权限
   * @param roles 角色权限数组
   * @param allowOutUser 是否允许外部用户访问
   * @returns 是否有权限
   */
  const checkPermission = (
    roles: (string | number)[],
    allowOutUser?: boolean
  ): boolean => {
    return hasPermission(roles, allowOutUser);
  };

  /**
   * 检查外部用户权限
   * @param allowOutUser 是否允许外部用户访问
   * @returns 外部用户是否有权限
   */
  const checkOutUserPermission = (allowOutUser?: boolean): boolean => {
    return hasOutUserPermission(allowOutUser);
  };

  /**
   * 检查路由权限
   * @param route 路由对象
   * @returns 是否有权限访问路由
   */
  const checkRoutePermission = (route: any): boolean => {
    if (!route.meta?.requiresAuth) {
      return true;
    }

    const roles = route.meta?.roles || [];
    const allowOutUser = route.meta?.allow_out_user;

    return checkPermission(roles, allowOutUser);
  };

  /**
   * 创建权限检查函数
   * @param roles 角色权限数组
   * @param allowOutUser 是否允许外部用户访问
   * @returns 权限检查函数
   */
  const createPermissionChecker = (
    roles: (string | number)[],
    allowOutUser?: boolean
  ) => {
    return () => checkPermission(roles, allowOutUser);
  };

  return {
    // 用户信息
    userInfo,
    isExternalUser,

    // 权限检查方法
    checkPermission,
    checkOutUserPermission,
    checkRoutePermission,
    createPermissionChecker,

    // 兼容原有方法
    hasPermission: userStore.hasPermission,
    hasRolePermission: userStore.hasRolePermission,
    hasFullPermission: userStore.hasFullPermission,
    hasOutUserPermission: userStore.hasOutUserPermission,
  };
}
