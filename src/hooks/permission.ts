import { RouteLocationNormalized, RouteRecordRaw } from 'vue-router';
import { useUserStore } from '@/store';
import { intersection } from 'lodash';

export default function usePermission() {
  const userStore = useUserStore();
  return {
    accessRouter(route: RouteLocationNormalized | RouteRecordRaw) {
      // 基础权限检查
      const hasBasicPermission =
        !route.meta?.requiresAuth ||
        !route.meta?.roles ||
        route.meta?.roles?.includes('*') ||
        intersection(route.meta?.roles || [], userStore.roles || []).length;

      // 如果基础权限检查不通过，直接返回 false
      if (!hasBasicPermission) {
        return false;
      }

      // 外部用户额外权限检查
      if (userStore.out_user === 1) {
        // 配置了 三木且是电销角色 拥有更多权限 不再继续检查外部用户
        if (
          route.meta?.allow_three_wood_customer_service === true &&
          userStore.is_three_wood_customer_service
        ) {
          return true;
        }

        // 如果是外部用户，需要检查 allow_out_user 字段
        return route.meta?.allow_out_user === true;
      }

      // 内部用户保持原有逻辑
      return true;
    },
    findFirstPermissionRoute(_routers: any, roles = []) {
      const cloneRouters = [..._routers];
      while (cloneRouters.length) {
        const firstElement = cloneRouters.shift();

        // 检查基础角色权限
        const hasRolePermission = firstElement?.meta?.roles?.find(
          (el: (number | string)[]) => {
            return el.includes('*') || intersection(el, roles).length > 0;
          }
        );

        if (hasRolePermission) {
          // 如果是外部用户，还需要检查 allow_out_user
          if (userStore.out_user === 1) {
            if (firstElement?.meta?.allow_out_user === true) {
              return { name: firstElement.name };
            }
          } else {
            // 内部用户直接返回
            return { name: firstElement.name };
          }
        }

        if (firstElement?.children) {
          cloneRouters.push(...firstElement.children);
        }
      }
      return null;
    },
    // You can add any rules you want
  };
}
