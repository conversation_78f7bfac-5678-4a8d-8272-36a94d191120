import { DirectiveBinding } from 'vue';
import { hasPermission } from '@/utils/permission';

function checkPermission(el: HTMLElement, binding: DirectiveBinding) {
  const { value } = binding;

  if (Array.isArray(value)) {
    if (value.length > 0) {
      const permissionValues = value;

      // 从元素的 dataset 中获取 allow_out_user 信息
      const allowOutUser = el.dataset.allowOutUser === 'true';

      // 使用统一的权限检查函数
      const hasAccess = hasPermission(permissionValues, allowOutUser);

      if (!hasAccess && el.parentNode) {
        el.parentNode.removeChild(el);
      }
    }
  } else {
    throw new Error(`need roles! Like v-permission="['admin','user']"`);
  }
}

export default {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding);
  },
  updated(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding);
  },
};
