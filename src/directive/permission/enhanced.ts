import { DirectiveBinding } from 'vue';
import { hasPermission } from '@/utils/permission';

interface PermissionValue {
  roles: (string | number)[];
  allowOutUser?: boolean;
}

function checkEnhancedPermission(el: HTMLElement, binding: DirectiveBinding) {
  const { value } = binding;

  // 支持两种格式：
  // 1. v-permission-enhanced="{ roles: ['admin'], allowOutUser: true }"
  // 2. v-permission-enhanced="['admin']" (兼容原有格式)

  let roles: (string | number)[] = [];
  let allowOutUser: boolean | undefined;

  if (Array.isArray(value)) {
    // 兼容原有格式
    roles = value;
    // 从元素的 dataset 中获取 allow_out_user 信息
    allowOutUser = el.dataset.allowOutUser === 'true';
  } else if (typeof value === 'object' && value !== null) {
    // 新格式
    const permissionValue = value as PermissionValue;
    roles = permissionValue.roles || [];
    allowOutUser = permissionValue.allowOutUser;
  } else {
    throw new Error(
      `Invalid permission value. Use v-permission-enhanced="{ roles: ['admin'], allowOutUser: true }" or v-permission-enhanced="['admin']"`
    );
  }

  if (roles.length > 0) {
    // 使用统一的权限检查函数
    const hasAccess = hasPermission(roles, allowOutUser);

    if (!hasAccess && el.parentNode) {
      el.parentNode.removeChild(el);
    }
  }
}

export default {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    checkEnhancedPermission(el, binding);
  },
  updated(el: HTMLElement, binding: DirectiveBinding) {
    checkEnhancedPermission(el, binding);
  },
};
