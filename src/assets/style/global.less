* {
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 14px;
  //background-color: var(--color-bg-1);
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

body::before {
  width: 100%;
  height: 100vh;
  // background: url('@/assets/images/bg.image') top repeat; //工作台的背景图
  background: url('@/assets/images/layout-bg.png'); //一站式的背景图
  background-size: 100% 100%;
  opacity: 0.8;
  bottom: 0;
  content: '';
  display: block;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: -2;
}

a {
  color: inherit;
  text-decoration: none;
}

.initA {
  text-decoration: none;
  color: inherit;
}

body[arco-theme='dark']::before {
  background: var(--color-bg-1);
  opacity: 1;
}
.card-b {
  border: 1px solid var(--color-neutral-3) !important;
}

// 滚动条设置
/*定义滚动条高宽及背景
 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 9px;
  height: 9px;
}
/*定义滚动条轨道
 内阴影+圆角*/
::-webkit-scrollbar-track {
  border-radius: 9px;
}
/*定义滑块
 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 9px;
  background-color: var(--color-fill-4);
}

.echarts-tooltip-diy {
  background: linear-gradient(
    304.17deg,
    rgba(253, 254, 255, 0.6) -6.04%,
    rgba(244, 247, 252, 0.6) 85.2%
  ) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  /* Note: backdrop-filter has minimal browser support */

  border-radius: 6px !important;
  .content-panel {
    display: flex;
    justify-content: space-between;
    padding: 0 9px;
    background: rgba(255, 255, 255, 0.8);
    width: 164px;
    height: 32px;
    line-height: 32px;
    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
    border-radius: 4px;
    margin-bottom: 4px;
  }
  .tooltip-title {
    margin: 0 0 10px 0;
  }
  p {
    margin: 0;
  }
  .tooltip-title,
  .tooltip-value {
    font-size: 13px;
    line-height: 15px;
    display: flex;
    align-items: center;
    text-align: right;
    color: #1d2129;
    font-weight: bold;
  }
  .tooltip-item-icon {
    display: inline-block;
    margin-right: 8px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
  }
}

.general-card {
  border-radius: 4px;
  border: none;
  & > .arco-card-header {
    height: auto;
    padding: 20px;
    border: none;
  }
  & > .arco-card-body {
    padding: 0 20px 20px 20px;
  }
}

.split-line {
  border-color: rgb(var(--gray-2));
}

.arco-table-cell {
  .circle {
    display: inline-block;
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: rgb(var(--blue-6));
    &.pass {
      background-color: rgb(var(--green-6));
    }
  }
}

.content-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

@list: 0, 5, 10, 20;
each(@list, {
  .mr-@{value} {
    margin-right: @value * 1px !important;
  }
  .mt-@{value} {
    margin-top: @value * 1px !important;
  }
  .mb-@{value} {
    margin-bottom: @value * 1px !important;
  }
  .ml-@{value} {
    margin-left: @value * 1px !important;
  }
  .m-@{value} {
    margin: @value * 1px;
  }
  .pt-@{value} {
    padding-top: @value * 1px !important;
  }
  .pb-@{value} {
    padding-bottom: @value * 1px !important;
  }
  .pr-@{value} {
    padding-right: @value * 1px !important;
  }
  .pl-@{value} {
    padding-left: @value * 1px !important;
  }
  .p-@{value} {
    padding: @value * 1px;
  }
});

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.df {
  display: flex;
}

.fw-wrap {
  flex-wrap: wrap;
}

.jc-sa {
  display: flex;
  justify-content: space-around;
}

.jc-sb {
  display: flex;
  justify-content: space-between;
}

.jc-cen {
  display: flex;
  justify-content: center;
}

.ai-cen {
  display: flex;
  align-items: center;
}
.ai-end {
  display: flex;
  align-items: flex-end;
}

.ai-st {
  display: flex;
  align-items: flex-start;
}

.fd-cl {
  flex-direction: column;
}
.fd-r {
  flex-direction: row !important;
}
@list1: 50, 100, 150, 200;
each(@list1, {
  .w-@{value} {
    width: @value * 1px !important;
  }
}) .w100p {
  width: 100%;
}

.arco-input-group {
  background-color: var(--color-fill-2);
  .arco-checkbox {
    padding-left: 10px;
    padding-right: 10px;
  }
}

.table-card {
  flex: 1;
  margin-top: 10px;
  .arco-card-body {
    height: 100%;
  }
  .table-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    //padding: 5px 5px;
    &.check-header {
      padding: 5px 10px;
      background: rgb(var(--primary-1));
      color: rgb(var(--color-neutral-6));
      border-radius: 5px;
      .close-btn {
        cursor: pointer;
        font-size: 16px;
        transition: all 0.2s linear;
        &:hover {
          transform: scale(1.05);
        }
      }
    }
  }
}

.arco-input-wrapper {
  padding-left: 0;
  .arco-input {
    padding-left: 12px;
  }
}

//全局导航
.arco-trigger-menu .arco-trigger-menu-has-icon .arco-trigger-menu-icon {
  display: none !important;
}

.right-control-box {
  flex: 1;
  width: 950px;
}

.arco-divider-horizontal {
  border-bottom: 1px solid var(--color-neutral-2);
  margin: 10px 0;
}

.video-view-box {
  width: 100px;
  height: 60px;
  position: relative;
  line-height: 0;
  cursor: pointer;
  overflow: hidden;
  background: #000;
  border-radius: 4px;

  .video-wrap {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 5px;
  }

  .cover-ele {
    position: relative;
    width: 100%;
    height: 100%;
    // background: #211d2f;
    border-radius: 5px;
    object-fit: contain;
    z-index: 2;
    // &.min {
    //   width: 80px;
    //   height: 80px;
    // }
    img {
      width: 100% !important;
      height: 100% !important;
    }
    .arco-image-error-icon {
      position: relative;
      height: 40px;
      width: 100%;
      margin-top: 15px;
      z-index: 2;
    }
  }
  .cover-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 5px;
    object-fit: cover;
    z-index: 1;
    background: #211d2f;
    filter: blur(7px);
    transform: scale(1.5);
    opacity: 0.7;
  }

  .video-ele {
    width: 100%;
    height: 100%;
    background: #000;
    object-fit: contain;
  }

  .video-view-group {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    background: rgba(0, 0, 0, 0);
    cursor: pointer;
    z-index: 2;

    .video-view-btn {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      font-size: 24px;
      opacity: 1;
      z-index: 2;
      background: rgba(0, 0, 0, 0.3);
      padding: 5px;
      border-radius: 50%;
    }

    &:hover {
      .video-view-btn {
        opacity: 0.8;
      }
    }
  }
  .video-img-btn {
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: 2;
  }
}

.primary_text {
  color: rgb(var(--primary-6));
}
.error_text {
  color: rgb(var(--danger-6));
}
.success_text {
  color: rgb(var(--success-6));
}
.warning_text {
  color: rgb(var(--warning-6));
}

.text_underline {
  text-decoration: underline;
}

.cur-por {
  cursor: pointer;
}

.a-text {
  color: rgb(var(--primary-6)) !important;
  cursor: pointer;
}

// 菜单图标间距调整
.arco-menu-icon {
  margin-right: 10px !important;
}

//pop的箭头边框重置
.arco-menu-pop-trigger.arco-trigger-position-bl .arco-trigger-arrow {
  border: 0;
}

.erji {
  color: var(--color-text-3) !important;
}

.export-popover .arco-popover-content {
  margin: 0;
}

// DepartmentUserTreeSelect 组件全局样式
.no-padding-popover .arco-popover-content {
  padding: 0 !important;
}

.department-tree-popover {
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  min-width: 300px;

  .tree-select-content {
    .search-wrapper {
      padding: 0 0 12px 0;
      border-bottom: 1px solid var(--color-border-1);

      .arco-input {
        border-radius: 6px;
      }
    }

    .loading-wrapper,
    .error-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24px;
      gap: 8px;

      .loading-text,
      .error-text {
        color: var(--color-text-2);
        font-size: 14px;
      }
    }

    .error-wrapper {
      flex-direction: column;
      gap: 12px;

      .arco-icon {
        color: var(--color-danger-6);
        font-size: 18px;
      }
    }

    .tree-wrapper {
      max-height: 200px;
      overflow-y: auto;

      .search-results {
        .no-data {
          text-align: center;
          padding: 24px;
          color: var(--color-text-3);
          font-size: 14px;
        }

        .search-list {
          .search-item {
            display: flex;
            align-items: center;
            padding: 4px 8px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            gap: 8px;
            border-radius: var(--border-radius-small);
            margin-bottom: 4px;

            &:hover {
              background-color: var(--color-bg-2);
            }

            &.selected {
              background-color: rgba(var(--primary-6), 0.1);
            }

            .item-icon {
              color: var(--color-text-3);
              font-size: 14px;
            }

            .item-name {
              flex: 1;
              font-size: 14px;
              color: var(--color-text-1);
            }

            .item-type {
              font-size: 12px;
              color: var(--color-text-3);
              padding: 2px 6px;
              background-color: var(--color-bg-2);
              border-radius: 4px;
            }
          }
        }
      }

      .tree-container {
        padding: 8px;

        .no-data {
          text-align: center;
          padding: 24px;
          color: var(--color-text-3);
          font-size: 14px;
        }

        .tree-node-title {
          display: flex;
          align-items: center;
          gap: 6px;
          cursor: pointer;
          padding: 2px 4px;
          border-radius: var(--border-radius-small);
          transition: background-color 0.2s ease;

          &:hover {
            background-color: var(--color-bg-2);
          }

          .node-icon {
            color: var(--color-text-3);
            font-size: 14px;
          }

          .node-name {
            font-size: 14px;
            color: var(--color-text-1);
          }
        }
      }
    }

    .footer-actions {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px;
      border-top: 1px solid var(--color-border-1);
      background-color: var(--color-bg-1);

      .selected-count {
        font-size: 12px;
        color: var(--color-text-3);
      }

      .action-buttons {
        display: flex;
        gap: 8px;
      }
    }
  }
}
