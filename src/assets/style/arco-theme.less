// arco组件样式重置
.arco-card {
  border: none;
  // border-radius: var(--border-radius-large);
  .arco-card-header {
    border-bottom: none;
    height: auto;
  }
}

.arco-radio-group-button {
  // border-radius: var(--border-radius-large);
  padding-top: 0.5px;
  padding-bottom: 0.5px;
  .arco-radio-button {
    // border-radius: var(--border-radius-large);
    margin: 1px 1.5px;
    &.arco-radio-checked {
      box-shadow: 1px 1px 5px var(--color-border);
    }
  }
}

// .arco-picker {
//   border-radius: var(--border-radius-large);
// }

.vertical-title {
  position: relative;
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  &::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 16px;
    background-color: rgb(var(--primary-6));
    margin-right: 5px;
    // border-radius: 25px;
  }
}

.arco-avatar {
  background-color: var(--color-fill-3);
}

.arco-table-container {
  border: none !important;
}

.arco-table-td {
  border-bottom: 1px solid var(--color-neutral-2);
}

.arco-table-border .arco-table-tr .arco-table-th {
  border-bottom: 1px solid var(--color-neutral-2);
  background: var(--color-neutral-2);
}

// .arco-scrollbar-container {
//   border-radius: var(--border-radius-large);
// }

// // 输入框圆角
// .arco-select-view-single {
//   border-radius: var(--border-radius-large);
// }
// // 输入框圆角
// .arco-input-wrapper {
//   border-radius: var(--border-radius-large);
// }
// .arco-input-group {
//   border-radius: var(--border-radius-large);
//   .arco-input-wrapper {
//     border-radius: inherit;
//   }
// }
// .arco-select-view {
//   border-radius: var(--border-radius-large) !important;
// }

// 弹窗样式
.arco-modal {
  // border-radius: var(--border-radius-large);
  .arco-modal-header {
    border-bottom: none;
  }
  .arco-modal-body {
    padding: 0 20px 20px;
  }
  .arco-modal-footer {
    border-bottom: none;
  }
}

// 按钮圆角
// .arco-btn {
//   border-radius: var(--border-radius-large);
// }

// .arco-input-group {
//   .arco-btn {
//     border-radius: inherit;
//   }
// }

// 分割线
.arco-divider-vertical {
  border-color: var(--color-neutral-1);
}
.arco-divider-horizontal {
  border-color: var(--color-neutral-1);
}

.no-padding-card {
  .arco-card-body {
    padding: 12px 8px;
  }
}
.no-top-padding-card {
  .arco-card-body {
    padding: 0 0 12px 12px;
  }
}
// list组件
.arco-list-item {
  border-bottom: none !important;
  padding: 10px 0 0 0 !important;
}

// tag组件
// .arco-tag {
//   border-radius: var(--border-radius-large);
// }

.arco-form-item {
  margin-bottom: 12px;
}

.primary_color {
  color: rgb(var(--primary-6));
}

.arco-image-img {
  object-fit: contain;
  // border-radius: var(--border-radius-large);
}

.arco-comment-inner-comment {
  margin-top: 5px;
}

.del-red-color {
  color: rgb(var(--danger-6));
}

// .arco-textarea-wrapper {
//   border-radius: var(--border-radius-large);
// }

// .arco-tree-node-title {
//   border-radius: var(--border-radius-large);
//   padding-left: 0;
//   padding-right: 2px;
// }

.no-padding-popover {
  .arco-popover-popup-content {
    padding: 5px 10px;
  }
}

.no-bottom-modal-body {
  .arco-modal-body {
    padding-bottom: 0;
  }
}
