<template>
  <div class="login-form-wrapper">
    <div class="login-logo-box">
      <img alt="logo" src="@/assets/images/logo-txt.png" style="height: 30px" />
    </div>
    <div class="login-form-error-msg">{{ errorMessage }}</div>
    <a-form
      ref="loginForm"
      :model="userInfo"
      class="login-form"
      layout="vertical"
      @submit="handleSubmit"
    >
      <a-form-item
        field="account_name"
        :rules="[{ required: true, message: $t('login.form.userName.errMsg') }]"
        :validate-trigger="['change', 'blur']"
        hide-label
      >
        <a-input
          v-model="userInfo.account_name"
          :placeholder="$t('login.form.userName.placeholder')"
          allow-clear
        >
          <template #prefix>
            <icon-user />
          </template>
        </a-input>
      </a-form-item>
      <a-form-item
        field="password"
        :rules="[{ required: true, message: $t('login.form.password.errMsg') }]"
        :validate-trigger="['change', 'blur']"
        hide-label
      >
        <a-input-password
          v-model="userInfo.password"
          :placeholder="$t('login.form.password.placeholder')"
          allow-clear
        >
          <template #prefix>
            <icon-lock />
          </template>
        </a-input-password>
      </a-form-item>
      <a-space :size="16" direction="vertical">
        <div class="login-form-password-actions">
          <a-checkbox
            checked="rememberPassword"
            :model-value="loginConfig.rememberPassword"
            @change="setRememberPassword"
          >
            {{ $t('login.form.rememberPassword') }}
          </a-checkbox>
          <a-link @click="forgetAction">{{
            $t('login.form.forgetPassword')
          }}</a-link>
        </div>
        <a-button type="primary" html-type="submit" long :loading="loading">
          {{ $t('login.form.login') }}
        </a-button>
        <!--<a-button type="text" long class="login-form-register-btn">
          {{ $t('login.form.register') }}
        </a-button>-->
        <a-button type="outline" long :loading="loading" @click="fsLogin">
          <template #icon>
            <icon-lark-color />
          </template>
          飞书登录
        </a-button>
      </a-space>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onBeforeMount } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { ValidatedError } from '@arco-design/web-vue/es/form/interface';
  import { useI18n } from 'vue-i18n';
  import { useStorage } from '@vueuse/core';
  import { useUserStore } from '@/store';
  import useLoading from '@/hooks/loading';
  import { DEFAULT_ROUTE_NAME } from '@/router/constants';
  import request from '@/api/request';

  const { VITE_ACCOUNT_KEY } = import.meta.env;
  const router = useRouter();
  const { t } = useI18n();
  const errorMessage = ref('');
  const { loading, setLoading } = useLoading();
  const userStore = useUserStore();

  const loginConfig = useStorage(VITE_ACCOUNT_KEY, {
    rememberPassword: true,
    account_name: '',
    password: '',
  });
  const userInfo = reactive({
    account_name: loginConfig.value.account_name,
    password: loginConfig.value.password,
  });

  const handleSubmit = async ({
    errors,
    values,
  }: {
    errors: Record<string, ValidatedError> | undefined;
    values: Record<string, any>;
  }) => {
    if (loading.value) return;
    if (!errors) {
      setLoading(true);
      try {
        await userStore.login(values);
        const { redirect, ...othersQuery } = router.currentRoute.value.query;
        router.push({
          name: (redirect as string) || DEFAULT_ROUTE_NAME,
          query: {
            ...othersQuery,
            super: undefined,
          },
        });
        Message.success(t('login.form.login.success'));
        const { rememberPassword } = loginConfig.value;
        const { account_name, password } = values;
        // 实际生产环境需要进行加密存储。
        loginConfig.value.account_name = rememberPassword ? account_name : '';
        loginConfig.value.password = rememberPassword ? password : '';
      } catch (err) {
        errorMessage.value = (err as Error).message;
      } finally {
        setLoading(false);
      }
    }
  };

  const setRememberPassword = (value: boolean) => {
    loginConfig.value.rememberPassword = value;
  };

  const forgetAction = () => {
    Message.warning('请联系管理员');
  };

  const fsLogin = () => {
    window.location.href = `https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=${encodeURIComponent(
      window.location.href
    )}&app_id=cli_a61c9be6f9f2900e`;
  };

  const route = useRoute();
  const loginAction = async () => {
    try {
      loading.value = true;
      await userStore.loginFs({ code: route.query.code });
      const { redirect, ...othersQuery } = router.currentRoute.value.query;
      delete othersQuery.code;
      delete othersQuery.state;
      router.push({
        name: (redirect as string) || DEFAULT_ROUTE_NAME,
        query: {
          ...othersQuery,
        },
      });
    } catch (err) {
      router.replace({
        name: 'login',
        query: {
          ...router.currentRoute.value.query,
          code: undefined,
          state: undefined,
        },
      });
      errorMessage.value = (err as Error).message;
    } finally {
      setLoading(false);
    }
  };

  onBeforeMount(() => {
    if (route.query.code) {
      loginAction();
    }
  });
</script>

<style lang="less" scoped>
  .login-form {
    &-wrapper {
      background-color: #fff;
      padding: 50px;
      width: 420px;
      border-radius: 12px;
      .login-logo-box {
        text-align: center;
      }
    }

    &-title {
      color: var(--color-text-1);
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;
      text-align: center;
    }

    &-sub-title {
      color: var(--color-text-3);
      font-size: 16px;
      line-height: 24px;
    }

    &-error-msg {
      height: 32px;
      color: rgb(var(--red-6));
      line-height: 32px;
    }

    &-password-actions {
      display: flex;
      justify-content: space-between;
    }

    &-register-btn {
      color: var(--color-text-3) !important;
    }
  }
  .arco-input-wrapper {
    padding-left: 12px;
  }
</style>
