<template>
  <div
    v-if="!showIpt"
    style="color: var(--color-neutral-10); font-weight: bold"
  >
    <span v-if="curData">
      <span> {{ record.format(curData?.value) }}&nbsp; </span>
      <span :style="{ color: color }">
        ({{ rateFormat(curData?.sequential_num) }})
      </span>
    </span>
    <span v-else>-</span>
    <icon-edit
      v-if="[201, 202].includes(record.type)"
      class="a-text ml-5"
      @click="
        editVal = curData?.value;
        showIpt = !showIpt;
      "
    />
  </div>
  <a-input-group v-else>
    <a-input-number v-model="editVal" class="w-100" hide-button>
      <template #suffix>%</template>
    </a-input-number>
    <a-button type="primary" status="danger" @click="showIpt = !showIpt">
      <template #icon><icon-close /></template>
    </a-button>
    <a-button type="primary" :loading="loading" @click="sendInfo">
      <template #icon><icon-check /></template>
    </a-button>
  </a-input-group>
</template>

<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import { getPathValue } from '@/utils/util';
  import { rateFormat } from '@/utils/table-utils/table-util';
  import { colors } from '@/components/dict-select/dict-common';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';

  const emit = defineEmits(['refresh']);

  const props = defineProps({
    record: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
    column: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
    data: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
  });

  const curData = computed(() =>
    getPathValue(props.data, [props.column.dataIndex, 'data'])?.find(
      (item: any) => item.type === props.record.type
    )
  );
  const showIpt = ref(false);
  const loading = ref(false);
  const editVal = ref(null);

  function sendInfo() {
    if (!editVal.value) {
      return Message.error('请填写');
    }
    loading.value = true;
    request('/api/report/statThreadWeekUpdate', {
      day: props.column.dataIndex,
      type: props.record.type,
      value: editVal.value,
    })
      .then(() => {
        showIpt.value = false;
        Message.success('修改成功');
        emit('refresh');
      })
      .finally(() => {
        loading.value = false;
      });
  }
  const color = computed(() => {
    let val = curData.value?.sequential_num;
    if (!val) {
      return 'var(--color-neutral-6)';
    }
    if (val > 0) {
      return 'rgb(var(--red-6))';
    }
    return 'rgb(var(--green-6))';
  });
</script>

<style scoped lang="less"></style>
