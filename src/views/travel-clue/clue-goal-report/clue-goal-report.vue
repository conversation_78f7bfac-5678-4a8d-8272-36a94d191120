<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="handleSubmit()"
      >
        <template #formItemGroup>
          <a-form-item label="日期">
            <a-range-picker
              v-model="formModel.date"
              mode="week"
              :day-start-of-week="5"
              :allow-clear="false"
              @change="handleSubmit()"
            />
          </a-form-item>
        </template>
      </search-form-fold>
      <div class="table-card-header mt-10">
        <a-space> </a-space>
        <a-space>
          <a-button type="primary" :loading="exporting" @click="exportAction">
            <template #icon>
              <icon-export />
            </template>
            导出
          </a-button>
        </a-space>
      </div>
      <a-table
        :loading="loading"
        :columns="columns"
        :data="list"
        :pagination="false"
        :bordered="{ wrapper: true, cell: true }"
        :span-method="dataSpanMethod"
      >
        <template #dataSlot="{ record, column }: TableColumnSlot">
          <clue-goal-report-cell
            :record="record"
            :column="column"
            :data="data"
            @refresh="getList"
          />
        </template>
        <template #field="{ record }: TableColumnSlot">
          <a-tooltip v-if="record.tooltip" :content="record.tooltip">
            <span :style="{ cursor: 'pointer', ...record.style }">
              {{ record.field }}
              <icon-question-circle class="a-text" />
            </span>
          </a-tooltip>
          <span v-else :style="record.style">{{ record.field }}</span>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import dayjs from 'dayjs';
  import { TableColumnSlot } from '@/global';
  import {
    moneyFormat,
    numberFormat,
    rateFormat,
  } from '@/utils/table-utils/table-util';
  import ClueGoalReportCell from '@/views/travel-clue/clue-goal-report/clue-goal-report-cell.vue';

  window.dayjs = dayjs;
  const generateFormModel = () => {
    return {
      date: [
        dayjs()
          .day(dayjs().day(-2).diff(dayjs(), 'day') < 7 ? -9 : -2)
          .format('YYYY-MM-DD'),
        dayjs().endOf('w').add(-2, 'day').format('YYYY-MM-DD'),
      ],
    };
  };
  const loading = ref(false);
  const list = ref([
    {
      user: '关键词配置（李娜）',
      field: '评论爬取次数',
      type: 101,
      format: numberFormat,
    },
    {
      user: '关键词配置（李娜）',
      field: '意向用户数',
      type: 102,
      format: numberFormat,
      tooltip: '有意向用户数+可能有意向用户数',
    },
    {
      user: '关键词配置（李娜）',
      field: '关键词意向度',
      type: 103,
      format: rateFormat,
      tooltip: '关键词意向度=意向用户数/评论爬取次数',
      style: { 'text-decoration': 'underline' },
    },
    {
      user: 'AI（葛攀）',
      field: 'AI视频理解准确度（人工抽检）',
      type: 201,
      format: rateFormat,
      style: { 'text-decoration': 'underline' },
    },
    {
      user: 'AI（葛攀）',
      field: 'AI意向度分析准确度（人工抽检）',
      type: 202,
      format: rateFormat,
      style: { 'text-decoration': 'underline' },
    },
    {
      user: '爬取（陈鑫）',
      field: '平均评论至入库时间（h)',
      type: 302,
      format: moneyFormat,
      tooltip: '评论入库时间至用户评论时间',
      style: { 'text-decoration': 'underline' },
    },
    {
      user: '爬取（陈鑫）',
      field: '爬取评论数',
      type: 301,
      format: numberFormat,
      style: { 'text-decoration': 'underline' },
    },
    {
      user: '群控（冉攀攀）',
      field: '发送私信数',
      type: 401,
      format: numberFormat,
      style: { 'text-decoration': 'underline' },
    },
    {
      user: '群控（冉攀攀）',
      field: '发送成功数',
      type: 402,
      format: numberFormat,
    },
    {
      user: '群控（冉攀攀）',
      field: '发送成功率',
      type: 403,
      format: rateFormat,
      tooltip: '发送成功率=发送成功数/发送私信数',
      style: { 'text-decoration': 'underline' },
    },
    {
      user: '群控（冉攀攀）',
      field: '平均入库至发送时间（h)',
      type: 404,
      format: moneyFormat,
      tooltip: '评论入库时间至成功发送私信的时间',
      style: { 'text-decoration': 'underline' },
    },
    {
      user: '线索转化（李娜）',
      field: '一级线索数',
      type: 501,
      format: numberFormat,
    },
    {
      user: '线索转化（李娜）',
      field: '一级线索率',
      type: 502,
      format: rateFormat,
      tooltip: '一级线索率 = 一级线索数/发送成功数',
      style: { 'text-decoration': 'underline' },
    },
    {
      user: '线索转化（李娜）',
      field: '二级线索数',
      type: 503,
      format: numberFormat,
      tooltip: '只统计从公海流转的二级线索，其他来源类型的线索不统计',
    },
    {
      user: '线索转化（李娜）',
      field: '二级线索率',
      type: 504,
      format: rateFormat,
      tooltip: '二级线索率 = 二级线索数/一级线索数',
      style: { 'text-decoration': 'underline' },
    },
    {
      user: '转单（姜龙龙）',
      field: '订单数',
      type: 601,
      format: numberFormat,
      tooltip:
        '只统计从公海流转的二级线索成交订单，其他来源类型的订单不统计,筛选时间范围内的线索转化的订单',
    },
    {
      user: '转单（姜龙龙）',
      field: '订单数(订单时间)',
      type: 603,
      format: numberFormat,
      tooltip:
        '只统计从公海流转的二级线索成交订单，其他来源类型的订单不统计,筛选时间范围内产生的订单，包括历史的线索',
    },
    {
      user: '转单（姜龙龙）',
      field: '转单率',
      type: 602,
      format: rateFormat,
      tooltip: '转单率 = 订单数/二级线索数',
      style: { 'text-decoration': 'underline' },
    },
    {
      user: '装修（曲桂香）',
      field: '视频播放次数',
      type: 701,
      format: numberFormat,
    },
    {
      user: '装修（曲桂香）',
      field: '视频查看率',
      type: 702,
      format: rateFormat,
      tooltip: '视频查看率 = 视频播放次数/一级线索数',
      style: { 'text-decoration': 'underline' },
    },
  ]);
  list.value.forEach((item, index) => {
    let target = list.value.findIndex((item2) => item2.user === item.user);
    if (target === index) {
      item.rowspan = list.value.filter(
        (item2) => item2.user === item.user
      ).length;
    } else {
      item.rowspan = 0;
    }
  });

  const dataSpanMethod = ({ record, column }: any) => {
    if (column.dataIndex === 'user') {
      return {
        rowspan: record.rowspan,
      };
    }
  };
  const data = ref([]);
  const formModel = reactive(generateFormModel());

  const tableParams = computed(() => {
    return {
      ...formModel,
      start_date: formModel.date[0],
      end_date: formModel.date[1],
    };
  });

  const columnsConfig = ref<any[]>([]);

  const columns = computed(() => [
    {
      title: '模块',
      dataIndex: 'user',
      align: 'center',
    },
    {
      title: '指标',
      dataIndex: 'field',
      slotName: 'field',
      align: 'center',
    },
    ...columnsConfig.value,
  ]);

  const getList = async () => {
    loading.value = true;
    request('/api/report/statThreadWeek', {
      ...tableParams.value,
    })
      .then((res) => {
        columnsConfig.value = Object.keys(res.data).reduce(
          (sum: any[], key) => {
            sum.push(
              {
                title: `${res.data[key].start_week} ~ ${res.data[key].end_week}`,
                dataIndex: key,
                key: `${key}_value`,
                dataType: 'value',
                slotName: 'dataSlot',
                align: 'center',
              }
              // {
              //  title: `环比`,
              //  dataIndex: key,
              //  key: `${key}_sequential_num`,
              //  dataType: 'sequential_num',
              //  slotName: 'dataSlot',
              //  align: 'center',
              // }
            );
            return sum;
          },
          []
        );
        data.value = res.data || {};
      })
      .finally(() => {
        loading.value = false;
      });
  };
  getList();

  const exporting = ref(false);
  function exportAction() {
    exporting.value = true;
    request('/api/report/statThreadWeek', {
      ...tableParams.value,
      export: true,
      export_now: true,
    }).finally(() => {
      exporting.value = false;
    });
  }

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    getList();
  };
</script>

<style scoped lang="less"></style>
