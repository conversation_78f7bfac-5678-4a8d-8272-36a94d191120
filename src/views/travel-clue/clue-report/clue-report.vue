<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="handleSubmit()"
      >
        <template #formItemGroup>
          <a-form-item label="日期">
            <a-input-group class="w100p">
              <c-range-picker
                v-model="formModel.date"
                class="w100p"
                :allow-clear="false"
              />
              <a-checkbox
                :model-value="formModel.group_by.includes('accept_date')"
                @change="changeGroupFields('accept_date')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="来源媒体">
            <a-input-group class="w100p">
              <dict-select
                v-model="formModel.platform"
                :data-list="contactWayListM"
              />
              <a-checkbox
                :model-value="formModel.group_by.includes('platform')"
                @change="changeGroupFields('platform')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="来源类型">
            <a-input-group class="w100p">
              <dict-select
                v-model="formModel.source"
                :data-list="clueSuorceM"
                :max-tag-count="2"
              />
              <a-checkbox
                :model-value="formModel.group_by.includes('source')"
                @change="changeGroupFields('source')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="旅游线路">
            <a-input-group class="w100p">
              <dict-select
                v-model="formModel.area"
                :data-list="dataCacheStore.lineList"
                label-key="line_name"
                value-key="line_name"
              />
              <a-checkbox
                :model-value="formModel.group_by.includes('area')"
                @change="changeGroupFields('area')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="分组">
            <a-input-group class="w100p">
              <a-input
                v-model="formModel.keyword_group_name"
                allow-clear
                placeholder="ID或名称"
              />
              <a-checkbox
                :model-value="formModel.group_by.includes('keyword_group_id')"
                @change="changeGroupFields('keyword_group_id')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="关键词">
            <a-input-group class="w100p">
              <a-input
                v-model="formModel.keyword"
                allow-clear
                placeholder="ID或名称"
              />
              <a-checkbox
                :model-value="formModel.group_by.includes('keyword')"
                @change="changeGroupFields('keyword')"
              />
            </a-input-group>
          </a-form-item>

          <a-form-item label="线索类型">
            <a-input-group class="w100p">
              <dict-select
                v-model="formModel.clue_type"
                :data-list="clueTypeListM"
                @change="formModel.clue_type_detail = []"
              />
              <a-checkbox
                :model-value="formModel.group_by.includes('clue_type')"
                @change="changeGroupFields('clue_type')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="线索细分类型">
            <a-input-group class="w100p">
              <dict-select
                v-if="formModel.clue_type === '公海线索'"
                v-model="formModel.clue_type_detail"
                :disabled="!formModel.clue_type"
                :data-list="clueSeasTypeM"
                :max-tag-count="2"
              />
              <dict-select
                v-else-if="formModel.clue_type === '其他线索'"
                v-model="formModel.clue_type_detail"
                :disabled="!formModel.clue_type"
                :data-list="clueDetailTypeM"
                :max-tag-count="2"
              />
              <request-select
                v-else-if="formModel.clue_type === '内容号线索'"
                v-model="formModel.clue_type_detail"
                :disabled="!formModel.clue_type"
                request-url="/api/contentAccount/list"
                label-key="account_name"
                :max-tag-count="1"
              />
              <request-select
                v-else-if="formModel.clue_type === '店铺线索'"
                v-model="formModel.clue_type_detail"
                :disabled="!formModel.clue_type"
                request-url="/api/store/list"
                label-key="store_name"
                :max-tag-count="1"
              />
              <request-select
                v-else
                v-model="formModel.clue_type_detail"
                :disabled="!formModel.clue_type"
                api="live_room"
                :max-tag-count="1"
              />
              <a-checkbox
                :model-value="formModel.group_by.includes('clue_type_detail')"
                @change="changeGroupFields('clue_type_detail')"
              />
            </a-input-group>
          </a-form-item>
        </template>
      </search-form-fold>
      <div class="table-card-header mt-20">
        <a-space>
          <span>
            <icon-exclamation-circle />
            数据更新时间：每天06点00分
          </span>
          <template v-if="isKeyword">
            <span class="title-box">
              已选择
              <a-tag>
                {{ rowSelection.selectedRowKeys.length }}
              </a-tag>
              个关键词
            </span>
            <icon-close-circle class="cur-por" @click="resetHandler" />
            <a-popconfirm
              :content="`该操作会删除所有分组下的关键词，确认删除这${rowSelection.selectedRows.length}个关键词吗？`"
              @ok="delAction()"
            >
              <a-button
                :disabled="!rowSelection.selectedRows.length"
                type="primary"
                status="danger"
                :loading="sending"
              >
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-space>
        <a-space>
          <columns-select
            :the-field-map="allFieldsConfig"
            :need-format-column="false"
            :send-params="{ type: 'clue-report' }"
            :default-columns="columnsConfig.map((item:any) => item.dataIndex)"
            @select-columns-ok="setColumns"
          ></columns-select>
          <a-button type="primary" @click="exportAction">
            <template #icon>
              <icon-export />
            </template>
            导出
          </a-button>
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :row-selection="isKeyword ? rowSelection : undefined"
        :row-key="isKeyword ? 'keyword' : 'id'"
        :sort-keys="sortKeys"
        :send-params="tableParams"
        :scroll-percent="scrollPercent"
        @select-change="selectionChange"
      >
      </base-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive, computed } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import { TableColumnSlot } from '@/global';
  import ColumnsSelect from '@/components/columns-select/columns-select.vue';
  import { cloneDeep } from 'lodash';
  import dayjs from 'dayjs';
  import { useDataCacheStore } from '@/store';
  import { contactWayListM } from '@/components/dict-select/dict-travel';
  import {
    clueDetailTypeM,
    clueSeasTypeM,
    clueSuorceM,
    clueTypeListM,
    isFlowM,
  } from '@/components/dict-select/dict-clue';
  import {
    moneyFormatShow,
    numberFormatShow,
    rateFormatShow,
  } from '@/utils/table-utils/columns-config';
  import RequestSelect from '@/components/select/request-select.vue';

  const dataCacheStore = useDataCacheStore();
  const generateFormModel = () => {
    return {
      date: [
        dayjs().add(-6, 'day').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      group_by: ['accept_date'] as string[], // 聚合维度 accept_date：日期 platform：来源媒体 source：来源类型 area：旅游路线 keyword_group_id：分组
      platform: [],
      source: [],
      area: [],
      keyword_group_name: '',
      keyword: '',
      clue_type: '',
      clue_type_detail: [],
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const editRef = ref();
  const formModel = reactive(generateFormModel());
  const isKeyword = computed(
    () =>
      formModel.group_by.length === 1 && formModel.group_by.includes('keyword')
  );

  const getList = async (data: any) => {
    return request('/api/report/threadReport', {
      ...data,
    });
  };

  const allFieldsConfig = [
    {
      title: '全部字段',
      dataIndex: '全部字段',
      keys: [] as string[],
      dataList: [
        {
          title: '评论爬取次数',
          dataIndex: 'spider_num',
          render: numberFormatShow(),
        },
        {
          title: '爬取评论数',
          dataIndex: 'thread_num',
          render: numberFormatShow(),
        },
        {
          title: '意向用户数',
          dataIndex: 'account_num',
          render: numberFormatShow(),
        },
        {
          title: '关键词意向度',
          dataIndex: 'keyword_rate',
          description: '关键词意向度=高意向评论用户数/评论爬取次数*100%',
          render: rateFormatShow(),
        },
        {
          title: '线索公海数量',
          dataIndex: 'thread_seas_num',
          render: numberFormatShow(),
        },
        {
          title: '发送私信数',
          dataIndex: 'thread_send_msg_num',
          render: numberFormatShow(),
        },
        {
          title: '发送成功数',
          dataIndex: 'thread_send_success_num',
          render: numberFormatShow(),
        },
        {
          title: '发送成功率',
          dataIndex: 'thread_send_success_num_rate',
          render: rateFormatShow(),
        },
        {
          title: '一级线索数',
          dataIndex: 'thread_sale_one_num',
        },
        {
          title: '一级线索率',
          dataIndex: 'thread_sale_one_num_rate',
          render: rateFormatShow(),
        },
        {
          title: '二级线索数',
          dataIndex: 'thread_sale_two_num',
        },
        {
          title: '二级线索率',
          dataIndex: 'thread_sale_two_num_rate',
          render: rateFormatShow(),
        },
        {
          title: '二级线索添加微信数',
          dataIndex: 'thread_sale_add_wechat_num',
        },
        {
          title: '二级线索介绍产品及公司',
          dataIndex: 'thread_sale_add_introduce_product_num',
        },
        {
          title: '订单数量',
          dataIndex: 'order_num',
        },
        {
          title: '添加微信订单数',
          dataIndex: 'order_add_wechat_num',
        },
        {
          title: '已发确认件订单数',
          dataIndex: 'order_ensure_num',
        },
        {
          title: '已发确认件订单订金',
          dataIndex: 'order_ensure_advance_price',
          render: moneyFormatShow(),
        },
        {
          title: '已发确认件订单总团款',
          dataIndex: 'order_ensure_amount',
          render: moneyFormatShow(),
        },
        {
          title: '出行订单数',
          dataIndex: 'order_travel_num',
        },
        {
          title: '核销订单数',
          dataIndex: 'order_travel_back_num',
        },
        {
          title: '核销订单总团款',
          dataIndex: 'order_travel_back_amount',
          render: moneyFormatShow(),
        },
        {
          title: '均值客单价',
          dataIndex: 'order_travel_back_avg_amount',
          description: '均值客单价=核销GMV/核销订单数',
          render: moneyFormatShow(),
        },
        {
          title: '核销利润',
          dataIndex: 'order_travel_back_profit',
          render: moneyFormatShow(),
        },
        {
          title: '核销订单毛利率',
          dataIndex: 'order_travel_back_profit_rate',
        },
        {
          title: '订单核销总人数',
          dataIndex: 'order_travel_back_people_num',
        },
        {
          title: '核销订单均单人次',
          dataIndex: 'order_travel_back_people_avg',
          description: '核销订单均单人次=核销总人次/核销订单数',
        },
        {
          title: '核销订单成人总人数',
          dataIndex: 'order_travel_back_adult_num',
        },
      ],
    },
  ];
  allFieldsConfig.forEach((item) => {
    item.keys = item.dataList.map((citem) => {
      citem.key = citem.dataIndex;
      return citem.dataIndex;
    });
  });
  const columnsConfig = ref(cloneDeep(allFieldsConfig[0].dataList));
  const sortKeys = allFieldsConfig[0].dataList.map((item) => item.dataIndex);

  const columns = computed(() => [
    ...(formModel.group_by.includes('accept_date')
      ? [
          {
            title: '日期',
            dataIndex: 'accept_date',
            fixed: 'left',
            width: 110,
          },
        ]
      : []),
    ...(formModel.group_by.includes('platform')
      ? [
          {
            title: '来源媒体',
            dataIndex: 'platform',
            width: 160,
          },
        ]
      : []),
    ...(formModel.group_by.includes('source')
      ? [
          {
            title: '来源类型',
            dataIndex: 'source',
          },
        ]
      : []),
    ...(formModel.group_by.includes('area')
      ? [
          {
            title: '旅游线路',
            dataIndex: 'area',
            width: 200,
          },
        ]
      : []),
    ...(formModel.group_by.includes('keyword_group_id')
      ? [
          {
            title: '分组',
            dataIndex: 'keyword_group_name',
          },
        ]
      : []),
    ...(formModel.group_by.includes('keyword')
      ? [
          {
            title: '关键词',
            dataIndex: 'keyword',
          },
        ]
      : []),
    ...(formModel.group_by.includes('clue_type')
      ? [
          {
            title: '线索类型',
            dataIndex: 'clue_type',
          },
        ]
      : []),
    ...(formModel.group_by.includes('clue_type_detail')
      ? [
          {
            title: '线索细分类型',
            dataIndex: 'clue_type_detail',
          },
        ]
      : []),
    ...columnsConfig.value,
  ]);

  // 数据选择相关
  let rowSelection = reactive({
    type: 'checkbox',
    fixed: true,
    width: 30,
    onlyCurrent: true,
    showCheckedAll: true,
    selectedRowKeys: [] as any[],
    selectedRows: [] as any[],
  });
  const selectionChange = (selectedRowKeys: any[], selectedRows: any[]) => {
    rowSelection.selectedRowKeys = selectedRowKeys.filter((item: any) => item);
    rowSelection.selectedRows = selectedRows;
  };

  // 重置
  const resetHandler = () => {
    rowSelection.selectedRowKeys = [];
    rowSelection.selectedRows = [];
  };

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    resetHandler();
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  const sending = ref(false);
  function delAction() {
    sending.value = true;
    request('/api/thread/delKeywords', {
      del_keywords: rowSelection.selectedRowKeys,
    })
      .then(() => {
        handleSubmit();
      })
      .finally(() => {
        sending.value = false;
      });
  }

  function changeGroupFields(key: string) {
    if (!formModel.group_by.includes(key)) {
      formModel.group_by.push(key);
    } else {
      formModel.group_by = formModel.group_by.filter((item) => item !== key);
    }
    if (!formModel.group_by.length) {
      formModel.group_by = ['accept_date'];
    }
    handleSubmit();
  }

  function setColumns(val: any[]) {
    columnsConfig.value = val.filter((item) =>
      allFieldsConfig.some((citem) => citem.keys.includes(item.dataIndex))
    );
    handleSubmit();
  }

  const tableParams = computed(() => {
    return {
      ...formModel,
      group_by: !formModel.group_by.length
        ? ['accept_date']
        : formModel.group_by,
    };
  });
  const scrollPercent = computed(() => ({
    maxHeight: '70vh',
    x: columns.value.length * 160,
  }));

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less"></style>
