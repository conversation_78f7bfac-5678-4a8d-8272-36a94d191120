<template>
  <div>
    <a-card v-permission-enhanced="{ roles: ['*'], allowOutUser: true }">
      <clue-type-radio
        ref="typeRadioRef"
        v-model="formModel.tab_type"
        @change="handleSubmit()"
      />
    </a-card>
    <a-card size="small" class="table-card">
      <search-form-custom
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        placeholder="请输入关键词"
        :search-rules="searchRules"
        type="clue_two_order_filter"
        :base-search-rules="baseSearchRules"
        @hand-submit="handleSubmit"
      ></search-form-custom>

      <div class="table-card-header mt-10">
        <a-space>
          <!-- 批量操作区域 -->
          <template v-if="rowSelection.selectedRowKeys.length > 0">
            <span class="title-box">
              已选择
              <a-tag>
                {{ rowSelection.selectedRowKeys.length }}
              </a-tag>
              条线索
            </span>
            <icon-close-circle class="cur-por" @click="clearSelection" />
            <a-button
              v-permission-enhanced="{ roles: ['*'], allowOutUser: false }"
              type="outline"
              :disabled="!rowSelection.selectedRows.length"
              :loading="batchServiceLoading"
              @click="showBatchServiceModal"
            >
              <template #icon>
                <icon-user />
              </template>
              批量设置客服
            </a-button>
          </template>
        </a-space>
        <a-space>
          <a-button
            v-permission-enhanced="{ roles: ['*'], allowOutUser: false }"
            type="primary"
            @click="addRef?.show()"
          >
            <template #icon>
              <icon-plus />
            </template>
            新增二级线索
          </a-button>
          <columns-select
            v-permission-enhanced="{ roles: ['*'], allowOutUser: true }"
            :the-field-map="filteredFieldsConfig"
            :need-format-column="false"
            :send-params="{ type: 'travel_sale_clue_two' }"
            :default-columns="columnsConfig.map((item:any) => item.dataIndex)"
            @select-columns-ok="setColumns"
          ></columns-select>
          <a-button
            v-permission-enhanced="{ roles: ['*'], allowOutUser: false }"
            @click="theTable?.exportTable({})"
          >
            <template #icon>
              <icon-export />
            </template>
            导出
          </a-button>
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :send-params="tableParams"
        :scroll-percent="scrollPercent"
        :data-handle="dataHandle"
        :row-selection="rowSelection"
        @select-change="selectionChange"
      >
        <!-- 线索ID -->
        <template #id="{ record }: TableColumnSlot">
          <!-- 所有用户显示ID和状态 -->
          <div class="order-number-container">
            <div class="order-number">
              <span v-if="record.id" class="order-number-text">
                {{ record.id }}
              </span>
              <span v-else class="order-number-empty">-</span>
            </div>
            <div class="order-status">
              <a-space direction="vertical" style="white-space: pre-line">
                <a-tag
                  size="small"
                  :color="getColor(clueTwoStatusM, record.status_two)"
                >
                  {{ getText(clueTwoStatusM, record.status_two) }}
                </a-tag>
                <template v-if="record.status_two === '作废'">
                  <a-tooltip
                    v-if="record.cancellation_reason?.length > 50"
                    :content="record.cancellation_reason.slice(0, 400)"
                  >
                    <span class="cancellation-reason-text"
                      >{{ record.cancellation_reason.slice(0, 50) }}...</span
                    >
                  </a-tooltip>
                  <span v-else class="cancellation-reason-text">{{
                    record.cancellation_reason
                  }}</span>
                </template>
              </a-space>
            </div>
          </div>
        </template>
        <!-- 订单状态标签 -->
        <!-- phone -->
        <template #phone="{ record }: TableColumnSlot">
          <div class="phone-container">
            <!-- 使用 TableInlineEdit 组件替换原有的显示 -->
            <div class="phone-edit-wrapper">
              <TableInlineEdit
                v-model="record.phone"
                placeholder="点击编辑手机号"
                :max-lines="1"
                :max-length="11"
                :show-copy="true"
                edit-mode="click"
                save-mode="manual"
                type="input"
                :min-rows="1"
                :max-rows="2"
                :show-word-limit="false"
                :on-save="(value) => handlePhoneSave(record, value)"
                :disabled="record.phoneLoading"
              />
            </div>

            <!-- 优化：电话接通状态指示器 -->
            <div v-if="record.phone" class="phone-status-wrapper">
              <a-popover
                :popup-visible="record.phonePopoverVisible"
                trigger="click"
                position="right"
                :content-style="{ padding: '12px', minWidth: '280px' }"
              >
                <template #content>
                  <div class="phone-status-popover">
                    <div class="status-section">
                      <div class="section-title">接通状态</div>
                      <!-- 优化：改为横向布局的状态选择 -->
                      <a-radio-group
                        v-model="record.tempPhoneStatus"
                        direction="horizontal"
                        size="small"
                        class="horizontal-radio-group"
                      >
                        <a-radio :value="1" class="status-radio connected">
                          <icon-check />
                          已接通
                        </a-radio>
                        <a-radio :value="2" class="status-radio not-connected">
                          <icon-close />
                          未接通
                        </a-radio>
                        <a-radio :value="0" class="status-radio default">
                          <icon-minus />
                          未标记
                        </a-radio>
                      </a-radio-group>
                    </div>

                    <!-- 新增：最后更新时间显示 -->
                    <div class="edit-time-section">
                      <div class="section-title">最后更新时间</div>
                      <div class="edit-time-display">
                        {{
                          record.phone_connect_edit_time
                            ? formatEditTime(record.phone_connect_edit_time)
                            : '未设置'
                        }}
                      </div>
                    </div>

                    <div class="remark-section">
                      <div class="section-title">备注信息</div>
                      <a-textarea
                        v-model="record.tempPhoneRemark"
                        placeholder="请输入备注信息..."
                        :auto-size="{ minRows: 2, maxRows: 4 }"
                        :max-length="200"
                        show-word-limit
                      />
                    </div>

                    <div class="action-section">
                      <a-space>
                        <a-checkbox v-model="record.is_defer_contract">
                          暂缓联系
                        </a-checkbox>
                      </a-space>

                      <a-space>
                        <a-button
                          size="small"
                          @click="cancelPhoneStatusEdit(record)"
                        >
                          取消
                        </a-button>
                        <a-button
                          type="primary"
                          size="small"
                          :loading="record.phoneStatusLoading"
                          @click="savePhoneStatus(record)"
                        >
                          确认
                        </a-button>
                      </a-space>
                    </div>
                  </div>
                </template>
                <a-tooltip
                  :content="getPhoneStatusTooltip(record)"
                  position="top"
                >
                  <div class="phone-status-wrapper">
                    <div
                      class="phone-status-indicator"
                      :class="getPhoneStatusClass(record.phone_is_connect)"
                    >
                      <icon-check v-if="record.phone_is_connect === 1" />
                      <icon-close v-else-if="record.phone_is_connect === 2" />
                      <icon-minus v-else />
                    </div>
                  </div>
                </a-tooltip>
              </a-popover>
              <!-- 新增：状态文字外显 -->
              <a-tag
                class="cur-por"
                size="small"
                :color="getPhoneStatusTagColor(record.phone_is_connect)"
                @click="openPhoneStatusPopover(record)"
              >
                {{ getPhoneStatusText(record.phone_is_connect) }}
                <!-- 是否有备注标志 -->
                <icon-message v-if="record.phone_connect_remark" />
              </a-tag>
            </div>
          </div>
        </template>
        <template #wechat="{ record }: TableColumnSlot">
          <div class="wechat-container">
            <div class="wechat-content">
              <!-- 使用 TableInlineEdit 组件替换原有的微信号显示 -->
              <div class="wechat-edit-wrapper">
                <TableInlineEdit
                  v-model="record.wechat"
                  placeholder="点击编辑微信号"
                  :max-lines="1"
                  :max-length="50"
                  :show-copy="true"
                  edit-mode="click"
                  save-mode="manual"
                  type="input"
                  :min-rows="1"
                  :max-rows="2"
                  :show-word-limit="false"
                  :on-save="(value) => handleWechatSave(record, value)"
                  :disabled="record.wechatLoading"
                />
              </div>

              <!-- 保留微信二维码显示 -->
              <a-image
                v-if="record.wechat_qrcode"
                :src="record.wechat_qrcode"
                width="50px"
                height="50px"
                style="margin-top: 4px"
              />
            </div>

            <!-- 新增：微信添加状态指示器 -->
            <div v-if="record.wechat" class="wechat-status-wrapper">
              <a-popover
                :popup-visible="record.wechatPopoverVisible"
                trigger="click"
                position="right"
                :content-style="{ padding: '12px', minWidth: '280px' }"
              >
                <template #content>
                  <div class="wechat-status-popover">
                    <div class="status-section">
                      <div class="section-title">添加状态</div>
                      <!-- 横向布局的状态选择 -->
                      <a-radio-group
                        v-model="record.tempWechatStatus"
                        direction="horizontal"
                        size="small"
                        class="horizontal-radio-group"
                      >
                        <a-radio :value="1" class="status-radio connected">
                          <icon-check />
                          已添加
                        </a-radio>
                        <a-radio :value="2" class="status-radio not-connected">
                          <icon-close />
                          未添加
                        </a-radio>
                        <a-radio :value="0" class="status-radio default">
                          <icon-minus />
                          未标记
                        </a-radio>
                      </a-radio-group>
                    </div>

                    <!-- 最后修改时间显示 -->
                    <div class="edit-time-section">
                      <div class="section-title">最后更新时间</div>
                      <div class="edit-time-display">
                        {{
                          record.wechat_add_edit_time
                            ? formatEditTime(record.wechat_add_edit_time)
                            : '未设置'
                        }}
                      </div>
                    </div>

                    <div class="remark-section">
                      <div class="section-title">备注信息</div>
                      <a-textarea
                        v-model="record.tempWechatRemark"
                        placeholder="请输入备注信息..."
                        :auto-size="{ minRows: 2, maxRows: 4 }"
                        :max-length="200"
                        show-word-limit
                      />
                    </div>

                    <div class="action-section">
                      <a-space>
                        <a-checkbox v-model="record.is_defer_contract">
                          暂缓联系
                        </a-checkbox>
                      </a-space>
                      <a-space>
                        <a-button
                          size="small"
                          @click="cancelWechatStatusEdit(record)"
                        >
                          取消
                        </a-button>
                        <a-button
                          type="primary"
                          size="small"
                          :loading="record.wechatStatusLoading"
                          @click="saveWechatStatus(record)"
                        >
                          确认
                        </a-button>
                      </a-space>
                    </div>
                  </div>
                </template>

                <a-tooltip
                  :content="getWechatStatusTooltip(record)"
                  position="top"
                >
                  <div class="wechat-status-wrapper">
                    <div
                      class="wechat-status-indicator"
                      :class="getWechatStatusClass(record.wechat_is_add)"
                    >
                      <icon-check v-if="record.wechat_is_add === 1" />
                      <icon-close v-else-if="record.wechat_is_add === 2" />
                      <icon-minus v-else />
                    </div>
                  </div>
                </a-tooltip>
              </a-popover>
              <!-- 状态文字外显 -->
              <a-tag
                size="small"
                class="cur-por"
                :color="getWechatStatusTagColor(record.wechat_is_add)"
                @click="openWechatStatusPopover(record)"
              >
                {{ getWechatStatusText(record.wechat_is_add) }}
                <!-- 是否有备注标志 -->
                <icon-message v-if="record.wechat_add_remark" />
              </a-tag>
            </div>
          </div>
        </template>
        <template #action="{ record }: TableColumnSlot">
          <!-- 外部用户显示受理和作废按钮 -->
          <div v-if="isExternalUser" class="action-buttons-heng">
            <!-- 受理按钮 -->
            <a-link
              v-if="record.status_two === '已分配'"
              type="text"
              class="link-text"
              size="mini"
              :loading="record.loading"
              @click="shouliAction(record)"
            >
              <template #icon>
                <icon-check />
              </template>
              受理
            </a-link>

            <!-- 作废按钮 -->
            <a-link
              v-if="record.status_two !== '作废'"
              type="text"
              status="danger"
              size="small"
              class="link-text"
              @click="cancelRef?.show(record)"
            >
              <template #icon>
                <icon-close />
              </template>
              作废
            </a-link>

            <!-- 显示占位符-->
            <span v-if="record.status_two === '作废'" class="divider">-</span>
          </div>
          <!-- 内部用户显示所有操作按钮 -->
          <div v-else class="action-buttons-container">
            <!-- 响应式按钮布局 -->
            <div class="action-buttons-wrapper">
              <!-- 主要操作按钮（最多显示2个，按优先级排序） -->
              <template
                v-for="action in getMainActions(record)"
                :key="action.key"
              >
                <a-button
                  :type="action.type || 'text'"
                  :status="action.status"
                  size="mini"
                  @click="action.handler"
                >
                  <template v-if="action.icon" #icon>
                    <component :is="action.icon" />
                  </template>
                  {{ action.label }}
                </a-button>
              </template>

              <!-- 更多操作下拉菜单（只有当操作超过2个时才显示） -->
              <a-dropdown
                v-if="getDropdownActions(record).length > 0"
                v-model:popup-visible="record.dropdownVisible"
                trigger="click"
                position="bl"
              >
                <a-button type="text" class="action-btn more-btn">
                  <template #icon>
                    <icon-down />
                  </template>
                  更多
                </a-button>
                <template #content>
                  <template
                    v-for="action in getDropdownActions(record)"
                    :key="action.key"
                  >
                    <!-- 普通操作 -->
                    <a-doption
                      v-if="!action.isPopconfirm"
                      @click="handleDropdownAction(action, record)"
                    >
                      <template v-if="action.icon" #icon>
                        <component :is="action.icon" />
                      </template>
                      {{ action.label }}
                    </a-doption>

                    <!-- 需要确认的操作 -->
                    <a-doption v-else @click.stop>
                      <!-- 添加微信的特殊处理 -->
                      <a-popconfirm
                        v-if="action.key === 'add-wechat'"
                        position="left"
                        @ok="handleConfirmAction(action, record)"
                        @cancel="handleCancelAction(record)"
                      >
                        <template #content>
                          <div>
                            <div>确定已添加微信吗？</div>
                            <div class="ai-cen mt-5">
                              <span style="white-space: nowrap">微信号：</span>
                              <a-input
                                v-model="record.wechat"
                                size="mini"
                                @click.stop
                              />
                            </div>
                          </div>
                        </template>
                        <div
                          style="
                            width: 100%;
                            display: flex;
                            align-items: center;
                            cursor: pointer;
                          "
                          @click.stop
                        >
                          <component
                            :is="action.icon"
                            v-if="action.icon"
                            style="margin-right: 8px"
                          />
                          {{ action.label }}
                        </div>
                      </a-popconfirm>

                      <!-- 其他确认操作 -->
                      <a-popconfirm
                        v-else
                        position="left"
                        :content="action.confirmContent"
                        @ok="handleConfirmAction(action, record)"
                        @cancel="handleCancelAction(record)"
                      >
                        <div
                          style="
                            width: 100%;
                            display: flex;
                            align-items: center;
                            cursor: pointer;
                          "
                          @click.stop
                        >
                          <component
                            :is="action.icon"
                            v-if="action.icon"
                            style="margin-right: 8px"
                          />
                          {{ action.label }}
                        </div>
                      </a-popconfirm>
                    </a-doption>
                  </template>
                </template>
              </a-dropdown>
            </div>
          </div>
        </template>
        <template #auto_reply_template="{ record }: TableColumnSlot">
          <span style="white-space: pre-line">
            {{ record.auto_reply_template }}
          </span>
        </template>
        <template #account_id="{ record }: TableColumnSlot">
          <template v-if="record.media_sec_uid">
            <a-link
              v-if="record.platform === '小红书'"
              :href="`https://www.xiaohongshu.com/user/profile/${record.media_sec_uid}`"
              target="_blank"
            >
              {{ record.account_id }}
            </a-link>
            <a-link
              v-else
              :href="`https://www.douyin.com/user/${record.media_sec_uid}`"
              target="_blank"
            >
              {{ record.account_id }}
            </a-link>
          </template>
          <span v-else>{{ record.account_id }}</span>
        </template>
        <template #thread_content="{ record }: TableColumnSlot">
          <a-tooltip
            v-if="record.thread_content?.length > 50"
            :content="record.thread_content.slice(0, 400)"
          >
            <span>{{ record.thread_content.slice(0, 50) }}...</span>
          </a-tooltip>
          <span v-else>{{ record.thread_content }}</span>
        </template>
        <!-- <template #remark="{ record }: TableColumnSlot">
          <a-popover v-model:popup-visible="record.showBtn" trigger="click">
            <template #content>
              <a-input-group>
                <a-textarea
                  v-model="record.remark_new"
                  style="width: 400px"
                  :auto-size="{ minRows: 2, maxRows: 6 }"
                  placeholder="请输入备注信息"
                  @focus="record.showBtn = true"
                  @blur="hideEditBtn(record)"
                />
                <a-button
                  v-if="record.showBtn"
                  type="primary"
                  @click="editRemark(record)"
                >
                  <icon-check />
                </a-button>
              </a-input-group>
            </template>
            <div class="remark-display-container">
              <table-cell-text
                :text="record.remark"
                :max-lines="2"
                :show-copy="false"
                placeholder="-"
                class="remark-text-content"
              />
              <icon-edit class="a-text remark-edit-icon" />
            </div>
          </a-popover>
        </template> -->
        <template #remark="{ record }: TableColumnSlot">
          <span v-if="record.is_statistics">-</span>
          <table-inline-edit
            v-else
            v-model="record.remark"
            placeholder="请输入"
            :max-lines="2"
            :max-length="500"
            :show-copy="false"
            edit-mode="click"
            save-mode="manual"
            :on-save="(value) => editRemark(record, value)"
          />
        </template>
        <template #status_two="{ record }: TableColumnSlot">
          <a-space direction="vertical" style="white-space: pre-line">
            <a-tag
              size="small"
              :color="getColor(clueTwoStatusM, record.status_two)"
            >
              {{ getText(clueTwoStatusM, record.status_two) }}
            </a-tag>
            <template v-if="record.status_two === '作废'">
              <a-tooltip
                v-if="record.cancellation_reason?.length > 50"
                :content="record.cancellation_reason.slice(0, 400)"
              >
                <span>{{ record.cancellation_reason.slice(0, 50) }}...</span>
              </a-tooltip>
              <span v-else>{{ record.cancellation_reason }}</span>
            </template>
          </a-space>
        </template>
        <template #customer_intentionality="{ record }: TableColumnSlot">
          <dict-select
            v-model="record.customer_intentionality"
            :data-list="orderIntentionalityM"
            style="width: 100%"
            size="mini"
            placeholder=""
            @change="
              updateClueInfo({
                ...record,
                id: record.id,
                customer_intentionality: record.customer_intentionality,
              })
            "
          />
        </template>
        <!-- 是否投流 -->
        <template #is_flow_text="{ record }: TableColumnSlot">
          <dict-select
            v-if="record.create_user_id !== 100000"
            v-model="record.is_flow"
            :data-list="isFlowM"
            style="width: 100%"
            size="mini"
            placeholder=""
            @change="
              updateClueInfo({
                ...record,
                id: record.id,
                is_flow: record.is_flow,
              })
            "
          />
          <span v-else>{{ record.is_flow_text }}</span>
        </template>
        <!-- 大概出行日期 -->
        <template #estimated_travel_date="{ record }: TableColumnSlot">
          <TableInlineEdit
            v-model="record.estimated_travel_date"
            placeholder="点击编辑日期"
            :max-lines="2"
            :max-length="200"
            :show-copy="true"
            edit-mode="click"
            save-mode="manual"
            :min-rows="1"
            :max-rows="4"
            :show-word-limit="false"
            :on-save="(value) => handleTravelDateSave(record, value)"
            :disabled="record.dateLoading"
          />
        </template>
        <!-- 订单编号 -->
        <template #relation_order="{ record }: TableColumnSlot">
          <div class="order-number-container">
            <div class="order-number">
              <span v-if="record.relation_order" class="order-number-text">
                {{ record.relation_order }}
              </span>
              <span v-else class="order-number-empty">-</span>
            </div>
            <!-- 订单状态标签 -->
            <span v-if="record.relation_order_status" class="order-status">
              <a-tag
                :color="getOrderStatusColor(record.relation_order_status)"
                size="small"
              >
                {{ record.relation_order_status }}
              </a-tag>
            </span>
          </div>
        </template>
        <!-- 线索细分类型 -->
        <template #clue_type_detail_text="{ record }: TableColumnSlot">
          <span
            >{{ record.clue_type_detail_text }}
            <span v-if="record.clue_type_detail_id">
              （{{ record.clue_type_detail_id }}）
            </span>
          </span>
        </template>
      </base-table>
    </a-card>
    <travel-price-edit ref="priceRef" @save="theTable?.fetchData()" />
    <sale-clue-two-add
      ref="addRef"
      @refresh="theTable?.fetchData()"
    ></sale-clue-two-add>
    <sale-clue-cancellation
      ref="cancelRef"
      @refresh="theTable?.fetchData()"
    ></sale-clue-cancellation>
    <sale-clue-detail ref="detailRef" no-chat></sale-clue-detail>
    <travel-order-detail ref="orderDetailRef"></travel-order-detail>

    <!-- 批量设置客服模态框 -->
    <a-modal
      v-model:visible="batchServiceModalVisible"
      title="批量设置客服"
      width="400px"
      :mask-closable="false"
      :esc-to-close="false"
      @ok="handleBatchServiceSave"
      @cancel="handleBatchServiceCancel"
    >
      <a-form layout="vertical" :model="{ batchServiceUserId }">
        <a-form-item label="选择客服">
          <request-select
            v-model="batchServiceUserId"
            request-url="/api/travel/allServiceUser"
            label-key="user_name"
            value-key="fs_user_id"
            placeholder="请选择客服"
            :loading="serviceListLoading"
            allow-clear
            allow-search
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';

  import { ref, reactive, computed, markRaw } from 'vue';
  import request from '@/api/request';
  import { TableColumnSlot } from '@/global';
  import {
    clueSuorceM,
    clueTwoStatusM,
    clueTypeListM,
    clueTypeM,
    depositPayTypeM,
    depositPayTypeM2,
    isFlowM,
  } from '@/components/dict-select/dict-clue';
  import TravelPriceEdit from '@/views/travel-order/travel-order-list/travel-price-edit.vue';
  import { useRoute } from 'vue-router';
  import { useDataCacheStore, useUserStore } from '@/store';
  import requestSelect from '@/components/select/request-select.vue';
  import SaleClueTwoAdd from '@/views/travel-clue/sale-clue-list-two/sale-clue-two-add.vue';
  import SaleClueCancellation from '@/views/travel-clue/sale-clue-list/sale-clue-cancellation.vue';
  import { getColor, getText } from '@/components/dict-select/dict-util';
  import ColumnsSelect from '@/components/columns-select/columns-select.vue';
  import { cloneDeep } from 'lodash';
  import {
    contactWayListM,
    orderIntentionalityM,
    orderStatusListM,
  } from '@/components/dict-select/dict-travel';
  import SaleClueDetail from '@/views/travel-clue/sale-clue-list/sale-clue-detail.vue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import { getDictTxtRender } from '@/utils/table-utils/columns-config';
  import ClueTypeSelect from '@/views/travel-clue/sale-clue-list-two/clue-type-select.vue';
  import TravelOrderDetail from '@/views/travel-order/travel-order-list/travel-order-detail.vue';
  import ClueTypeRadio from '@/components/order-type-radio/clue-type-radio.vue';
  import requestTreeSelect from '@/components/select/request-tree-select.vue';
  import { Message } from '@arco-design/web-vue';
  import TableCellText from '@/components/table-cell-text/table-cell-text.vue';
  import TableInlineEdit from '@/components/table-inline-edit/TableInlineEdit.vue';
  import { usePermission } from '@/hooks/usePermission';

  const typeRadioRef = ref();
  const refreshTypeRadio = () => {
    typeRadioRef.value?.refresh();
  };

  // 权限控制
  const { isExternalUser } = usePermission();

  const detailRef = ref();
  const orderDetailRef = ref();
  const generateFormModel = () => {
    return {
      id: null as string | null,
      thread_seas_id: null,
      phone_or_wechat: null,
      two_service_user_fs_id: null,
      status_two: null,
      status: '流转二级线索池',
      order_status: null,
      area: null,
      from_setting: null,
      source: null,
      time_distribute_two: null,
      service_user_fs_id: null,
      cancellation_time: null,
      remark: null,
      user_director_id: [],
      user_laterstage_id: [],
      user_operate_id: [],
      user_live_id: [],
      time_relation_inquiry: null,
      time_accept: null,
      time_add_wechat: null,
      time_introduce_product: null,
      order_no: null,
      is_miniapp_pay: null,
      add_user_id: [],
      tab_type: 0,
      clue_type_custom: [],
      // clue_type: null,
      // clue_type_detail: null,
      clue_type_origin_customer: null,
      customer_intentionality: null,
      is_flow: null,
      department_ids: [],
      create_user_id: null,
      media_order_no: null,
      pay_type: null,
      time_into_two: null,
      platform: null,
    };
  };
  const userStore = useUserStore();
  const loading = ref(false);
  const theTable = ref();
  const priceRef = ref();
  const addRef = ref();
  const cancelRef = ref();
  const formModel = reactive(generateFormModel());
  const route = useRoute();
  formModel.id = route.query.id as string;
  if (formModel.id) {
    formModel.tab_type = 0;
  }
  const dataCacheStore = useDataCacheStore();

  const baseSearchRules: any = ref([
    {
      field: 'keywords',
      label: '关键词',
      value: null,
      symbol: '是',
    },
  ]);
  const searchRules = computed(() => {
    let arr = [
      {
        field: 'area',
        label: '旅游线路',
        value: null,
        component_name: 'dict-select',
        alwaysShow: false,
        attr: {
          dataList: dataCacheStore.lineList,
          labelKey: 'line_name',
          valueKey: 'line_name',
        },
      },
      {
        field: 'platform',
        label: '来源媒体',
        value: null,
        component_name: 'dict-select',
        alwaysShow: false,
        attr: {
          dataList: contactWayListM,
        },
      },
      {
        field: 'from_setting',
        label: '来源详情',
        value: null,
        component_name: 'a-input',
        attr: {
          placeholder: '来源详情',
          allowClear: true,
        },
      },
      {
        field: 'thread_seas_id',
        label: '公海线索ID',
        value: null,
        component_name: 'a-input',
        attr: {
          placeholder: '公海线索ID',
          allowClear: true,
        },
      },
      {
        field: 'media_order_no',
        label: '媒体订单号',
        value: null,
        component_name: 'a-input',
        attr: {
          placeholder: '请输入',
          allowClear: true,
        },
      },
      {
        field: 'phone_or_wechat',
        label: '手机/微信',
        value: null,
        component_name: 'a-input',
        attr: {
          placeholder: '手机/微信',
          allowClear: true,
        },
      },
      {
        field: 'two_service_user_fs_id',
        label: '客服',
        value: null,
        component_name: markRaw(requestSelect),
        alwaysShow: false,
        attr: {
          requestUrl: '/api/travel/allServiceUser',
          labelKey: 'user_name',
          valueKey: 'fs_user_id',
        },
      },
      {
        field: 'department_ids',
        label: '客服所属部门',
        value: null,
        component_name: markRaw(requestTreeSelect),
        alwaysShow: false,
        attr: {
          requestUrl: '/api/department/list',
          labelKey: 'department_name',
          childKey: 'child',
          multiple: true,
          maxTagCount: 2,
        },
      },
      {
        field: 'create_user_id',
        label: '添加人',
        value: null,
        component_name: 'department-user-tree-select',
        alwaysShow: false,
        attr: {
          placeholder: '请选择',
          maxDisplayCount: 2,
          sendParams: {
            is_order_filter_create_user_id: true,
          },
        },
      },
      {
        field: 'status_two',
        label: '状态',
        value: null,
        alwaysShow: true,
        component_name: 'dict-select',
        attr: {
          dataList: clueTwoStatusM,
        },
      },
      {
        field: 'service_user_fs_id',
        label: '接粉专员',
        value: null,
        component_name: markRaw(requestSelect),
        attr: {
          requestUrl: '/api/travel/allServiceUser',
          labelKey: 'user_name',
          valueKey: 'fs_user_id',
          multiple: true,
        },
      },
      {
        field: 'time_into_two',
        label: '入库时间',
        value: null,
        component_name: 'c-range-picker',
        attr: {
          needDefault: false,
          allowClear: true,
        },
      },
      {
        field: 'time_distribute_two',
        label: '分配时间',
        value: null,
        component_name: 'c-range-picker',
      },
      {
        field: 'cancellation_time',
        label: '作废时间',
        value: null,
        component_name: 'c-range-picker',
      },
      {
        field: 'time_accept',
        label: '受理时间',
        value: null,
        component_name: 'c-range-picker',
      },
      {
        field: 'time_add_wechat',
        label: '加微信时间',
        value: null,
        component_name: 'c-range-picker',
      },
      {
        field: 'time_introduce_product',
        label: '介绍产品及公司时间',
        value: null,
        component_name: 'c-range-picker',
      },
      {
        field: 'time_relation_inquiry',
        label: '询价并转单时间',
        value: null,
        component_name: 'c-range-picker',
      },
      {
        field: 'remark',
        label: '备注',
        value: null,
        component_name: 'a-input',
        attr: {
          allowClear: true,
          placeholder: '请输入',
        },
      },
      {
        field: 'add_user_id',
        label: '线索提供人',
        value: null,
        component_name: 'department-user-tree-select',
        alwaysShow: false,
        attr: {
          placeholder: '请选择线索提供人',
          maxDisplayCount: 2,
          sendParams: {
            is_order_filter_add_user_id: true,
          },
        },
      },
      {
        field: 'order_no',
        label: '订单编号',
        value: null,
        component_name: 'a-input',
        attr: {
          allowClear: true,
          placeholder: '请输入',
        },
      },
      {
        field: 'order_status',
        label: '订单状态',
        value: null,
        component_name: 'dict-select',
        alwaysShow: true,
        attr: {
          maxTagCount: 1,
          multiple: true,
          dataList: orderStatusListM,
        },
      },
      {
        field: 'pay_type',
        label: '支付方式',
        value: null,
        component_name: 'dict-select',
        attr: {
          dataList: depositPayTypeM2,
        },
      },
      {
        field: 'clue_type_custom',
        label: '线索类型',
        value: null,
        component_name: markRaw(ClueTypeSelect),
      },
      {
        field: 'clue_type_origin_customer',
        label: '原客户昵称及电话',
        value: null,
        component_name: 'a-input',
        attr: {
          allowClear: true,
          placeholder: '请输入',
        },
      },
      {
        field: 'customer_intentionality',
        label: '客户意向度',
        value: null,
        component_name: 'dict-select',
        alwaysShow: false,
        attr: {
          dataList: orderIntentionalityM,
        },
      },
      {
        field: 'is_flow',
        label: '是否投流',
        value: null,
        component_name: 'dict-select',
        alwaysShow: false,
        attr: {
          dataList: isFlowM,
        },
      },
    ];

    // 如果是外部用户，只保留指定的筛选项
    if (isExternalUser.value) {
      const allowedSearchFields = [
        'phone_or_wechat', // 关键词搜索（手机/微信）
        'platform', // 来源媒体
        'area', // 旅游线路
        'clue_type_custom', // 线索类型
        'customer_intentionality', // 客户意向度
      ];
      arr = arr.filter((item) => allowedSearchFields.includes(item.field));
    }
    // 如果是运营 不过滤
    // 客服/计调  不展示是否投流
    if (
      !isExternalUser.value &&
      !userStore.roles.includes(6) &&
      (userStore.roles.includes(3) || userStore.roles.includes(14))
    ) {
      arr = arr.filter(
        // 投流有两个字段 兼容一下
        (item) => item.field !== 'is_flow_text' && item.field !== 'is_flow'
      );
    }
    return arr;
  });

  const updateClueInfo = async (data: any) => {
    return request('/api/thread/addSales', {
      ...data,
    });
  };

  // 处理日期变更
  const handleDateChange = async (record: any, newDate: string) => {
    if (record.dateLoading) return; // 防止重复提交

    // 如果值没有变化，不进行更新
    if (newDate === record.originalEstimatedTravelDate) return;

    const originalDate =
      record.originalEstimatedTravelDate || record.estimated_travel_date; // 保存原始值
    record.dateLoading = true;

    try {
      await updateClueInfo({
        ...record,
        id: record.id,
        estimated_travel_date: newDate,
      });

      Message.success('出行日期更新成功');
      // 更新原始值，避免重复提交
      record.originalEstimatedTravelDate = newDate;
    } catch (error) {
      console.error('更新出行日期失败:', error);
      Message.error('更新出行日期失败，请重试');
      // 恢复原始值
      record.estimated_travel_date = originalDate;
    } finally {
      record.dateLoading = false;
    }
  };

  // TableInlineEdit 组件的保存函数
  const handleTravelDateSave = async (record: any, newDate: string) => {
    if (record.dateLoading) return; // 防止重复提交

    // 如果值没有变化，不进行更新
    if (newDate === record.originalEstimatedTravelDate) return;

    const originalDate =
      record.originalEstimatedTravelDate || record.estimated_travel_date; // 保存原始值
    record.dateLoading = true;

    try {
      await updateClueInfo({
        ...record,
        id: record.id,
        estimated_travel_date: newDate,
      });

      Message.success('出行日期更新成功');
      // 更新原始值，避免重复提交
      record.originalEstimatedTravelDate = newDate;
    } catch (error) {
      console.error('更新出行日期失败:', error);
      Message.error('更新出行日期失败，请重试');
      // 恢复原始值
      record.estimated_travel_date = originalDate;
      throw error; // 重新抛出错误，让 TableInlineEdit 组件处理
    } finally {
      record.dateLoading = false;
    }
  };

  // 手机号保存函数
  const handlePhoneSave = async (record: any, newPhone: string) => {
    if (record.phoneLoading) return; // 防止重复提交

    // 如果值没有变化，不进行更新
    if (newPhone === record.originalPhone) return;

    // 手机号格式验证
    if (newPhone && newPhone.length !== 11) {
      Message.error('请输入正确格式的手机号');
      throw new Error('手机号格式不正确');
    }

    const originalPhone = record.originalPhone || record.phone; // 保存原始值
    record.phoneLoading = true;

    try {
      await updateClueInfo({
        ...record,
        id: record.id,
        phone: newPhone,
      });

      Message.success('手机号更新成功');
      // 更新原始值，避免重复提交
      record.originalPhone = newPhone;
    } catch (error) {
      // console.error('更新手机号失败:', error);
      // Message.error('更新手机号失败，请重试');
      // 恢复原始值
      record.phone = originalPhone;
      throw error; // 重新抛出错误，让 TableInlineEdit 组件处理
    } finally {
      record.phoneLoading = false;
    }
  };

  // 微信号保存函数
  const handleWechatSave = async (record: any, newWechat: string) => {
    if (record.wechatLoading) return; // 防止重复提交

    // 如果值没有变化，不进行更新
    if (newWechat === record.originalWechat) return;

    const originalWechat = record.originalWechat || record.wechat; // 保存原始值
    record.wechatLoading = true;

    try {
      await updateClueInfo({
        ...record,
        id: record.id,
        wechat: newWechat,
      });

      Message.success('微信号更新成功');
      // 更新原始值，避免重复提交
      record.originalWechat = newWechat;
    } catch (error) {
      // console.error('更新微信号失败:', error);
      // Message.error('更新微信号失败，请重试');
      // 恢复原始值
      record.wechat = originalWechat;
      throw error; // 重新抛出错误，让 TableInlineEdit 组件处理
    } finally {
      record.wechatLoading = false;
    }
  };

  function dataHandle(list: any[]) {
    return list.map((item) => ({
      ...item,
      remark_new: item.remark,
      dropdownVisible: false, // 初始化下拉菜单状态
      phoneStatusLoading: false, // 初始化电话状态加载状态
      phonePopoverVisible: false, // 初始化popover显示状态
      // 电话接通状态字段初始化
      phone_is_connect: item.phone_is_connect ?? 0, // 0=未标记, 1=已接通, 2=未接通
      phone_connect_remark: item.phone_connect_remark || '',
      phone_connect_edit_time: item.phone_connect_edit_time || '', // 新增：最后更新时间
      // 临时编辑字段
      tempPhoneStatus: item.phone_is_connect ?? 0,
      tempPhoneRemark: item.phone_connect_remark || '',
      // 新增：微信添加状态字段初始化
      wechatStatusLoading: false, // 初始化微信状态加载状态
      wechatPopoverVisible: false, // 初始化微信popover显示状态
      wechat_is_add: item.wechat_is_add ?? 0, // 0=未标记, 1=已添加, 2=未添加
      wechat_add_remark: item.wechat_add_remark || '',
      // 手机号和微信号编辑相关字段初始化
      phoneLoading: false, // 手机号保存加载状态
      wechatLoading: false, // 微信号保存加载状态
      originalPhone: item.phone, // 保存原始手机号
      originalWechat: item.wechat, // 保存原始微信号
      wechat_add_edit_time: item.wechat_add_edit_time || '', // 最后修改时间
      // 微信临时编辑字段
      tempWechatStatus: item.wechat_is_add ?? 0,
      tempWechatRemark: item.wechat_add_remark || '',
      // 日期编辑状态
      dateLoading: false, // 初始化日期加载状态
      originalEstimatedTravelDate: item.estimated_travel_date, // 保存原始出行日期
    }));
  }
  const getList = async (data: any) => {
    let clue_type_search: any = {};
    formModel.clue_type_custom.forEach((item: any) => {
      // 拆分订单类型和细分类型
      const [type, typeDetail] = item.split('___');
      if (!typeDetail) {
        clue_type_search[type] = [];
      } else if (!clue_type_search[type]) {
        clue_type_search[type] = [typeDetail];
      } else if (clue_type_search[type] && clue_type_search[type].length) {
        clue_type_search[type].push(typeDetail);
      }
    });
    return request('/api/thread/salesThreadList', {
      ...data,
      clue_type_search,
    });
  };

  const allFieldsConfig = [
    {
      title: '全部字段',
      dataIndex: '全部字段',
      keys: [] as string[],
      dataList: [
        {
          title: '公海线索ID',
          dataIndex: 'thread_seas_id',
        },
        {
          title: '询价单号',
          dataIndex: 'relation_inquiry_order',
        },
        {
          title: '订单编号',
          dataIndex: 'relation_order',
          slotName: 'relation_order',
          align: 'center',
          width: 100,
        },
        {
          title: '用户账号ID',
          dataIndex: 'account_id',
        },
        {
          title: '用户账号昵称',
          dataIndex: 'account_name',
        },
        {
          title: '手机号',
          dataIndex: 'phone',
          slotName: 'phone',
          align: 'left',
          width: 200,
        },
        {
          title: '微信',
          align: 'left',
          dataIndex: 'wechat',
          slotName: 'wechat',
          width: 200,
        },
        {
          title: '成人/儿童数量',
          dataIndex: 'adult_num',
          width: 150,
          render: ({ record }: TableColumnSlot) =>
            record.adult_num
              ? `${record.adult_num}大 ${record.children_num || 0}小`
              : '-',
        },
        {
          title: '大概出行日期',
          dataIndex: 'estimated_travel_date',
          slotName: 'estimated_travel_date',
          width: 150,
        },
        {
          title: '来源媒体',
          dataIndex: 'platform',
        },
        {
          title: '旅游线路',
          dataIndex: 'area',
          width: 150,
        },
        {
          title: '来源详情',
          dataIndex: 'from_setting',
          width: 150,
        },
        {
          title: '来源内容',
          dataIndex: 'thread_content',
          width: 150,
        },
        {
          title: '自定义模版消息内容',
          dataIndex: 'auto_reply_template',
          width: 200,
        },
        {
          title: '接粉专员',
          dataIndex: 'service_user',
        },
        {
          title: '客服分配规则',
          dataIndex: 'distribute_rule',
        },
        {
          title: '分配客服',
          dataIndex: 'two_service_user',
        },
        {
          title: '客服所属部门',
          dataIndex: 'department_names',
        },
        {
          title: '分配时间',
          dataIndex: 'time_distribute_two',
          width: 180,
        },
        {
          title: '入库时间',
          dataIndex: 'time_into_two',
          width: 180,
        },
        {
          title: '作废时间',
          dataIndex: 'cancellation_time',
          width: 180,
        },
        {
          title: '受理时间',
          dataIndex: 'time_accept',
          width: 180,
        },
        {
          title: '加微信时间',
          dataIndex: 'time_add_wechat',
          width: 180,
        },
        {
          title: '介绍产品及公司时间',
          dataIndex: 'time_introduce_product',
          width: 180,
        },
        {
          title: '询价并转单时间',
          dataIndex: 'time_relation_inquiry',
          width: 180,
        },
        // {
        //   title: '状态',
        //   dataIndex: 'status_two',
        //   width: 200,
        // },
        {
          title: '备注',
          dataIndex: 'remark',
          slotName: 'remark',
          width: 180,
        },
        {
          title: '线索提供人',
          dataIndex: 'add_user_name',
          width: 120,
        },
        {
          title: '添加人',
          dataIndex: 'create_user_name',
        },
        {
          title: '支付方式',
          dataIndex: 'pay_type_text',
        },
        {
          title: '线索类型',
          dataIndex: 'clue_type',
        },
        {
          title: '线索细分类型',
          dataIndex: 'clue_type_detail_text',
          slotName: 'clue_type_detail_text',
          width: 250,
        },
        {
          title: '原客户昵称及电话',
          dataIndex: 'clue_type_origin_customer_nickname',
          width: 200,
          render: ({ record }: TableColumnSlot) =>
            record.clue_type_origin_customer_phone
              ? `${record.clue_type_origin_customer_nickname}(${record.clue_type_origin_customer_phone})`
              : '-',
        },
        {
          dataIndex: 'customer_intentionality',
          title: '客户意向度',
          defaultHide: true,
          width: 200,
          description:
            'A：确定出行时间，确认人数。\nB：确定人数，不确定出行时间/不确定人数。\nC：不确定人数，不确定出行时间。\nD：无意向，且非空号。',
        },
        // 是否投流
        {
          dataIndex: 'is_flow_text',
          title: '是否投流',
          width: 120,
          defaultHide: true,
        },
      ],
    },
  ];
  allFieldsConfig.forEach((item) => {
    // 如果是运营，则不过滤
    // 如果是客服/计调，则隐藏是否投流
    if (
      !userStore.roles.includes(6) &&
      (userStore.roles.includes(3) || userStore.roles.includes(14))
    ) {
      item.dataList = item.dataList.filter(
        (citem) => citem.dataIndex !== 'is_flow_text'
      );
    }
    // 如果是三木且是电销角色，则隐藏入库时间
    if (userStore.is_three_wood_customer_service) {
      item.dataList = item.dataList.filter(
        (citem) => citem.dataIndex !== 'time_into_two'
      );
    }

    item.keys = item.dataList.map((citem) => {
      // @ts-ignore
      citem.key = citem.dataIndex;
      return citem.dataIndex;
    });
  });
  // 外部用户允许查看的列
  const externalUserAllowedColumns = [
    'phone',
    'wechat',
    'adult_num',
    'estimated_travel_date',
    'platform',
    'area',
    'two_service_user',
    'remark',
    'add_user_name',
    'clue_type',
    // 'clue_type_detail_text',
    'customer_intentionality',
    'time_distribute_two',
  ];

  // 初始化列配置，外部用户只显示允许的列
  const getInitialColumnsConfig = () => {
    if (isExternalUser.value) {
      return cloneDeep(
        allFieldsConfig[0].dataList.filter((column) =>
          externalUserAllowedColumns.includes(column.dataIndex)
        )
      );
    }
    return cloneDeep(allFieldsConfig[0].dataList);
  };

  const columnsConfig = ref(getInitialColumnsConfig());

  // 为外部用户过滤列选择器的可选列
  const filteredFieldsConfig = computed(() => {
    if (isExternalUser.value) {
      // 外部用户只能看到允许的列
      const filteredDataList = allFieldsConfig[0].dataList.filter((column) =>
        externalUserAllowedColumns.includes(column.dataIndex)
      );
      return [
        {
          ...allFieldsConfig[0],
          dataList: filteredDataList,
          keys: filteredDataList.map((item) => item.dataIndex),
        },
      ];
    }
    // 内部用户看到所有列
    return allFieldsConfig;
  });

  const columns = computed(() => {
    const baseColumns = [
      {
        title: '线索ID',
        dataIndex: 'id',
        width: 100,
        align: 'center',
        fixed: 'left',
      },
    ];

    // 如果是外部用户，使用用户配置的列，但只显示允许的列
    if (isExternalUser.value) {
      // 过滤用户配置的列，确保只包含允许的列
      const userSelectedColumns = columnsConfig.value.filter((column) =>
        externalUserAllowedColumns.includes(column.dataIndex)
      );
      baseColumns.push(...userSelectedColumns);

      // 外部用户显示操作列
      baseColumns.push({
        title: '操作',
        dataIndex: 'action',
        align: 'center',
        width: 140,
        fixed: 'right',
      });
    } else {
      // 内部用户显示所有配置的列
      baseColumns.push(...columnsConfig.value);

      // 内部用户显示操作列
      baseColumns.push({
        title: '操作',
        dataIndex: 'action',
        align: 'center',
        width: 200,
        fixed: 'right',
      });
    }

    return baseColumns;
  });
  const scrollPercent = computed(() => ({
    maxHeight: '67vh',
    x: columns.value.length * 160,
  }));
  // table渲染完成回调
  const changeHandler = (tableData: any) => {
    loading.value = false;
  };

  const tableParams = computed(() => ({
    ...formModel,
  }));

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    delete resData.tab_type;
    Object.assign(formModel, resData);
    theTable.value?.search();
    refreshTypeRadio();
  };

  function setColumns(val: any[]) {
    if (isExternalUser.value) {
      // 外部用户：只保留允许的列
      columnsConfig.value = val.filter((item) =>
        externalUserAllowedColumns.includes(item.dataIndex)
      );
    } else {
      // 内部用户：使用原有逻辑
      columnsConfig.value = val.filter((item) =>
        allFieldsConfig.some((citem) => citem.keys.includes(item.dataIndex))
      );
    }
    handleSubmit();
  }

  function hideEditBtn(record: any) {
    setTimeout(() => {
      record.showBtn = false;
      record.remark_new = record.remark;
    }, 300);
  }
  function editRemark(record: any, value: string) {
    record.showBtn = false;
    // record.remark = record.remark_new;
    request('/api/thread/addSales', {
      ...record,
      id: record.id,
      remark: value,
    });
  }

  function exportAction() {
    theTable.value?.exportTable({});
  }

  // 批量操作相关
  // 数据选择相关
  let rowSelection = reactive({
    type: 'checkbox',
    fixed: true,
    width: 30,
    onlyCurrent: true,
    showCheckedAll: true,
    selectedRowKeys: [] as any[],
    selectedRows: [] as any[],
  });

  const selectionChange = (selectedRowKeys: any[], selectedRows: any[]) => {
    rowSelection.selectedRowKeys = selectedRowKeys.filter((item: any) => item);
    rowSelection.selectedRows = selectedRows;
  };

  // 清空选择
  const clearSelection = () => {
    rowSelection.selectedRowKeys = [];
    rowSelection.selectedRows = [];
  };

  // 批量设置客服相关
  const batchServiceModalVisible = ref(false);
  const batchServiceUserId = ref<string | null>(null);
  const batchServiceLoading = ref(false);
  const serviceListLoading = ref(false);

  const showBatchServiceModal = () => {
    if (rowSelection.selectedRows.length === 0) {
      Message.warning('请先选择要设置客服的线索记录');
      return;
    }
    batchServiceModalVisible.value = true;
    batchServiceUserId.value = null;
  };

  const handleBatchServiceCancel = () => {
    batchServiceModalVisible.value = false;
    batchServiceUserId.value = null;
  };

  const handleBatchServiceSave = async () => {
    try {
      batchServiceLoading.value = true;

      // 批量更新线索的客服
      const res = await request('/api/thread/batchSetCustomerService', {
        ids: rowSelection.selectedRowKeys,
        two_service_user_fs_id: batchServiceUserId.value,
      });
      Message.success(`操作成功`);
      // 关闭弹窗
      handleBatchServiceCancel();
      // 刷新表格数据
      theTable.value?.fetchData();

      // 清空选择
      clearSelection();
    } catch (error) {
      console.error('批量设置客服失败:', error);
      Message.error('批量设置客服失败');
    } finally {
      batchServiceLoading.value = false;
    }
  };

  function addWechat(record: any) {
    if (!record.loading) {
      record.loading = true;
      request('/api/thread/ensureAddWechat', {
        id: record.id,
        wechat: record.wechat,
      })
        .then(() => {
          theTable.value?.fetchData();
        })
        .finally(() => {
          record.loading = false;
        });
    }
  }
  function shouliAction(record: any) {
    if (!record.loading) {
      record.loading = true;
      request('/api/thread/hadAccept', {
        id: record.id,
      })
        .then(() => {
          Message.success('操作成功');
          theTable.value?.fetchData();
        })
        .finally(() => {
          record.loading = false;
        });
    }
  }
  function hasCommunicate(record: any) {
    if (!record.loading) {
      record.loading = true;
      request('/api/thread/hadIntroduceProduct', {
        id: record.id,
      })
        .then(() => {
          theTable.value?.fetchData();
        })
        .finally(() => {
          record.loading = false;
        });
    }
  }

  // 处理生成订单操作
  const handleCreateOrder = (record: any) => {
    priceRef?.value?.show({
      inquiry_order_info: {
        thread_id: record.id,
        adult_num: record.adult_num,
        children_num: record.children_num,
        customer_name: record.account_name,
      },
      order_info: {
        phone: record.phone,
        source: record.platform,
        service_user_fs_id: record.two_service_user_fs_id,
        service_user: record.two_service_user,
        customer_name: record.account_name,
        customer_phone: record.phone,
        order_source: '内容私域',
      },
    });
  };

  // 判断是否显示编辑订单按钮
  const shouldShowEditOrder = (record: any) => {
    return (
      ([
        '已支付订金',
        '已受理',
        '待计调审核',
        '已发确认件',
        '已出行',
        '已核销',
      ].includes(record.relation_order_status) &&
        userStore.hasPermission(14)) ||
      (['已支付订金', '已受理', '待计调审核'].includes(
        record.relation_order_status
      ) &&
        userStore.hasPermission(3))
    );
  };

  // 获取所有可用的操作按钮
  const getAllActions = (record: any) => {
    const actions = [];

    // 2. 编辑订单（优先级第二）
    if (record.status_two === '已询价' && shouldShowEditOrder(record)) {
      actions.push({
        key: 'edit-order',
        label: '编辑订单',
        type: 'text',
        icon: 'icon-edit',
        priority: 2,
        handler: () =>
          priceRef?.value?.show({ order_no: record.relation_order }),
      });
    }

    // 3. 生成订单（优先级第三）
    if (
      ['已受理', '已添加微信', '已介绍产品及公司'].includes(record.status_two)
    ) {
      actions.push({
        key: 'create-order',
        label: '生成订单',
        type: 'text',
        icon: 'icon-plus',
        priority: 3,
        handler: () => handleCreateOrder(record),
      });
    }

    // 4. 受理操作
    if (record.status_two === '已分配' && userStore.hasPermission(3)) {
      actions.push({
        key: 'accept',
        label: '受理',
        type: 'text',
        icon: 'icon-check',
        priority: 4,
        isPopconfirm: true,
        confirmContent: '确定受理此线索吗？',
        handler: () => shouliAction(record),
      });
    }

    // 1. 编辑（优先级最高，总是显示）
    actions.push({
      key: 'edit',
      label: '编辑',
      type: 'text',
      status: 'warning',
      icon: 'icon-edit',
      priority: 10,
      handler: () => addRef?.value?.show(record),
    });

    // 5. 添加微信操作
    // if (record.status_two === '已受理') {
    //   actions.push({
    //     key: 'add-wechat',
    //     label: '添加微信',
    //     type: 'text',
    //     icon: 'icon-wechat',
    //     priority: 5,
    //     isPopconfirm: true,
    //     handler: () => addWechat(record),
    //   });
    // }

    // 6. 介绍产品及公司操作
    // if (record.status_two === '已受理') {
    //   actions.push({
    //     key: 'introduce-product',
    //     label: '介绍产品及公司',
    //     type: 'text',
    //     icon: 'icon-info-circle',
    //     priority: 6,
    //     isPopconfirm: true,
    //     confirmContent: '确定已介绍产品及公司吗？',
    //     handler: () => hasCommunicate(record),
    //   });
    // }

    // 7. 查看订单操作
    if (record.status_two === '已询价' && !shouldShowEditOrder(record)) {
      actions.push({
        key: 'view-order',
        label: '查看订单',
        type: 'text',
        icon: 'icon-eye',
        priority: 7,
        handler: () =>
          orderDetailRef?.value?.show({ order_no: record.relation_order }),
      });
    }

    // 8. 查看沟通记录操作
    if (record.thread_seas_id) {
      actions.push({
        key: 'view-communication',
        label: '查看沟通记录',
        type: 'text',
        icon: 'icon-message',
        priority: 8,
        handler: () => detailRef?.value?.show(record),
      });
    }

    // 9. 作废操作（优先级最低）
    if (record.status_two !== '作废') {
      actions.push({
        key: 'cancel',
        label: '作废',
        type: 'text',
        icon: 'icon-close',
        priority: 9,
        handler: () => cancelRef?.value?.show(record),
      });
    }

    return actions.sort((a, b) => a.priority - b.priority);
  };

  // 获取主要操作按钮（最多显示2个）
  const getMainActions = (record: any) => {
    const allActions = getAllActions(record);
    return allActions.slice(0, Math.min(allActions.length, 1));
  };

  // 获取下拉菜单中的操作按钮
  const getDropdownActions = (record: any) => {
    const allActions = getAllActions(record);
    return allActions.slice(1);
  };

  // 处理普通下拉菜单操作
  const handleDropdownAction = (action: any, record: any) => {
    // 执行操作
    action.handler();
    // 关闭下拉菜单
    record.dropdownVisible = false;
  };

  // 处理确认操作
  const handleConfirmAction = (action: any, record: any) => {
    // 执行操作
    action.handler();
    // 关闭下拉菜单
    record.dropdownVisible = false;
  };

  // 处理取消操作
  const handleCancelAction = (record: any) => {
    // 关闭下拉菜单
    record.dropdownVisible = false;
  };

  // 获取订单状态颜色
  const getOrderStatusColor = (status: string) => {
    // 使用字典数据和工具函数获取颜色，保持代码一致性
    return getColor(orderStatusListM, status) || '';
  };

  // 电话接通状态相关函数

  // 获取电话状态的CSS类名
  const getPhoneStatusClass = (status: number) => {
    switch (status) {
      case 1:
        return 'phone-status-connected';
      case 2:
        return 'phone-status-not-connected';
      default:
        return 'phone-status-default';
    }
  };

  // 新增：格式化编辑时间
  const formatEditTime = (timeStr: string) => {
    if (!timeStr) return '未设置';

    try {
      const date = new Date(timeStr);
      if (Number.isNaN(date.getTime())) return '未设置';

      // 格式化为 YYYY-MM-DD HH:mm:ss
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (error) {
      return '未设置';
    }
  };

  // 获取电话状态的tooltip内容
  const getPhoneStatusTooltip = (record: any) => {
    const statusText =
      {
        1: '已接通',
        2: '未接通',
        0: '未标记',
      }[record.phone_is_connect] || '未标记';

    // let tooltip = `状态：${statusText}`;
    let tooltip = ``;

    // 添加备注信息
    if (record.phone_connect_remark) {
      tooltip += `\n备注：${record.phone_connect_remark}`;
    }
    // 添加最后更新时间
    if (record.phone_connect_edit_time) {
      tooltip += `\n（最后更新时间：${formatEditTime(
        record.phone_connect_edit_time
      )}）`;
    } else {
      tooltip += `\n最后更新时间：未设置`;
    }

    return tooltip;
  };

  // 新增：获取电话状态文字
  const getPhoneStatusText = (status: number) => {
    return (
      {
        1: '已接通',
        2: '未接通',
        0: '未标记',
      }[status] || '未标记'
    );
  };

  // 新增：获取电话状态标签颜色
  const getPhoneStatusTagColor = (status: number) => {
    switch (status) {
      case 1:
        return 'green'; // 已接通 - 绿色
      case 2:
        return 'red'; // 未接通 - 红色
      default:
        return 'gray'; // 未标记 - 灰色
    }
  };

  // 新增：获取微信状态文字
  const getWechatStatusText = (status: number) => {
    return (
      {
        1: '已添加',
        2: '未添加',
        0: '未标记',
      }[status] || '未标记'
    );
  };

  // 新增：获取微信状态标签颜色
  const getWechatStatusTagColor = (status: number) => {
    switch (status) {
      case 1:
        return 'green'; // 已添加 - 绿色
      case 2:
        return 'red'; // 未添加 - 红色
      default:
        return 'gray'; // 未标记 - 灰色
    }
  };

  // 新增：获取微信状态的CSS类名
  const getWechatStatusClass = (status: number) => {
    switch (status) {
      case 1:
        return 'wechat-status-connected';
      case 2:
        return 'wechat-status-not-connected';
      default:
        return 'wechat-status-default';
    }
  };

  // 新增：获取微信状态的tooltip内容
  const getWechatStatusTooltip = (record: any) => {
    const statusText =
      {
        1: '已添加',
        2: '未添加',
        0: '未标记',
      }[record.wechat_is_add] || '未标记';

    let tooltip = `状态：${statusText}`;

    // 添加最后修改时间
    if (record.wechat_add_edit_time) {
      tooltip += `\n最后修改时间：${formatEditTime(
        record.wechat_add_edit_time
      )}`;
    } else {
      tooltip += `\n最后修改时间：未设置`;
    }

    // 添加备注信息
    if (record.wechat_add_remark) {
      tooltip += `\n备注：${record.wechat_add_remark}`;
    }

    return tooltip;
  };

  // 打开电话状态编辑popover
  const openPhoneStatusPopover = (record: any) => {
    // 初始化临时编辑数据
    record.tempPhoneStatus = record.phone_is_connect ?? 0;
    record.tempPhoneRemark = record.phone_connect_remark || '';
    // 备份原始的暂缓联系状态
    record.originalDeferContract = record.is_defer_contract;
    record.phonePopoverVisible = true;
  };

  // 取消电话状态编辑
  const cancelPhoneStatusEdit = (record: any) => {
    record.phonePopoverVisible = false;
    // 重置临时数据
    record.tempPhoneStatus = record.phone_is_connect ?? 0;
    record.tempPhoneRemark = record.phone_connect_remark || '';
    // 恢复原始的暂缓联系状态
    record.is_defer_contract = record.originalDeferContract;
  };

  // 保存电话接通状态
  const savePhoneStatus = async (record: any) => {
    if (record.phoneStatusLoading) return;

    record.phoneStatusLoading = true;

    try {
      const response = await request('/api/thread/phoneConnectSave', {
        id: record.id,
        phone_is_connect: record.tempPhoneStatus,
        phone_connect_remark: record.tempPhoneRemark || '',
        is_defer_contract: record.is_defer_contract,
      });

      // 更新实际数据
      record.phone_is_connect = record.tempPhoneStatus;
      record.phone_connect_remark = record.tempPhoneRemark || '';

      // 更新最后更新时间（从API响应获取或使用当前时间）
      record.phone_connect_edit_time =
        response?.phone_connect_edit_time || new Date().toISOString();

      // 关闭popover
      record.phonePopoverVisible = false;

      // 刷新列表数据以确保显示最新状态
      theTable.value?.fetchData();

      // 可以添加成功提示
      Message.success('电话状态保存成功');
    } catch (error) {
      console.error('保存电话状态失败:', error);
      // Message.error('保存失败，请重试');
    } finally {
      record.phoneStatusLoading = false;
    }
  };

  // 新增：打开微信状态编辑popover
  const openWechatStatusPopover = (record: any) => {
    // 初始化临时编辑数据
    record.tempWechatStatus = record.wechat_is_add;
    record.tempWechatRemark = record.wechat_add_remark;
    // 备份原始的暂缓联系状态
    record.originalDeferContract = record.is_defer_contract;
    // 打开popover
    record.wechatPopoverVisible = true;
  };

  // 新增：取消微信状态编辑
  const cancelWechatStatusEdit = (record: any) => {
    record.wechatPopoverVisible = false;
    // 重置临时数据
    record.tempWechatStatus = record.wechat_is_add ?? 0;
    record.tempWechatRemark = record.wechat_add_remark || '';
    // 恢复原始的暂缓联系状态
    record.is_defer_contract = record.originalDeferContract;
  };

  // 新增：保存微信添加状态
  const saveWechatStatus = async (record: any) => {
    if (record.wechatStatusLoading) return;

    record.wechatStatusLoading = true;

    try {
      const response = await request('/api/thread/wechatConnectSave', {
        id: record.id,
        wechat_is_add: record.tempWechatStatus,
        wechat_add_remark: record.tempWechatRemark || '',
        is_defer_contract: record.is_defer_contract,
      });

      // 更新实际数据
      record.wechat_is_add = record.tempWechatStatus;
      record.wechat_add_remark = record.tempWechatRemark || '';

      // 更新最后修改时间（从API响应获取或使用当前时间）
      record.wechat_add_edit_time =
        response?.wechat_add_edit_time || new Date().toISOString();

      // 关闭popover
      record.wechatPopoverVisible = false;

      // 刷新列表数据以确保显示最新状态
      theTable.value?.fetchData();

      // 可以添加成功提示
      Message.success('微信状态保存成功');
    } catch (error) {
      console.error('保存微信状态失败:', error);
      // Message.error('保存失败，请重试');
    } finally {
      record.wechatStatusLoading = false;
    }
  };
</script>

<style scoped lang="less">
  // 优化：手机号容器样式 - 紧凑化布局
  .phone-container {
    display: flex;
    align-items: left;
    flex-flow: column;
    gap: 4px; // 从8px减少到4px，更紧凑

    // 手机号编辑组件样式
    .phone-edit-wrapper {
      flex: 1;
      min-width: 0;
    }

    .phone-link {
      flex: 1;
      min-width: 0;
    }

    .phone-empty {
      color: var(--color-text-3);
    }
  }

  // 新增：电话状态包装器样式
  .phone-status-wrapper {
    display: flex;
    align-items: center;
    gap: 1px; // 状态指示器和文字之间的间距
  }

  // 新增：微信容器样式
  .wechat-container {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    gap: 4px;

    .wechat-content {
      flex: 1;
      min-width: 0;

      // 微信号编辑组件样式
      .wechat-edit-wrapper {
        flex: 1;
        min-width: 0;
      }

      .wechat-text {
        word-break: break-all;
      }

      .wechat-empty {
        color: var(--color-text-3);
      }
    }
  }

  // 新增：订单编号容器样式
  .order-number-container {
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: center;
    justify-content: center;

    .order-number {
      .order-number-text {
        font-weight: 500;
        color: var(--color-text-1);
        word-break: break-all;
      }

      .order-number-empty {
        color: var(--color-text-3);
      }
    }
  }

  // 新增：微信状态包装器样式
  .wechat-status-wrapper {
    display: flex;
    align-items: center;
    gap: 1px; // 状态指示器和文字之间的间距
  }

  // 优化：电话接通状态指示器样式 - 增强视觉效果
  .phone-status-indicator {
    width: 16px; // 从20px减少到16px，更紧凑
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); // 更流畅的动画
    border: 2px solid transparent; // 增加边框厚度
    font-size: 10px; // 从12px减少到10px，适配更小的容器
    flex-shrink: 0;
    position: relative;

    // 添加内阴影增强立体感
    &::before {
      content: '';
      position: absolute;
      top: 1px;
      left: 1px;
      right: 1px;
      bottom: 1px;
      border-radius: 50%;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.2),
        rgba(255, 255, 255, 0.05)
      );
      pointer-events: none;
    }

    &:hover {
      transform: scale(1.15); // 增加悬停缩放效果
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); // 增强阴影效果
    }

    &:active {
      transform: scale(1.05);
      transition: all 0.1s ease;
    }

    // 优化：默认状态（未标记）- 更明显的视觉区别
    &.phone-status-default {
      background: linear-gradient(135deg, #f7f8fa, #e5e6eb); // 渐变背景
      color: #86909c; // 更明确的颜色
      border-color: #c9cdd4;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);

      &:hover {
        background: linear-gradient(135deg, #f2f3f5, #e0e1e6);
        border-color: #a9aeb8;
        color: #6b7785;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
      }
    }

    // 优化：已接通状态 - 更鲜明的绿色方案
    &.phone-status-connected {
      background: linear-gradient(135deg, #00d084, #00b968); // 渐变绿色
      color: white;
      border-color: #00d084;
      box-shadow: 0 2px 8px rgba(0, 208, 132, 0.3); // 绿色阴影

      &:hover {
        background: linear-gradient(135deg, #00e094, #00c978);
        border-color: #00e094;
        box-shadow: 0 4px 16px rgba(0, 208, 132, 0.4);
      }

      // 添加成功状态的光晕效果
      &::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 50%;
        background: radial-gradient(
          circle,
          rgba(0, 208, 132, 0.2),
          transparent 70%
        );
        z-index: -1;
      }
    }

    // 优化：未接通状态 - 更鲜明的红色方案
    &.phone-status-not-connected {
      background: linear-gradient(135deg, #f53f3f, #d91a1a); // 渐变红色
      color: white;
      border-color: #f53f3f;
      box-shadow: 0 2px 8px rgba(245, 63, 63, 0.3); // 红色阴影

      &:hover {
        background: linear-gradient(135deg, #f76560, #e63946);
        border-color: #f76560;
        box-shadow: 0 4px 16px rgba(245, 63, 63, 0.4);
      }

      // 添加错误状态的光晕效果
      &::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 50%;
        background: radial-gradient(
          circle,
          rgba(245, 63, 63, 0.2),
          transparent 70%
        );
        z-index: -1;
      }
    }

    // 图标样式优化
    .arco-icon {
      font-weight: bold;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    }
  }

  // 新增：电话状态标签样式
  .phone-status-tag {
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      opacity: 0.8;
    }
  }

  // 新增：微信状态指示器样式（复用电话状态指示器样式）
  .wechat-status-indicator {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    font-size: 10px;
    flex-shrink: 0;
    position: relative;

    // 添加内阴影增强立体感
    &::before {
      content: '';
      position: absolute;
      top: 1px;
      left: 1px;
      right: 1px;
      bottom: 1px;
      border-radius: 50%;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.2),
        rgba(255, 255, 255, 0.05)
      );
      pointer-events: none;
    }

    &:hover {
      transform: scale(1.15);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: scale(1.05);
      transition: all 0.1s ease;
    }

    // 默认状态（未标记）
    &.wechat-status-default {
      background: linear-gradient(135deg, #f7f8fa, #e5e6eb);
      color: #86909c;
      border-color: #c9cdd4;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);

      &:hover {
        background: linear-gradient(135deg, #f2f3f5, #e0e1e6);
        border-color: #a9aeb8;
        color: #6b7785;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
      }
    }

    // 已添加状态
    &.wechat-status-connected {
      background: linear-gradient(135deg, #00d084, #00b968);
      color: white;
      border-color: #00d084;
      box-shadow: 0 2px 8px rgba(0, 208, 132, 0.3);

      &:hover {
        background: linear-gradient(135deg, #00e094, #00c978);
        border-color: #00e094;
        box-shadow: 0 4px 16px rgba(0, 208, 132, 0.4);
      }

      // 添加成功状态的光晕效果
      &::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 50%;
        background: radial-gradient(
          circle,
          rgba(0, 208, 132, 0.2),
          transparent 70%
        );
        z-index: -1;
      }
    }

    // 未添加状态
    &.wechat-status-not-connected {
      background: linear-gradient(135deg, #f53f3f, #d91a1a);
      color: white;
      border-color: #f53f3f;
      box-shadow: 0 2px 8px rgba(245, 63, 63, 0.3);

      &:hover {
        background: linear-gradient(135deg, #f76560, #e63946);
        border-color: #f76560;
        box-shadow: 0 4px 16px rgba(245, 63, 63, 0.4);
      }

      // 添加错误状态的光晕效果
      &::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 50%;
        background: radial-gradient(
          circle,
          rgba(245, 63, 63, 0.2),
          transparent 70%
        );
        z-index: -1;
      }
    }

    // 图标样式优化
    .arco-icon {
      font-weight: bold;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    }
  }

  // 新增：微信状态标签样式
  .wechat-status-tag {
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      opacity: 0.8;
    }
  }

  // 新增：微信状态popover样式（复用电话状态popover样式）
  .wechat-status-popover {
    .status-section {
      margin-bottom: 12px;

      .section-title {
        font-size: 13px;
        font-weight: 500;
        color: var(--color-text-1);
        margin-bottom: 8px;
      }

      // 横向布局的单选组
      :deep(.horizontal-radio-group) {
        display: flex;
        flex-direction: row;
        gap: 8px;
        flex-wrap: wrap;

        .arco-radio {
          margin: 0;
          padding: 6px 8px;
          border: 1px solid var(--color-border-2);
          border-radius: 4px;
          transition: all 0.2s ease;
          flex: 1;
          min-width: 70px;
          text-align: center;

          &:hover {
            border-color: var(--color-border-1);
            background-color: var(--color-fill-1);
          }

          &.arco-radio-checked {
            border-color: rgb(var(--primary-6));
            background-color: var(--color-primary-light-1);
          }

          // 不同状态的特殊样式
          &.connected.arco-radio-checked {
            border-color: #00d084;
            background-color: rgba(0, 208, 132, 0.1);
            color: #00b968;
          }

          &.not-connected.arco-radio-checked {
            border-color: #f53f3f;
            background-color: rgba(245, 63, 63, 0.1);
            color: #d91a1a;
          }

          &.default.arco-radio-checked {
            border-color: #86909c;
            background-color: rgba(134, 144, 156, 0.1);
            color: #86909c;
          }

          .arco-radio-label {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 3px;
            font-size: 11px;
            white-space: nowrap;
          }
        }
      }
    }

    // 最后修改时间显示区域
    .edit-time-section {
      margin-bottom: 12px;

      .section-title {
        font-size: 13px;
        font-weight: 500;
        color: var(--color-text-1);
        margin-bottom: 6px;
      }

      .edit-time-display {
        font-size: 12px;
        color: var(--color-text-2);
        padding: 6px 8px;
        border-radius: 4px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      }
    }

    .remark-section {
      margin-bottom: 12px;

      .section-title {
        font-size: 13px;
        font-weight: 500;
        color: var(--color-text-1);
        margin-bottom: 6px;
      }
    }

    .action-section {
      display: flex;
      justify-content: space-between;
      padding-top: 6px;
      border-top: 1px solid var(--color-border-2);
    }
  }

  // 优化：电话状态popover样式 - 紧凑化布局
  .phone-status-popover {
    .status-section {
      margin-bottom: 12px; // 从16px减少到12px

      .section-title {
        font-size: 13px; // 从14px减少到13px
        font-weight: 500;
        color: var(--color-text-1);
        margin-bottom: 8px; // 稍微增加一点间距以适应横向布局
      }

      // 优化：横向布局的单选组
      :deep(.horizontal-radio-group) {
        display: flex;
        flex-direction: row; // 明确设置为横向
        gap: 8px; // 横向间距
        flex-wrap: wrap; // 允许换行以适应小屏幕

        .arco-radio {
          margin: 0;
          padding: 6px 8px; // 横向布局时减少padding
          border: 1px solid var(--color-border-2);
          border-radius: 4px;
          transition: all 0.2s ease;
          flex: 1; // 平均分配宽度
          min-width: 70px; // 最小宽度确保文字显示完整
          text-align: center;

          &:hover {
            border-color: var(--color-border-1);
            background-color: var(--color-fill-1);
          }

          &.arco-radio-checked {
            border-color: rgb(var(--primary-6));
            background-color: var(--color-primary-light-1);
          }

          // 不同状态的特殊样式
          &.connected.arco-radio-checked {
            border-color: #00d084;
            background-color: rgba(0, 208, 132, 0.1);
            color: #00b968;
          }

          &.not-connected.arco-radio-checked {
            border-color: #f53f3f;
            background-color: rgba(245, 63, 63, 0.1);
            color: #d91a1a;
          }

          &.default.arco-radio-checked {
            border-color: #86909c;
            background-color: rgba(134, 144, 156, 0.1);
            color: #86909c;
          }

          .arco-radio-label {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 3px; // 减少图标和文字间距
            font-size: 11px; // 进一步减小字体以适应横向布局
            white-space: nowrap;
          }
        }
      }
    }

    // 新增：最后更新时间显示区域
    .edit-time-section {
      margin-bottom: 12px;

      .section-title {
        font-size: 13px;
        font-weight: 500;
        color: var(--color-text-1);
        margin-bottom: 6px;
      }

      .edit-time-display {
        font-size: 12px;
        color: var(--color-text-2);
        // background-color: var(--color-fill-1);
        padding: 6px 8px;
        border-radius: 4px;
        // border: 1px solid var(--color-border-2);
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; // 使用等宽字体
      }
    }

    .remark-section {
      margin-bottom: 12px; // 从16px减少到12px

      .section-title {
        font-size: 13px; // 从14px减少到13px
        font-weight: 500;
        color: var(--color-text-1);
        margin-bottom: 6px; // 从8px减少到6px
      }
    }

    .action-section {
      display: flex;
      justify-content: space-between;
      padding-top: 6px; // 从8px减少到6px
      border-top: 1px solid var(--color-border-2);
    }
  }

  // 新增：响应式操作按钮容器样式
  .action-buttons-container {
    width: 100%;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    &.center {
      justify-content: center;
    }
  }

  .action-buttons-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    flex-wrap: wrap;
    max-width: 100%;

    // 当按钮换行时的样式
    &:has(.action-btn:nth-child(n + 3)) {
      justify-content: flex-start;
      gap: 2px 4px;
    }
  }

  .action-btn {
    flex-shrink: 0;
    min-width: auto;
    padding: 0 8px;
    font-size: 12px;
    height: 24px;
    line-height: 22px;
    border-radius: 4px;
    transition: all 0.2s ease;
    white-space: nowrap;

    // 确保按钮文字不被截断
    overflow: visible;
    text-overflow: clip;

    &.arco-btn-text {
      border: none;
      background: transparent;

      &:hover {
        background: var(--color-fill-2);
      }
    }

    // 紧凑模式样式
    &.action-btn-compact {
      padding: 0 6px;
      font-size: 11px;
      min-width: 32px;

      // 紧凑模式下隐藏图标，节省空间
      :deep(.arco-btn-icon) {
        display: none;
      }
    }

    // 更多按钮特殊样式
    &.more-btn {
      color: var(--color-text-2);

      &:hover {
        color: var(--color-text-1);
        background: var(--color-fill-2);
      }

      :deep(.arco-btn-icon) {
        margin-left: 2px;
        font-size: 10px;
      }
    }
  }

  // 响应式适配
  @media (max-width: 1200px) {
    .action-buttons-wrapper {
      gap: 2px;

      .action-btn {
        padding: 0 6px;
        font-size: 11px;

        &:not(.action-btn-compact) {
          :deep(.arco-btn-icon) {
            display: none;
          }
        }
      }
    }
  }

  // 移动端适配
  @media (max-width: 768px) {
    .action-buttons-container {
      padding: 1px 2px;
    }

    .action-buttons-wrapper {
      gap: 1px 2px;

      .action-btn {
        padding: 0 4px;
        font-size: 10px;
        height: 22px;
        line-height: 20px;
        min-width: 28px;

        :deep(.arco-btn-icon) {
          display: none;
        }
      }
    }

    // 移动端电话状态适配
    .phone-container {
      flex-direction: column;
      align-items: flex-start;
      gap: 2px;
    }

    .phone-status-wrapper {
      gap: 1px;
    }

    .phone-status-tag {
      font-size: 10px;
    }

    .phone-status-indicator {
      width: 14px;
      height: 14px;
      font-size: 9px;
    }

    // 移动端微信状态适配
    .wechat-container {
      flex-direction: column;
      align-items: flex-start;
      gap: 2px;
    }

    .wechat-status-wrapper {
      gap: 1px;
    }

    .wechat-status-tag {
      font-size: 10px;
    }

    .wechat-status-indicator {
      width: 14px;
      height: 14px;
      font-size: 9px;
    }

    // 移动端popover横向布局适配
    :deep(.horizontal-radio-group) {
      flex-direction: column !important; // 小屏幕时改为垂直布局
      gap: 6px !important;

      .arco-radio {
        flex: none !important;
        min-width: auto !important;
        text-align: left !important;
      }
    }
  }

  // 新增：下拉菜单危险选项样式
  :deep(.danger-option) {
    color: rgb(var(--danger-6));

    &:hover {
      background-color: var(--color-danger-light-1);
      color: rgb(var(--danger-6));
    }

    .arco-icon {
      color: rgb(var(--danger-6));
    }
  }

  // 新增：备注显示容器样式
  .remark-display-container {
    position: relative;
    display: flex;
    align-items: flex-start;
    gap: 4px;
    width: 100%;
    min-height: 20px;

    .remark-text-content {
      flex: 1;
      min-width: 0; // 确保文本可以正确截断

      // 保持换行格式
      :deep(.text-content) {
        white-space: pre-line;
        word-break: break-word;
      }
    }

    .remark-edit-icon {
      flex-shrink: 0;
      margin-top: 2px; // 微调图标位置
      opacity: 0.6;
      transition: opacity 0.2s ease;
      cursor: pointer;

      &:hover {
        opacity: 1;
        color: rgb(var(--primary-6));
      }
    }

    // 悬停时显示编辑图标
    &:hover .remark-edit-icon {
      opacity: 1;
    }
  }

  .action-buttons-heng {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    flex-wrap: wrap;
    max-width: 100%;
    .link-text {
      font-size: 12px;
    }
  }

  .cancellation-reason-text {
    font-size: 12px;
  }
</style>
