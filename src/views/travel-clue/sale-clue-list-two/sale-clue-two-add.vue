<template>
  <d-modal
    :visible="visible"
    unmount-on-close
    :simple="false"
    :title="`${formModel.id ? '编辑' : '新增'}二级线索`"
    :mask-closable="false"
    title-align="start"
    :ok-loading="okLoading"
    width="600px"
    :body-style="{ maxHeight: '80vh' }"
    @cancel="handleCancel"
    @ok="handleBeforeOk"
  >
    <a-form ref="formRef" class="mt-10" :model="formModel" auto-label-width>
      <a-form-item
        label="手机号"
        field="phone"
        :rules="{ validator: phoneValidator }"
      >
        <a-input
          v-model="formModel.phone"
          autocomplete="off"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item label="微信号">
        <a-input
          v-model="formModel.wechat"
          autocomplete="off"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item label="微信二维码">
        <upload-image-file
          v-model="formModel.wechat_qrcode"
          :draggable="true"
          :multiple="false"
          file-type="image/jpg, image/png, image/jpeg"
          list-type="picture"
          :limit="1"
          :send-params="{ type: 'clue_wechat' }"
          tip="支持拖拽和粘贴"
        >
        </upload-image-file>
      </a-form-item>
      <!-- <template v-if="!formModel.id"> -->
      <a-form-item label="旅游线路" field="area" :rules="requiredRule">
        <dict-select
          v-model="formModel.area"
          :data-list="dataCacheStore.lineList"
          label-key="line_name"
          value-key="line_name"
        />
      </a-form-item>
      <a-form-item label="成人人数">
        <a-input-number
          v-model="formModel.adult_num"
          :precision="0"
          :min="0"
          autocomplete="off"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item label="儿童人数">
        <a-input-number
          v-model="formModel.children_num"
          :precision="0"
          :min="0"
          autocomplete="off"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item label="大概出行日期">
        <a-input
          v-model="formModel.estimated_travel_date"
          autocomplete="off"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item label="来源媒体" field="platform" :rules="requiredRule">
        <dict-select
          v-model="formModel.platform"
          :data-list="contactWayListM"
        />
      </a-form-item>
      <!--<a-form-item label="来源内容">
          <a-input
            v-model="formModel.thread_content"
            allow-clear
            placeholder="来源内容"
          />
        </a-form-item>-->
      <a-form-item label="客服分配规则">
        <dict-select
          v-model="formModel.distribute_rule"
          :data-list="clueAssignM"
          :allow-clear="false"
        />
      </a-form-item>
      <!-- </template> -->
      <a-form-item label="客服">
        <request-select
          v-model="formModel.two_service_user_fs_id"
          request-url="/api/travel/allServiceUser"
          label-key="user_name"
          value-key="fs_user_id"
          @change="
            (val:any, item:any) => (formModel.two_service_user = item?.user_name || '')
          "
        />
      </a-form-item>
      <a-form-item label="备注">
        <a-textarea
          v-model="formModel.remark"
          :auto-size="{ minRows: 3, maxRows: 6 }"
          autocomplete="off"
          placeholder="请输入"
        />
      </a-form-item>

      <a-form-item label="线索类型" field="clue_type" :rules="requiredRule">
        <dict-select
          v-model="formModel.clue_type"
          :data-list="clueTypeListM"
          @change="
            (val) =>
              (formModel.clue_type_detail_text =
                val === '其他线索' ? '客户转介绍' : '')
          "
        />
      </a-form-item>
      <template v-if="formModel.clue_type">
        <a-form-item
          label="线索细分类型"
          field="clue_type_detail_text"
          :rules="requiredRule"
        >
          <request-select
            v-model="formModel.clue_type_detail_text"
            request-url="/api/thread/clueTypeDetailText"
            label-key="name"
            value-key="value"
            :send-params="{
              clue_type: formModel.clue_type,
              is_clue_statistics: true,
            }"
          />

          <!-- <dict-select
            v-if="formModel.clue_type === '公海线索'"
            v-model="formModel.clue_type_detail_text"
            :data-list="clueSeasTypeM"
          />
          <request-select
            v-else-if="formModel.clue_type === '内容号线索'"
            v-model="formModel.clue_type_detail_text"
            request-url="/api/contentAccount/list"
            label-key="account_name"
          />

          <request-select
            v-else-if="formModel.clue_type === '店铺线索'"
            v-model="formModel.clue_type_detail_text"
            request-url="/api/store/list"
            label-key="store_name"
          />

          <request-select
            v-else-if="formModel.clue_type === '直播账号线索'"
            v-model="formModel.clue_type_detail_text"
            request-url="/api/liveRoom/list"
            label-key="live_name"
          />
          <dict-select
            v-else-if="formModel.clue_type === '其他线索'"
            v-model="formModel.clue_type_detail_text"
            :data-list="clueDetailTypeM"
            :allow-clear="false"
          /> -->
        </a-form-item>
        <!-- 是否投流 -->
        <a-form-item
          v-if="
            ((formModel.clue_type === '内容号线索' ||
              formModel.clue_type === '直播账号线索') &&
              !userStore.roles.includes(3)) ||
            userStore.roles.includes(6)
          "
          label="是否投流"
          field="is_flow"
          :disabled="formModel.create_user_id === 100000"
          :rules="[
            {
              required: formModel.clue_type === '内容号线索',
              message: '请选择是否投流',
              trigger: 'change',
            },
          ]"
        >
          <dict-select v-model="formModel.is_flow" :data-list="isFlowM" />
        </a-form-item>
        <a-form-item
          v-if="formModel.clue_type === '其他线索'"
          label="原客户昵称及电话"
          field="clue_type_origin_customer_phone"
          :rules="requiredRule"
        >
          <request-select
            v-model="formModel.clue_type_origin_customer_phone"
            request-url="/api/thread/getCustomers"
            label-key="customer_name"
            value-key="phone"
            :get-label="
                (item:any) => `${item.customer_name || '无'}(${item.phone})`
              "
            @change="
                (val:any, item:any) =>
                  (formModel.clue_type_origin_customer_nickname =
                    item.customer_name || '')
              "
          />
        </a-form-item>
      </template>

      <a-form-item label="线索提供人" field="add_user_id" :rules="requiredRule">
        <request-select v-model="formModel.add_user_id" api="user" />
      </a-form-item>
      <a-form-item label="客户意向度">
        <dict-select
          v-model="formModel.customer_intentionality"
          :data-list="orderIntentionalityM"
          style="width: 100%"
        />
      </a-form-item>
    </a-form>
  </d-modal>
</template>

<script lang="ts" setup>
  import { computed, ref, unref } from 'vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import DModal from '@/components/d-modal/d-modal.vue';
  import { requiredRule } from '@/utils/util';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import {
    contactWayListM,
    orderIntentionalityM,
  } from '@/components/dict-select/dict-travel';
  import {
    clueAssignM,
    clueDetailTypeM,
    clueSeasTypeM,
    clueSuorceM,
    clueTypeListM,
    isFlowM,
  } from '@/components/dict-select/dict-clue';
  import RequestSelect from '@/components/select/request-select.vue';
  import { useDataCacheStore, useUserStore } from '@/store';
  import UploadImageFile from '@/components/upload-file/upload-image-file.vue';

  const emit = defineEmits(['refresh']);
  const formRef = ref<FormInstance>();
  const userStore = useUserStore();
  const generateFormModel = () => {
    return {
      id: '',
      phone: '',
      wechat: '',
      wechat_qrcode: '',
      adult_num: null,
      children_num: null,
      estimated_travel_date: '',
      platform: '',
      source: '',
      customer_intentionality: '',
      thread_content: '',
      distribute_rule: '线索补齐分配',
      two_service_user: '',
      two_service_user_fs_id: '',
      user_director_id: '',
      user_laterstage_id: '',
      user_operate_id: '',
      user_live_id: '',
      remark: '',
      area: '',

      clue_type: '',
      clue_type_detail_text: '' as any,
      clue_type_origin_customer_nickname: '',
      clue_type_origin_customer_phone: '',
      is_flow: null,

      add_user_id: null,
      create_user_id: null,
    };
  };
  const formModel = ref(generateFormModel());

  const visible = ref(false);
  const okLoading = ref(false);
  const dataCacheStore = useDataCacheStore();

  const show = (data: any) => {
    let initForm = generateFormModel();
    formModel.value = initForm;
    if (data) {
      Object.keys(initForm).forEach((key) => {
        // @ts-ignore
        formModel.value[key] =
          data[key] || initForm[key as keyof typeof initForm];
      });
      if (
        ['直播账号线索', '店铺线索', '内容号线索', '公海线索'].includes(
          formModel.value.clue_type
        )
      ) {
        let clue_type_detail_text = parseInt(
          formModel.value.clue_type_detail_text,
          10
        );
        if (!Number.isNaN(clue_type_detail_text)) {
          formModel.value.clue_type_detail_text = clue_type_detail_text;
        }
      }
      formModel.value.add_user_id = data.add_user_id;
    } else {
      formModel.value.add_user_id = userStore.id;
    }
    visible.value = true;
    okLoading.value = false;
  };

  // 验证手机号是否为1开头的11位数字
  function phoneValidator(value: any, callback: any) {
    if (value && !/^1[0-9]{10}$/.test(value)) {
      callback('手机号必须为1开头的11位数字');
    } else {
      callback();
    }
  }

  const handleCancel = () => {
    visible.value = false;
    formRef.value?.clearValidate();
  };
  const handleBeforeOk = async () => {
    const res = await formRef.value?.validate();
    if (!res) {
      if (formModel.value.phone && formModel.value.phone.length !== 11) {
        return Message.error('请输入正确格式的手机号');
      }
      if (
        !formModel.value.phone &&
        !formModel.value.wechat &&
        !formModel.value.wechat_qrcode
      ) {
        return Message.error('请输入手机号、微信号、微信二维码中的某一项');
      }
      if (!formModel.value.source) {
        formModel.value.source = '客服新增';
      }
      okLoading.value = true;
      request('/api/thread/addSales', {
        ...formModel.value,
      })
        .then(() => {
          emit('refresh');
          handleCancel();
        })
        .finally(() => {
          okLoading.value = false;
        });
    }
  };
  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>
