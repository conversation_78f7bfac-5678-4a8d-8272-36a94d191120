<template>
  <a-tree-select
    :model-value="modelValue"
    :data="treeData"
    :load-more="loadMore"
    multiple
    :max-tag-count="1"
    allow-clear
    placeholder="请选择"
    @update:model-value="valueChange"
  ></a-tree-select>
</template>

<script setup>
  import { onMounted, ref, watch } from 'vue';
  import {
    clueDetailTypeM,
    clueSeasTypeM,
    clueTypeListM,
  } from '@/components/dict-select/dict-clue';
  import request from '@/api/request';

  const emit = defineEmits(['update:modelValue']);
  const props = defineProps({
    modelValue: {
      type: [String, Array],
      default: '',
    },
  });

  // 判断是否为父节点（不包含 '___' 分隔符）
  const isParentNode = (value) => {
    return !value.includes('___');
  };

  // 判断是否为指定父节点的子节点
  const isChildOfParent = (childValue, parentValue) => {
    return childValue.startsWith(`${parentValue}___`);
  };

  // 获取子节点的父节点值
  const getParentValue = (childValue) => {
    const parts = childValue.split('___');
    return parts.length > 1 ? parts[0] : null;
  };

  // 处理父子节点互斥逻辑
  const handleMutualExclusion = (newValues, oldValues) => {
    if (!Array.isArray(newValues) || !Array.isArray(oldValues)) {
      return newValues;
    }

    // 找出新增的节点（本次操作选中的节点）
    const addedValues = newValues.filter((val) => !oldValues.includes(val));

    let resultValues = [...newValues];

    // 处理新增的节点
    addedValues.forEach((addedValue) => {
      if (isParentNode(addedValue)) {
        // 如果选中的是父节点，移除其所有子节点
        resultValues = resultValues.filter(
          (val) => !isChildOfParent(val, addedValue)
        );
      } else {
        // 如果选中的是子节点，移除其直接父节点
        const parentValue = getParentValue(addedValue);
        if (parentValue && resultValues.includes(parentValue)) {
          resultValues = resultValues.filter((val) => val !== parentValue);
        }
      }
    });

    return resultValues;
  };

  function valueChange(val) {
    // 实现父子节点互斥逻辑
    const processedVal = handleMutualExclusion(val, props.modelValue || []);
    emit('update:modelValue', processedVal);
  }

  const treeData = ref(
    clueTypeListM.map((item) => ({
      title: item.label,
      value: item.value,
      key: item.value,
      isLeaf: false,
    }))
  );
  const childrenConfig = {
    公海线索: {
      dataList: clueSeasTypeM,
    },
    内容号线索: {
      url: '/api/thread/clueTypeDetailText',
      titleKey: 'name',
      valueKey: 'value',
      sendParams: {
        clue_type: '内容号线索',
      },
    },
    店铺线索: {
      url: '/api/thread/clueTypeDetailText',
      titleKey: 'name',
      valueKey: 'value',
      sendParams: {
        clue_type: '店铺线索',
      },
    },
    直播账号线索: {
      url: '/api/thread/clueTypeDetailText',
      titleKey: 'name',
      valueKey: 'value',
      sendParams: {
        clue_type: '直播账号线索',
      },
    },
    其他线索: {
      dataList: clueDetailTypeM,
    },
  };

  const loadMore = (nodeData) => {
    const { key } = nodeData;
    nodeData.isLoading = true;
    return new Promise((resolve) => {
      let config = childrenConfig[key];
      if (config?.dataList) {
        nodeData.children = config.dataList.map((item) => ({
          title: item.label,
          value: `${key}___${item.value}`,
          key: `${key}___${item.value}`,
          isLeaf: true,
        }));
        nodeData.isLoading = false;
        resolve();
      } else if (config?.url) {
        request(config.url, config?.sendParams)
          .then((res) => {
            nodeData.children = (res.data.data || res.data).map((item) => ({
              title: item[config.titleKey],
              value: `${key}___${item[config.valueKey]}`,
              key: `${key}___${item[config.valueKey]}`,
              isLeaf: true,
            }));
            nodeData.isLeaf = nodeData.children.length === 0;
          })
          .finally(() => {
            resolve();
            nodeData.isLoading = false;
          });
      } else {
        debugger;
        request('/api/liveRoom/list', config?.sendParams)
          .then((res) => {
            nodeData.children = (res.data.data || res.data).map((item) => ({
              title: item.live_name,
              value: `${key}___${item.id}`,
              key: `${key}___${item.id}`,
              isLeaf: true,
            }));
            nodeData.isLeaf = nodeData.children.length === 0;
          })
          .finally(() => {
            resolve();
            nodeData.isLoading = false;
          });
      }
    });
  };

  watch(
    () => props.modelValue,
    (val) => {
      if (val?.length) {
        val.forEach((item) => {
          const [type, typeDetail] = item.split('___');
          let node = treeData.value.find(
            (currentItem) => currentItem.value === type
          );
          if (typeDetail && !node.children?.length && !node.isLoading) {
            loadMore(node);
          }
        });
      }
    },
    {
      immediate: true,
    }
  );
</script>
