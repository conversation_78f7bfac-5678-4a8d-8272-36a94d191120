# 外部用户权限控制测试说明

## 实现的功能

在 `sale-clue-list-two.vue` 文件中，我们为外部用户实现了以下权限控制：

### 1. 列显示限制
- **功能**: 外部用户只能看到特定的列，不能看到所有列
- **允许的列**:
  - 线索ID（不包含状态）
  - 手机号
  - 微信
  - 成人/儿童数量
  - 大概出行日期
  - 来源媒体
  - 旅游线路
  - 分配客服
  - 备注
  - 线索提供人
  - 线索类型
  - 线索细分类型
  - 客户意向度
  - 分配时间

### 2. 线索ID列特殊处理
- **功能**: 外部用户看到的线索ID不包含状态信息
- **实现方式**: 在模板中使用 `v-if="isExternalUser"` 条件渲染不同的内容

### 3. 搜索筛选项限制
- **功能**: 外部用户只能使用5个指定的搜索筛选项
- **允许的筛选项**:
  - 关键词搜索（手机/微信）
  - 来源媒体
  - 旅游线路
  - 线索类型
  - 客户意向度

### 4. 操作列权限控制
- **外部用户**: 可以看到操作列，但只显示"受理"和"作废"两个按钮
  - **受理按钮**: 仅当线索状态为"已分配"时显示，点击需要确认
  - **作废按钮**: 仅当线索状态不是"作废"时显示
- **内部用户**: 显示所有操作按钮（编辑、生成订单、受理、作废等）

### 5. 功能按钮限制
- **新增按钮**: 外部用户无法看到"新增二级线索"按钮
- **导出按钮**: 外部用户无法看到"导出"按钮
- **列选择器**: 外部用户可以看到列选择器，但只能选择允许的14个列
- **实现方式**: 使用 `v-permission-enhanced` 指令和 `filteredFieldsConfig` 计算属性

## 代码修改点

### 1. 导入权限 Hook
```typescript
import { usePermission } from '@/hooks/usePermission';

// 权限控制
const { isExternalUser } = usePermission();
```

### 2. 外部用户允许的列配置
```typescript
// 外部用户允许查看的列
const externalUserAllowedColumns = [
  'phone',
  'wechat',
  'adult_num',
  'estimated_travel_date',
  'platform',
  'area',
  'two_service_user',
  'remark',
  'add_user_name',
  'clue_type',
  'clue_type_detail_show',
  'customer_intentionality',
  'time_distribute_two'
];

// 为外部用户过滤列选择器的可选列
const filteredFieldsConfig = computed(() => {
  if (isExternalUser.value) {
    // 外部用户只能看到允许的列
    const filteredDataList = allFieldsConfig[0].dataList.filter(column =>
      externalUserAllowedColumns.includes(column.dataIndex)
    );
    return [{
      ...allFieldsConfig[0],
      dataList: filteredDataList,
      keys: filteredDataList.map(item => item.dataIndex)
    }];
  } else {
    // 内部用户看到所有列
    return allFieldsConfig;
  }
});
```

### 3. 修改 columns 计算属性
```typescript
const columns = computed(() => {
  const baseColumns = [
    {
      title: '线索ID',
      dataIndex: 'id',
      width: 100,
      align: 'center',
      fixed: 'left',
    },
  ];

  // 如果是外部用户，只显示允许的列
  if (isExternalUser.value) {
    const allowedColumns = allFieldsConfig[0].dataList.filter(column =>
      externalUserAllowedColumns.includes(column.dataIndex)
    );
    baseColumns.push(...allowedColumns);
  } else {
    // 内部用户显示所有配置的列
    baseColumns.push(...columnsConfig.value);

    // 内部用户显示操作列
    baseColumns.push({
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 200,
      fixed: 'right',
    });
  }

  return baseColumns;
});
```

### 4. 修改搜索筛选项
```typescript
// 如果是外部用户，只保留指定的筛选项
if (isExternalUser.value) {
  const allowedSearchFields = [
    'phone_or_wechat', // 关键词搜索（手机/微信）
    'platform', // 来源媒体
    'area', // 旅游线路
    'clue_type_custom', // 线索类型
    'customer_intentionality' // 客户意向度
  ];
  arr = arr.filter(item => allowedSearchFields.includes(item.field));
}
```

### 5. 修改线索ID列模板
```vue
<template #id="{ record }: TableColumnSlot">
  <!-- 外部用户只显示ID，不显示状态 -->
  <div v-if="isExternalUser" class="order-number">
    <span v-if="record.id" class="order-number-text">
      {{ record.id }}
    </span>
    <span v-else class="order-number-empty">-</span>
  </div>
  <!-- 内部用户显示ID和状态 -->
  <div v-else class="order-number-container">
    <!-- 完整的ID和状态显示 -->
  </div>
</template>
```

### 5. 修改操作列模板
```vue
<template #action="{ record }: TableColumnSlot">
  <!-- 外部用户显示受理和作废按钮 -->
  <div v-if="isExternalUser" class="action-buttons-container">
    <div class="action-buttons-wrapper">
      <!-- 受理按钮 -->
      <a-popconfirm
        v-if="record.status_two === '已分配'"
        content="确定受理吗？"
        @ok="shouliAction(record)"
      >
        <a-button
          type="text"
          status="success"
          size="mini"
          :loading="record.loading"
        >
          <template #icon>
            <icon-check />
          </template>
          受理
        </a-button>
      </a-popconfirm>

      <!-- 作废按钮 -->
      <a-button
        v-if="record.status_two !== '作废'"
        type="text"
        status="danger"
        size="mini"
        @click="cancelRef?.value?.show(record)"
      >
        <template #icon>
          <icon-close />
        </template>
        作废
      </a-button>
    </div>
  </div>
  <!-- 内部用户显示所有操作按钮 -->
  <div v-else class="action-buttons-container">
    <!-- 内部用户的所有操作按钮 -->
  </div>
</template>
```

## 测试方法

1. **内部用户测试**:
   - 登录内部用户账号 (out_user !== 1)
   - 访问二级线索列表页面
   - 应该能看到操作列和新增按钮

2. **外部用户测试**:
   - 登录外部用户账号 (out_user === 1)
   - 访问二级线索列表页面
   - 应该只看到指定的14个列
   - 验证列选择器功能（可以自定义显示哪些允许的列）
   - 验证搜索筛选项（只能使用5个指定的筛选项）
   - 验证操作列功能：
     - 对于状态为"已分配"的线索，显示"受理"按钮
     - 对于状态不是"作废"的线索，显示"作废"按钮
     - 点击"受理"按钮，弹出确认对话框
     - 点击"作废"按钮，打开作废原因填写弹窗
     - 不显示其他操作按钮（编辑、生成订单等）
   - 验证无法使用新增和导出功能

## 权限控制逻辑

- **内部用户**:
  - 可以看到所有列（通过列选择器自定义）
  - 线索ID列包含状态信息
  - 可以使用所有功能按钮（操作列、新增、导出、列选择器）

- **外部用户**:
  - 只能看到14个指定的列
  - 可以使用列选择器，但只能选择这14个允许的列
  - 只能使用5个指定的搜索筛选项
  - 可以看到操作列，但只能使用"受理"和"作废"两个操作
  - 无法使用新增和导出功能

## 外部用户可见的列清单

1. 线索ID（仅ID，无状态）
2. 手机号
3. 微信
4. 成人/儿童数量
5. 大概出行日期
6. 来源媒体
7. 旅游线路
8. 分配客服
9. 备注
10. 线索提供人
11. 线索类型
12. 线索细分类型
13. 客户意向度
14. 分配时间

这样的设计确保了外部用户只能查看必要的线索信息，而不能进行任何修改操作，同时限制了他们能看到的数据范围，符合外部用户权限控制的需求。
