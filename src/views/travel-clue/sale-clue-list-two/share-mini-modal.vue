<template>
  <d-modal
    :visible="visible"
    unmount-on-close
    :simple="false"
    title="分享"
    :mask-closable="false"
    title-align="start"
    width="600px"
    hide-cancel
    @ok="handleBeforeOk"
    @cancel="handleCancel"
  >
    <a-spin :loading="loading" class="image-buf">
      <img width="300" :src="'data:image/png;base64,' + img" />
      <a-typography-paragraph copyable>
        {{ url_link }}
      </a-typography-paragraph>
    </a-spin>
  </d-modal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';

  const visible = ref(false);
  const loading = ref(false);
  const img = ref('');
  const url_link = ref('');

  function getShareInfo(product_id: string, clue_id: string) {
    loading.value = true;
    request('/api/miniApp/miniAppCode', {
      product_id,
      clue_id,
    })
      .then((res) => {
        img.value = res.data.qr;
        url_link.value = res.data.link;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const show = async (product_id: string, clue_id: string) => {
    visible.value = true;
    getShareInfo(product_id, clue_id);
  };

  const handleCancel = () => {
    visible.value = false;
  };
  const handleBeforeOk = async () => {
    handleCancel();
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped>
  .image-buf {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .arco-typography {
    margin-top: 30px;
  }
</style>
