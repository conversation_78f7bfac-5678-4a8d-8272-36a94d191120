<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        placeholder="请输入公海线索ID"
        :search-rules="searchRules"
        :base-search-rules="baseSearchRules"
        @hand-submit="handleSubmit"
      ></search-form>

      <div class="table-card-header mt-10">
        <div>
          <a-space>
            <span class="title-box">
              已选择
              <a-tag>
                {{ rowSelection.selectedRowKeys.length }}
              </a-tag>
              个订单
            </span>
            <icon-close-circle class="cur-por" @click="resetHandler" />
            <a-button
              :disabled="!rowSelection.selectedRows.length"
              type="primary"
              :loading="sending"
              @click="sendMsg"
            >
              发送私信
            </a-button>
          </a-space>
        </div>
        <a-space>
          <columns-select
            :the-field-map="allFieldsConfig"
            :need-format-column="false"
            :send-params="{ type: 'travel_all_clue' }"
            :default-columns="columnsConfig.map((item) => item.dataIndex)"
            @select-columns-ok="setColumns"
          ></columns-select>
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :send-params="tableParams"
        :row-selection="rowSelection"
        row-key="tread_id"
        :data-handle="dataHandle"
        :sort-keys="[
          'comment_time',
          'send_msg_time',
          'real_send_msg_time',
          'add_time',
        ]"
        :scroll-percent="scrollPercent"
        @select-change="selectionChange"
      >
        <template #action="{ record }: TableColumnSlot">
          <a-spin :loading="record.loading">
            <a-space>
              <a-link @click="showEditFn(record)"> <icon-edit />编辑 </a-link>
            </a-space>
          </a-spin>
        </template>
        <template #video_url="{ record }: TableColumnSlot">
          <a-link
            v-if="record.video_url"
            :href="record.video_url"
            target="_blank"
          >
            {{ record.video_url }}
          </a-link>
          <span v-else>-</span>
        </template>
        <template #send_msg="{ record }: TableColumnSlot">
          <span style="white-space: pre-line">
            {{ record.send_msg }}
          </span>
        </template>
        <template #account_id="{ record }: TableColumnSlot">
          <template v-if="record.media_sec_uid">
            <a-link
              v-if="record.platform === '小红书'"
              :href="`https://www.xiaohongshu.com/user/profile/${record.media_sec_uid}`"
              target="_blank"
            >
              {{ record.account_id }}
            </a-link>
            <a-link
              v-else
              :href="`https://www.douyin.com/user/${record.media_sec_uid}`"
              target="_blank"
            >
              {{ record.account_id }}
            </a-link>
          </template>
          <span v-else>{{ record.account_id }}</span>
        </template>
        <template #thread_content="{ record }: TableColumnSlot">
          <a-tooltip
            v-if="record.thread_content?.length > 50"
            :content="record.thread_content.slice(0, 400)"
          >
            <span>{{ record.thread_content.slice(0, 50) }}...</span>
          </a-tooltip>
          <span v-else>{{ record.thread_content }}</span>
        </template>
      </base-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';

  import { ref, reactive, onBeforeMount, nextTick, computed, h } from 'vue';
  import request from '@/api/request';
  import {
    getDictTxtRender,
    moneyFormatShow,
  } from '@/utils/table-utils/columns-config';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import { Message } from '@arco-design/web-vue';
  import { TableColumnSlot } from '@/global';
  import {
    clueSuorceM,
    intentionalityIntentM,
    msgStatusM,
  } from '@/components/dict-select/dict-clue';
  import {
    contactWayListM,
    yesOrNo,
  } from '@/components/dict-select/dict-travel';
  import { colors } from '@/components/dict-select/dict-common';
  import { useDataCacheStore } from '@/store';
  import ColumnsSelect from '@/components/columns-select/columns-select.vue';
  import { cloneDeep } from 'lodash';

  const generateFormModel = () => {
    return {
      tread_id: null,
      platform: null,
      source: '',
      from_setting: '',
      account_id: null,
      thread_content: '',
      is_had_chat: '',
      area: '',
      intentionality_intent: '',
      comment_time: [],
      send_msg_time: [],
      real_send_msg_time: [],
      add_time: [],
    };
  };
  const loading = ref(false);
  const sending = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());
  const dataCacheStore = useDataCacheStore();

  const baseSearchRules: any = ref([
    {
      field: 'tread_id',
      label: '公海线索ID',
      value: null,
      symbol: '是',
    },
  ]);
  const searchRules = computed(() => [
    {
      field: 'area',
      label: '旅游线路',
      value: null,
      component_name: 'dict-select',
      alwaysShow: true,
      attr: {
        dataList: dataCacheStore.lineList,
        labelKey: 'line_name',
        valueKey: 'line_name',
      },
    },
    {
      field: 'platform',
      label: '来源媒体',
      value: null,
      component_name: 'dict-select',
      alwaysShow: true,
      attr: {
        dataList: contactWayListM,
      },
    },
    {
      field: 'source',
      label: '来源类型',
      value: null,
      component_name: 'dict-select',
      attr: {
        dataList: clueSuorceM,
      },
    },
    {
      field: 'from_setting',
      label: '来源详情',
      value: null,
      component_name: 'a-input',
      attr: {
        placeholder: '来源详情',
        allowClear: true,
      },
    },
    {
      field: 'account_id',
      label: '用户账号ID',
      value: null,
      component_name: 'a-input',
      attr: {
        placeholder: '用户账号ID',
        allowClear: true,
      },
    },
    {
      field: 'thread_content',
      label: '线索内容',
      value: null,
      component_name: 'a-input',
      attr: {
        placeholder: '线索内容',
        allowClear: true,
      },
    },
    {
      field: 'is_had_chat',
      label: '是否发送私信',
      value: null,
      alwaysShow: true,
      component_name: 'dict-select',
      attr: {
        dataList: msgStatusM,
      },
    },
    {
      field: 'intentionality_intent',
      label: 'AI意向等级',
      value: null,
      component_name: 'dict-select',
      attr: {
        dataList: intentionalityIntentM,
      },
    },
    {
      field: 'comment_time',
      label: '评论时间',
      value: null,
      component_name: 'a-range-picker',
    },
    {
      field: 'send_msg_time',
      label: '提交发送私信时间',
      value: null,
      alwaysShow: true,
      component_name: 'a-range-picker',
    },
    {
      field: 'real_send_msg_time',
      label: '实际发送私信时间',
      value: null,
      component_name: 'a-range-picker',
    },
    {
      field: 'add_time',
      label: '入库时间',
      value: null,
      alwaysShow: true,
      component_name: 'a-range-picker',
    },
  ]);

  const getList = async (data: any) => {
    return request('/api/thread/seasList', {
      ...data,
    });
  };

  const editRef = ref();
  function showEditFn(record?: any) {
    editRef.value?.show(record, {});
  }

  const allFieldsConfig = [
    {
      title: '全部字段',
      dataIndex: '全部字段',
      keys: [] as string[],
      dataList: [
        {
          title: '来源媒体',
          dataIndex: 'platform',
        },
        {
          title: '旅游线路',
          dataIndex: 'area',
        },
        {
          title: '来源类型',
          dataIndex: 'source',
        },
        {
          title: '来源详情',
          dataIndex: 'from_setting',
        },
        {
          title: '用户账号ID',
          dataIndex: 'account_id',
        },
        {
          title: '用户账号昵称',
          dataIndex: 'account_name',
        },
        {
          title: '线索内容',
          dataIndex: 'thread_content',
          width: 300,
        },
        {
          title: '自定义模版消息内容',
          dataIndex: 'send_msg',
          width: 300,
        },
        {
          title: 'AI意向等级',
          dataIndex: 'intentionality_intent',
        },
        {
          title: '是否发送私信',
          dataIndex: 'is_had_chat',
          render: ({ record }: TableColumnSlot) => {
            let color = colors.primary;
            if (record.is_had_chat === '是') {
              color = colors.success;
            } else if (['否', '发送失败'].includes(record.is_had_chat)) {
              color = colors.error;
            }
            return h(
              'span',
              {
                style: {
                  color,
                },
              },
              record.is_had_chat
            );
          },
        },
        {
          title: '发送设备',
          dataIndex: 'send_device_id',
        },
        {
          title: '发送失败原因',
          dataIndex: 'send_msg_error',
          width: 200,
        },
        {
          title: '视频链接',
          dataIndex: 'video_url',
          width: 200,
        },
        {
          title: '评论时间',
          dataIndex: 'comment_time',
          width: 200,
        },
        {
          title: '提交发送私信时间',
          dataIndex: 'send_msg_time',
          width: 200,
        },
        {
          title: '实际发送私信时间',
          dataIndex: 'real_send_msg_time',
          width: 200,
        },
        {
          title: '入库时间',
          dataIndex: 'add_time',
          width: 200,
        },
        // {
        //  title: '更新时间',
        //  dataIndex: 'update_time',
        // },

        // {
        //  title: '操作',
        //  dataIndex: 'action',
        //  fixed: 'right',
        // },
      ],
    },
  ];
  allFieldsConfig.forEach((item) => {
    item.keys = item.dataList.map((citem) => {
      // @ts-ignore
      citem.key = citem.dataIndex;
      return citem.dataIndex;
    });
  });
  const columnsConfig = ref(cloneDeep(allFieldsConfig[0].dataList));
  const columns = computed(() => [
    {
      title: '公海线索ID',
      dataIndex: 'tread_id',
      width: 140,
    },
    ...columnsConfig.value,
  ]);
  const scrollPercent = computed(() => ({
    maxHeight: '67vh',
    x: columns.value.length * 170,
  }));

  // table渲染完成回调
  const changeHandler = (tableData: any) => {
    loading.value = false;
  };

  const tableParams = computed(() => ({
    ...formModel,
  }));

  // 数据选择相关
  let rowSelection = reactive({
    type: 'checkbox',
    fixed: true,
    width: 30,
    onlyCurrent: true,
    showCheckedAll: true,
    selectedRowKeys: [] as any[],
    selectedRows: [] as any[],
  });
  const selectionChange = (selectedRowKeys: any[], selectedRows: any[]) => {
    rowSelection.selectedRowKeys = selectedRowKeys.filter((item: any) => item);
    rowSelection.selectedRows = selectedRows;
  };

  // 重置
  const resetHandler = () => {
    rowSelection.selectedRowKeys = [];
    rowSelection.selectedRows = [];
  };
  function dataHandle(list: any[]) {
    resetHandler();
    return list.map((item) => ({
      ...item,
      disabled: item.is_had_chat !== '否',
    }));
  }

  function sendMsg() {
    sending.value = true;
    request('/api/thread/sendDYMsg', {
      tread_ids: rowSelection.selectedRows
        .filter((item: any) => item.is_had_chat === '否')
        .map((item: any) => item.tread_id),
    })
      .then(() => {
        Message.success('操作成功');
        theTable.value?.fetchData();
      })
      .finally(() => {
        sending.value = false;
      });
  }

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function setColumns(val: any[]) {
    columnsConfig.value = val.filter((item) =>
      allFieldsConfig.some((citem) => citem.keys.includes(item.dataIndex))
    );
    handleSubmit();
  }

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less"></style>
