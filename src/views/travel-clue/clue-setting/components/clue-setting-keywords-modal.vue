<template>
  <a-modal
    :visible="visible"
    width="800px"
    :title="`${getText(replayTemplateTypeM, sendParams.type)}配置`"
    :body-style="{ maxHeight: '80vh' }"
    :ok-loading="loading"
    @ok="sendInfo"
    @cancel="visible = false"
  >
    <a-form ref="formRef" :model="formModel">
      <a-form-item label="词组名称" :rules="requiredRule" field="title">
        <a-input
          v-model="formModel.title"
          placeholder="请输入词组名称"
          allow-clear
        />
      </a-form-item>
      <a-form-item :label="getText(replayTemplateTypeM, sendParams.type)">
        <a-space wrap>
          <a-tag
            v-for="(tag, index) of formModel.keywords"
            :key="tag"
            size="large"
            color="arcoblue"
            closable
            @close="handleRemove(index)"
          >
            {{ tag.account_name || tag }}
          </a-tag>
          <a-input-group v-if="showInput">
            <a-input
              ref="inputRef"
              v-model.trim="inputVal"
              :style="{ width: '500px' }"
              allow-clear
              placeholder="支持一次录入多个，使用分号；分隔"
              @keyup.enter="handleAdd"
              @blur="handleAdd"
            />
            <a-button type="primary" @click="handleAdd">
              <icon-check />
            </a-button>
          </a-input-group>
          <a-tag
            v-else
            size="large"
            color="arcoblue"
            :style="{
              cursor: 'pointer',
            }"
            @click="handleEdit"
          >
            <template #icon>
              <icon-plus />
            </template>
            添加
          </a-tag>
        </a-space>
      </a-form-item>
      <a-form-item label="公海私信模板">
        <div class="df fd-cl w100p">
          <a-textarea
            ref="threadSeasRef"
            v-model="formModel.reply_template_thread_seas"
            placeholder="请输入模版内容"
            :auto-size="{ minRows: 3 }"
          />
          <a-space wrap>
            <span>插入通配符</span>
            <a-button
              v-for="item in replayTemplateM"
              :key="item.value"
              type="text"
              size="mini"
              @click="
                insertName(
                  threadSeasRef,
                  'reply_template_thread_seas',
                  item.value
                )
              "
            >
              +{{ item.label }}
            </a-button>
          </a-space>
        </div>
      </a-form-item>
      <a-form-item label="一级线索回复模板">
        <div class="df fd-cl w100p">
          <a-textarea
            ref="oneThreadRef"
            v-model="formModel.reply_template_one_thread"
            placeholder="请输入模版内容"
            :auto-size="{ minRows: 3 }"
          />
          <a-space wrap>
            <span>插入通配符</span>
            <a-button
              v-for="item in replayTemplateM"
              :key="item.value"
              type="text"
              size="mini"
              @click="
                insertName(
                  oneThreadRef,
                  'reply_template_one_thread',
                  item.value
                )
              "
            >
              +{{ item.label }}
            </a-button>
          </a-space>
        </div>
      </a-form-item>
      <a-form-item label="转二级结束语模板">
        <div class="df fd-cl w100p">
          <a-textarea
            ref="getContactRef"
            v-model="formModel.reply_template_get_contact"
            placeholder="请输入模版内容"
            :auto-size="{ minRows: 3 }"
          />
          <a-space wrap>
            <span>插入通配符</span>
            <a-button
              v-for="item in replayTemplateM"
              :key="item.value"
              type="text"
              size="mini"
              @click="
                insertName(
                  getContactRef,
                  'reply_template_get_contact',
                  item.value
                )
              "
            >
              +{{ item.label }}
            </a-button>
          </a-space>
        </div>
      </a-form-item>
      <a-form-item label="挽单模板">
        <div class="df fd-cl w100p">
          <a-textarea
            ref="rescueOrderRef"
            v-model="formModel.reply_template_rescue_order"
            placeholder="请输入模版内容"
            :auto-size="{ minRows: 3 }"
          />
          <a-space wrap>
            <span>插入通配符</span>
            <a-button
              v-for="item in replayTemplateM"
              :key="item.value"
              type="text"
              size="mini"
              @click="
                insertName(
                  rescueOrderRef,
                  'reply_template_rescue_order',
                  item.value
                )
              "
            >
              +{{ item.label }}
            </a-button>
          </a-space>
        </div>
      </a-form-item>
      <a-form-item label="状态">
        <a-switch
          :model-value="formModel.state === 1"
          @change="(val) => (formModel.state = val ? 1 : -1)"
        />
      </a-form-item>
    </a-form>
    <clue-setting-account-save ref="accountRef" @save="handleAccountAdd" />
    <clue-setting-liveroom-save ref="liveroomRef" @save="handleAccountAdd" />
  </a-modal>
</template>

<script setup lang="ts">
  import { nextTick, PropType, ref } from 'vue';
  import { requiredRule, setCaretPosition } from '@/utils/util';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { cloneDeep, uniq } from 'lodash';
  import {
    replayTemplateM,
    replayTemplateTypeM,
  } from '@/components/dict-select/dict-clue';
  import { getText } from '@/components/dict-select/dict-util';
  import ClueSettingAccountSave from '@/views/travel-clue/clue-setting/components/clue-setting-account-save.vue';
  import ClueSettingLiveroomSave from '@/views/travel-clue/clue-setting/components/clue-setting-liveroom-save.vue';

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const accountRef = ref();
  const liveroomRef = ref();
  const emits = defineEmits(['save']);
  const props = defineProps({
    sendParams: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
  });

  function defaultForm() {
    return {
      title: '',
      keywords: [] as any[],
      reply_template_thread_seas: '',
      reply_template_one_thread: '',
      reply_template_get_contact: '',
      reply_template_rescue_order: '',
      state: -1,
      id: null,
    };
  }
  const formModel = ref(defaultForm());
  const inputRef = ref();
  const showInput = ref(false);
  const inputVal = ref('');
  const threadSeasRef = ref();
  const oneThreadRef = ref();
  const getContactRef = ref();
  const rescueOrderRef = ref();

  function insertName(
    iptRef: any,
    key:
      | 'reply_template_thread_seas'
      | 'reply_template_one_thread'
      | 'reply_template_get_contact'
      | 'reply_template_rescue_order',
    name: string
  ) {
    let newName: string = formModel.value[key];
    let iptDom = iptRef?.$el.querySelector('textarea');
    let index = iptDom.selectionStart || newName?.length || 0;
    if (!newName) {
      newName = name;
    } else {
      newName = `${newName.slice(0, index)}${name}${newName.slice(index)}`;
    }
    formModel.value[key] = newName;
    setCaretPosition(iptDom, index + name.length);
  }
  const handleEdit = () => {
    switch (props.sendParams.type) {
      case 'tk_account':
        accountRef.value?.show();
        break;
      case 'live_room':
        liveroomRef.value?.show();
        break;
      default:
        showInput.value = true;
        nextTick(() => {
          inputRef.value?.focus();
        });
        break;
    }
  };
  const handleAdd = () => {
    if (inputVal.value) {
      let keywords = inputVal.value.replaceAll('；', ';').split(';');
      formModel.value.keywords = uniq([
        ...formModel.value.keywords,
        ...keywords,
      ]);
      inputVal.value = '';
    }
    showInput.value = false;
  };
  const handleAccountAdd = (item: any) => {
    formModel.value.keywords.push(cloneDeep(item));
  };

  const handleRemove = (index: number) => {
    formModel.value.keywords.splice(index, 1);
  };

  // 获取配置信息
  function show(data?: any) {
    formRef.value.clearValidate();
    formModel.value = defaultForm();
    let initForm = defaultForm();
    formModel.value = initForm;
    if (data) {
      Object.keys(initForm).forEach((key) => {
        // @ts-ignore
        formModel.value[key] =
          data[key] || initForm[key as keyof typeof initForm];
      });
    }

    visible.value = true;
  }
  function sendInfo() {
    formModel.value.keywords = uniq(formModel.value.keywords);
    if (!formModel.value.keywords.length) {
      Message.error('请填写关键词');
    } else {
      formRef.value.validate((err: any) => {
        if (!err) {
          loading.value = true;
          request('/api/thread/baseSettingKeywordsSave', {
            ...props.sendParams,
            ...formModel.value,
          })
            .then(() => {
              Message.success('操作成功');
              visible.value = false;
              emits('save');
            })
            .finally(() => {
              loading.value = false;
            });
        }
      });
    }
  }
  defineExpose({
    show,
  });
</script>

<style scoped lang="less"></style>
