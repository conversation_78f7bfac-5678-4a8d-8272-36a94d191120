<template>
  <d-modal
    :visible="visible"
    width="800px"
    :title="`${formModel.id ? '编辑' : '添加'}图片配置`"
    :body-style="{ maxHeight: '80vh' }"
    :ok-loading="loading"
    @ok="sendInfo"
    @cancel="visible = false"
  >
    <a-form ref="formRef" class="form-box" :model="formModel">
      <a-form-item
        :label="getText(replayTemplateTypeM, sendParams.type)"
        field="selectList"
        :rules="requiredRuleArr"
      >
        <a-transfer
          v-model="formModel.selectList"
          :data="dataList"
          show-select-all
          simple
          :title="['列表', '已选择']"
        />
      </a-form-item>
      <a-form-item label="文件夹" field="dir_id" :rules="requiredRule">
        <request-select
          v-model="formModel.dir_id"
          :send-params="dirParams"
          value-key="id"
          label-key="dir_name"
          request-url="/api/material/imageDirList"
          :get-data-list="(arr) => arr?.children || []"
          :allow-clear="false"
        ></request-select>
      </a-form-item>
    </a-form>
  </d-modal>
</template>

<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { requiredRule, requiredRuleArr } from '@/utils/util';
  import RequestSelect from '@/components/select/request-select.vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import { getText } from '@/components/dict-select/dict-util';
  import { replayTemplateTypeM } from '@/components/dict-select/dict-clue';
  import { cloneDeep } from 'lodash';

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const emits = defineEmits(['save']);
  const props = defineProps({
    sendParams: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
  });

  function defaultForm() {
    return {
      id: null,
      setting_keywords_id: null,
      dir_id: null,
      keywords: [] as any[],
      selectList: [] as string[],
    };
  }
  const formModel = ref(defaultForm());

  const dirParams = computed(() => ({
    area: props.sendParams.area,
  }));
  const dataList = computed(() =>
    props.sendParams.keywords.map((item: any) => {
      if (props.sendParams.type !== 'keyword') {
        return {
          ...item,
          label: item.account_name,
          value: item.url,
        };
      }
      return {
        label: item,
        value: item,
      };
    })
  );

  function show(data?: any) {
    formRef.value?.clearValidate();
    formModel.value = defaultForm();
    let initForm = defaultForm();
    formModel.value = initForm;
    if (data) {
      Object.keys(initForm).forEach((key) => {
        // @ts-ignore
        formModel.value[key] =
          data[key] || initForm[key as keyof typeof initForm];
      });
      if (props.sendParams.type !== 'keyword') {
        formModel.value.selectList = data.keywords.map((item: any) => item.url);
      } else {
        formModel.value.selectList = data.keywords;
      }
    }

    visible.value = true;
  }
  function sendInfo() {
    formRef.value.validate((err: any) => {
      if (!err) {
        if (props.sendParams.type !== 'keyword') {
          formModel.value.keywords = dataList.value.filter((item: any) =>
            formModel.value.selectList.includes(item.value)
          );
        } else {
          formModel.value.keywords = cloneDeep(formModel.value.selectList);
        }
        loading.value = true;
        request('/api/thread/saveKeywordGroupImageDir', {
          ...formModel.value,
          setting_keywords_id:
            formModel.value.setting_keywords_id ||
            props.sendParams.setting_keywords_id,
        })
          .then(() => {
            Message.success('操作成功');
            visible.value = false;
            emits('save');
          })
          .finally(() => {
            loading.value = false;
          });
      }
    });
  }
  defineExpose({
    show,
  });
</script>

<style scoped lang="less">
  .form-box {
    :deep(.arco-transfer-view) {
      height: 380px;
      user-select: none;
    }
    :deep(.arco-transfer-view-header-title) {
      user-select: none;
    }
    :deep(.arco-transfer-list-item-content) {
      user-select: none;
    }
    :deep(.arco-empty-description) {
      user-select: none;
    }
  }
</style>
