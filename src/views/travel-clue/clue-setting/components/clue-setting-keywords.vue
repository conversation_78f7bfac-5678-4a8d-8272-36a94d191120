<template>
  <div class="p-10">
    <div class="jc-sb">
      <a-space>
        <dict-radio
          v-model="formModel.state"
          :disabled="loading"
          :data-list="clueConfigStateM"
        />
      </a-space>
      <a-space>
        <a-button type="primary" @click="showEdit()">
          <template #icon>
            <icon-plus />
          </template>
          新增
        </a-button>
      </a-space>
    </div>
    <base-table
      ref="tableRef"
      v-model:loading="loading"
      class="mt-10"
      :scroll-percent="{ x: 1200 }"
      :columns-config="columns"
      :send-params="tableParams"
      :data-config="dataConfig"
    >
      <template #keywords="{ record }: TableColumnSlot">
        <template v-if="record.keywords?.length">
          <component
            :is="record.keywords.length > 8 ? Tooltip : 'div'"
            :content="
              record.keywords
                .map((item) => item?.account_name || item)
                .join('，')
            "
            :content-style="{ maxWidth: '600px' }"
          >
            <a-space wrap size="mini">
              <a-tag
                v-for="item in record.keywords.slice(0, 8)"
                :key="item"
                color="blue"
              >
                {{ item?.account_name || item }}
              </a-tag>
              <a-tag v-if="record.keywords.length > 8" color="blue">
                等共{{ record.keywords.length }}个
              </a-tag>
            </a-space>
          </component>
        </template>
        <span v-else>-</span>
      </template>
      <template #state="{ record }: TableColumnSlot">
        <a-switch
          :loading="record.loading"
          :model-value="record.state === 1"
          @change="updateState(record)"
        />
      </template>
      <template #action="{ record }: TableColumnSlot">
        <a-space>
          <a-link @click="showEdit(record)"> 编辑 </a-link>
          <a-popconfirm
            trigger="click"
            :content="`确定复制【${record.title}】吗?`"
            @ok="copyAction(record)"
          >
            <a-link> 复制 </a-link>
          </a-popconfirm>
        </a-space>
      </template>
      <template #reply_template_thread_seas="{ record }: TableColumnSlot">
        <div>
          <span>{{ record.reply_template_thread_seas }}</span>
          <a-link
            @click="
              aiKeywordsParams.setting_keywords_id = record.id;
              aiKeywordsParams.template_type = 'thread_seas';
              aiRef?.show();
            "
          >
            AI润色
          </a-link>
        </div>
      </template>
      <template #reply_template_one_thread="{ record }: TableColumnSlot">
        <div>
          <span>{{ record.reply_template_one_thread }}</span>
          <a-link
            @click="
              aiKeywordsParams.setting_keywords_id = record.id;
              aiKeywordsParams.template_type = 'one_thread';
              aiRef?.show();
            "
          >
            AI润色
          </a-link>
        </div>
      </template>
      <template #reply_template_get_contact="{ record }: TableColumnSlot">
        <div>
          <span>{{ record.reply_template_get_contact }}</span>
          <a-link
            @click="
              aiKeywordsParams.setting_keywords_id = record.id;
              aiKeywordsParams.template_type = 'get_contact';
              aiRef?.show();
            "
          >
            AI润色
          </a-link>
        </div>
      </template>
      <template #reply_template_rescue_order="{ record }: TableColumnSlot">
        <div>
          <span>{{ record.reply_template_rescue_order }}</span>
          <a-link
            @click="
              aiKeywordsParams.setting_keywords_id = record.id;
              aiKeywordsParams.template_type = 'rescue_order';
              aiRef?.show();
            "
          >
            AI润色
          </a-link>
        </div>
      </template>
      <template #imgs="{ record }: TableColumnSlot">
        <div>
          <a-link
            @click="imgRef?.show({ ...record, setting_keywords_id: record.id })"
          >
            配置
          </a-link>
        </div>
      </template>
    </base-table>
    <clue-setting-keywords-modal
      ref="saveRef"
      :send-params="tableParams"
      @save="refreshData"
    />
    <clue-setting-keywords-ai-modal
      ref="aiRef"
      :send-params="aiKeywordsParams"
    />
    <clue-setting-image-modal ref="imgRef" :send-params="tableParams" />
  </div>
</template>

<script setup lang="ts">
  import request from '@/api/request';
  import { computed, nextTick, ref, watch } from 'vue';
  import { Message, Tooltip } from '@arco-design/web-vue';
  import { getText } from '@/components/dict-select/dict-util';
  import {
    clueConfigStateM,
    replayTemplateTypeM,
  } from '@/components/dict-select/dict-clue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import ClueSettingKeywordsModal from './clue-setting-keywords-modal.vue';
  import ClueSettingKeywordsAiModal from './clue-setting-keywords-ai-modal.vue';
  import ClueSettingImageModal from './clue-setting-image-modal.vue';

  const props = defineProps({
    sendParams: {
      type: Object,
      default: () => ({}),
    },
    platform: {
      type: String,
      default: '',
    },
    area: {
      type: String,
      default: '',
    },
  });
  const formModel = ref({
    state: 1,
  });
  const columns = computed(() => [
    { title: '词组名称', dataIndex: 'title', width: 150, fixed: 'left' },
    {
      title: getText(replayTemplateTypeM, props.sendParams.type),
      dataIndex: 'keywords',
    },
    { title: '状态', dataIndex: 'state', width: 120 },
    { title: '公海私信模板', dataIndex: 'reply_template_thread_seas' },
    { title: '一级线索回复模板', dataIndex: 'reply_template_one_thread' },
    { title: '转二级结束语模板', dataIndex: 'reply_template_get_contact' },
    { title: '挽单模板', dataIndex: 'reply_template_rescue_order' },
    { title: '图片配置', dataIndex: 'imgs' },
    { title: '操作', dataIndex: 'action', width: 120, fixed: 'right' },
  ]);
  const tableRef = ref();
  const saveRef = ref();
  const aiRef = ref();
  const imgRef = ref();
  const loading = ref(false);

  const aiKeywordsParams = ref({});

  const tableParams = computed(() => ({
    ...props.sendParams,
    ...formModel.value,
  }));

  function dataConfig(params: any) {
    return request('/api/thread/getBaseSettingKeywords', params);
  }

  function refreshData() {
    tableRef.value?.fetchData();
  }

  watch(
    tableParams,
    (val, oldValue) => {
      if (JSON.stringify(val) !== JSON.stringify(oldValue)) {
        nextTick(() => {
          refreshData();
        });
      }
    },
    {
      deep: true,
    }
  );

  function showEdit(record?: any) {
    saveRef.value?.show(record);
  }

  function updateState(record: any) {
    record.loading = true;
    request('/api/thread/baseSettingKeywordsSave', {
      ...record,
      state: record.state === 1 ? -1 : 1,
    })
      .then((res) => {
        record.state = res.data.state || record.state;
      })
      .finally(() => {
        record.loading = false;
      });
  }
  function copyAction(record: any) {
    let loadingModal = Message.loading('复制中...');
    request('/api/thread/baseSettingKeywordsSave', {
      ...record,
      title: `${record.title}_copy`,
      state: -1,
      id: undefined,
    })
      .then((res) => {
        refreshData();
      })
      .finally(() => {
        record.loading = false;
        loadingModal.close();
      });
  }
</script>

<style scoped lang="less"></style>
