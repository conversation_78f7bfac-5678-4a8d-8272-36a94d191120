<template>
  <div>
    <a-card class="mb-10">
      <div class="jc-sb">
        <dict-radio
          v-model="config.area"
          :data-list="dataCacheStore.lineList"
          label-key="line_name"
          value-key="line_name"
          @change="getConfig()"
        />
        <!-- <a-switch
          checked-text="开启"
          un-checked-text="关闭"
          :model-value="config.state === 1"
          @change="(val) => saveAction({ state: val ? 1 : -1 })"
        />-->
      </div>
    </a-card>
    <a-row class="mb-10" :gutter="10" align="stretch">
      <a-col :span="12">
        <a-card title="一级线索自动回复与流转配置">
          <a-form :model="config" auto-label-width>
            <a-form-item label="接粉专员状态">
              <a-radio-group
                v-model="config.service_user_status"
                @change="(val) => saveAction({ service_user_status: val })"
              >
                <a-radio :value="1">仅接粉专员离线时发送</a-radio>
                <a-radio :value="2">接粉专员在线和离线均发送</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item
              label="自动回复"
              help="收到用户私信回复之后系统自动回复"
            >
              <a-switch
                :model-value="config.auto_reply === 1"
                @change="(val) => saveAction({ auto_reply: val ? 1 : -1 })"
              />
            </a-form-item>
            <a-form-item
              label="自动流转二级线索"
              help="用户回复手机号或者微信号，自动将一级线索流转至二级线索池。"
            >
              <a-switch
                :model-value="config.auto_conversion === 1"
                @change="(val) => saveAction({ auto_conversion: val ? 1 : -1 })"
              />
            </a-form-item>
          </a-form>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card style="height: 100%" title="AI语义分析过滤等级">
          <a-radio-group
            v-model="config.ai_check_level"
            type="button"
            @change="(val) => saveAction({ ai_check_level: val })"
          >
            <a-radio :value="1">一级</a-radio>
            <a-radio :value="2">二级</a-radio>
            <a-radio :value="3">三级</a-radio>
          </a-radio-group>
        </a-card>
      </a-col>
    </a-row>
    <a-card size="small">
      <a-tabs type="card-gutter" lazy-load>
        <a-tab-pane key="爬虫关键词" title="爬虫关键词">
          <clue-setting-keywords
            :send-params="{
              platform,
              area: config.area,
              type: 'keyword',
            }"
            :platform="platform"
            :area="config.area"
          ></clue-setting-keywords>
        </a-tab-pane>
        <a-tab-pane
          v-if="platform === '抖音'"
          key="商业化抖音号"
          title="商业化抖音号"
        >
          <clue-setting-keywords
            :send-params="{
              platform,
              area: config.area,
              type: 'tk_account',
            }"
            :platform="platform"
            :area="config.area"
          ></clue-setting-keywords>
        </a-tab-pane>
        <a-tab-pane key="直播间监控" title="直播间监控">
          <clue-setting-keywords
            :send-params="{
              platform,
              area: config.area,
              type: 'live_room',
            }"
            :platform="platform"
            :area="config.area"
          ></clue-setting-keywords>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { PropType, ref } from 'vue';
  import request from '@/api/request';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import { useDataCacheStore } from '@/store';
  import ClueSettingKeywords from './clue-setting-keywords.vue';

  const props = defineProps({
    platform: {
      type: String as PropType<string>,
      default: '',
    },
  });
  const dataCacheStore = useDataCacheStore();

  const defaultConfig = () => ({
    platform: '',
    area: '哈尔滨',
    appoint_account: [],
    live_room_setting: [],
    ai_check_level: 1,
    state: 1,

    service_user_status: 1,
    auto_conversion: -1,
    auto_reply: -1,
  });
  const config = ref(defaultConfig());

  // 获取配置信息
  function getConfig() {
    request('/api/thread/getBaseSetting', {
      platform: props.platform,
      area: config.value.area,
    }).then((res) => {
      Object.assign(config.value, defaultConfig(), res.data);
    });
  }
  getConfig();

  // 更新配置信息
  function saveAction(params: any) {
    request('/api/thread/baseSettingSave', {
      platform: props.platform,
      area: config.value.area,
      ...params,
    });
  }
</script>

<style scoped lang="less">
  :deep(.arco-tabs-content) {
    padding-top: 0;
  }
</style>
