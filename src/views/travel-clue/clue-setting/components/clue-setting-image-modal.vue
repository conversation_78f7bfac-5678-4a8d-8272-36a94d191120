<template>
  <d-modal
    v-model:visible="visible"
    title="图片配置"
    width="80vw"
    unmount-on-close
    :footer="null"
  >
    <div class="table-card">
      <div class="table-card-header mt-10">
        <a-space> </a-space>
        <a-space>
          <a-button type="primary" @click="saveRef?.show()">
            <template #icon><icon-plus /></template>
            新增
          </a-button>
        </a-space>
      </div>
      <base-table
        ref="theTable"
        v-model:loading="loading"
        class="mt-10"
        :columns-config="columns"
        :data-config="getList"
        :auto-request="false"
        :scroll-percent="{ x: 1000, y: '70vh' }"
        :send-params="tableParams"
        @select-change="selectionChange"
      >
        <template #keywords="{ record }: TableColumnSlot">
          <component
            :is="record.keywords.length > 8 ? Tooltip : 'div'"
            :content="
              record.keywords
                .map((item) => item?.account_name || item)
                .join('，')
            "
            :content-style="{ maxWidth: '600px' }"
          >
            <a-space wrap size="mini">
              <a-tag
                v-for="item in record.keywords.slice(0, 8)"
                :key="item"
                color="blue"
              >
                {{ item?.account_name || item }}
              </a-tag>
              <a-tag v-if="record.keywords.length > 8" color="blue">
                等共{{ record.keywords.length }}个
              </a-tag>
            </a-space>
          </component>
        </template>
        <template #action="{ record }: TableColumnSlot">
          <a-button type="primary" @click="saveRef?.show(record)">
            编辑
          </a-button>
        </template>
      </base-table>
    </div>
    <clue-setting-image-save
      ref="saveRef"
      :send-params="{ ...sendParams, ...formModel }"
      @save="queryAction()"
    />
  </d-modal>
</template>

<script lang="ts" setup>
  import DModal from '@/components/d-modal/d-modal.vue';
  import { computed, nextTick, reactive, ref } from 'vue';
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import request from '@/api/request';
  import { Message, Tooltip } from '@arco-design/web-vue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import {
    replayTemplateStateM,
    replayTemplateTypeM,
  } from '@/components/dict-select/dict-clue';
  import ClueSettingImageSave from '@/views/travel-clue/clue-setting/components/clue-setting-image-save.vue';
  import { getText } from '@/components/dict-select/dict-util';

  const props = defineProps({
    sendParams: {
      type: Object,
      default: () => ({}),
    },
  });

  const columns = computed(() => [
    {
      title: getText(replayTemplateTypeM, props.sendParams.type),
      dataIndex: 'keywords',
    },
    {
      title: '文件夹',
      dataIndex: 'dir_name',
    },
    {
      title: '创建时间',
      dataIndex: 'add_time',
    },
    {
      title: '更新时间',
      dataIndex: 'update_time',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ]);
  const visible = ref(false);
  const loading = ref(false);
  const theTable = ref();
  const saveRef = ref();
  function defaultForm() {
    return {
      setting_keywords_id: null,
      area: null,
      platform: null,
      keywords: null,
    };
  }
  const formModel = ref(defaultForm());

  // 数据选择相关
  let rowSelection = reactive({
    type: 'checkbox',
    fixed: true,
    width: 30,
    onlyCurrent: true,
    showCheckedAll: true,
    selectedRowKeys: [] as any[],
    selectedRows: [] as any[],
  });
  const selectionChange = (selectedRowKeys: any[], selectedRows: any[]) => {
    rowSelection.selectedRowKeys = selectedRowKeys.filter((item: any) => item);
    rowSelection.selectedRows = selectedRows;
  };
  const resetHandler = () => {
    rowSelection.selectedRowKeys = [];
    rowSelection.selectedRows = [];
  };

  function queryAction() {
    nextTick(() => {
      theTable.value.search();
    });
  }

  const show = (record: any) => {
    visible.value = true;
    let initForm = defaultForm();
    formModel.value = initForm;
    if (record) {
      Object.keys(initForm).forEach((key) => {
        // @ts-ignore
        formModel.value[key] =
          record[key] || initForm[key as keyof typeof initForm];
      });
    }
    queryAction();
  };

  const tableParams = computed(() => ({
    ...formModel.value,
    ...props.sendParams,
  }));

  let cancelToken: AbortController;
  function getList(data: any) {
    resetHandler();
    cancelToken?.abort('重复请求取消');
    cancelToken = new AbortController();
    return request(
      '/api/thread/getKeywordGroupImageDir',
      {
        ...data,
      },
      cancelToken.signal
    );
  }

  defineExpose({
    show,
  });
</script>

<style scoped></style>
