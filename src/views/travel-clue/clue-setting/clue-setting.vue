<template>
  <div>
    <a-tabs :default-active-key="contactWayListM[0].value" lazy-load>
      <a-tab-pane
        v-for="item in contactWayListM"
        :key="item.value"
        :title="item.label"
      >
        <clue-setting-box :platform="item.value"></clue-setting-box>
      </a-tab-pane>
      <template #extra>
        <router-link to="/clue/image-manage">
          <a-button type="primary">
            <template #icon> <icon-image /> </template>
            图片库
          </a-button>
        </router-link>
      </template>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
  import { contactWayListM } from '@/components/dict-select/dict-travel';
  import ClueSettingBox from './components/clue-setting-box.vue';
</script>

<style scoped lang="less"></style>
