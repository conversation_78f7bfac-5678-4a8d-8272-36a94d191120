<template>
  <div class="clue-chat">
    <div class="chat-msg-box">
      <template v-if="msgList.length">
        <div
          v-for="(item, index) in msgList"
          :key="index"
          :class="{
            'chat-msg-item': true,
            'chat-msg-item-right': item.msg_show === 'right',
          }"
        >
          <div v-if="item.msg_show === 'right'" class="chat-msg-time">
            {{ item.send_time }} {{ item.send_user }}
          </div>
          <div v-else class="chat-msg-time">
            {{ item.send_user }} {{ item.send_time }}
          </div>
          <div v-if="item.message_type === 'image'" class="chat-msg">
            <img
              class="msg-img"
              :src="item.message"
              alt=""
              @click="previewImg([item.message])"
            />
          </div>
          <div v-else class="chat-msg">
            <span v-html="getMessage(item.message || '')"> </span>
          </div>
        </div>
      </template>
      <a-empty v-else description="暂无消息" />
    </div>
    <div v-if="!noChat" class="ipt-box">
      <a-input-group class="w100p">
        <a-input
          ref="inputRef"
          v-model="msgTxt"
          allow-clear
          @keyup.enter="sendMsg"
        />
        <a-upload
          action="/api/uploadFile"
          :show-file-list="false"
          :data="{ type: 'send_msg_image' }"
          accept="image/*"
          @change="uploadChange"
          @success="uploadSuccess"
        >
          <template #upload-button>
            <a-button
              :loading="uploading"
              style="border-radius: 0"
              type="outline"
              @click="sendMsg"
            >
              <template #icon>
                <icon-image />
              </template>
            </a-button>
          </template>
        </a-upload>
        <chat-emoji :platform="sendParams.platform" @select="selectEmmoji">
          <a-button type="outline">
            <template #icon>
              <icon-face-smile-fill />
            </template>
          </a-button>
        </chat-emoji>
        <a-button type="primary" @click="sendMsg">
          <template #icon>
            <icon-send />
          </template>
        </a-button>
      </a-input-group>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, nextTick, onBeforeUnmount, ref } from 'vue';
  import request from '@/api/request';
  import { FileItem, Message } from '@arco-design/web-vue';
  import ChatEmoji from '@/views/travel-clue/sale-clue-list/chat-emoji.vue';
  import { previewImg, setCaretPosition } from '@/utils/util';
  import dayjs from 'dayjs';

  const props = defineProps({
    sendParams: {
      type: Object,
      default: () => ({}),
    },
    noChat: {
      type: Boolean,
      default: false,
    },
  });
  const msgTxt = ref('');
  const inputRef = ref();
  const msgList = ref<any[]>([]);

  const xiaohongshu = computed(() => props.sendParams.platform === '小红书');

  function getMsgList() {
    request('/api/thread/getChatList', {
      ...props.sendParams,
    }).then((res) => {
      msgList.value = res.data || [];
    });
  }
  let timer = setInterval(() => {
    getMsgList();
  }, 5000);
  getMsgList();
  onBeforeUnmount(() => {
    clearInterval(timer);
  });

  function uploadSuccess(file: FileItem) {
    if (file.status === 'done' && file.response?.code === 0) {
      msgList.value.push({
        message: file.response?.data?.url,
        msg_show: 'right',
        message_type: 'image',
        send_time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      });
      nextTick(() => {
        document.querySelector('.chat-msg-box')?.scrollTo({ top: 10000 });
      });
      request('/api/thread/sendDYChat', {
        ...props.sendParams,
        image_url: file.response?.data?.file_path,
        type: 'image',
      });
    } else if (file.status === 'error') {
      Message.error(file.response?.data?.msg || '上传失败');
    }
  }

  const uploading = ref(false);
  function uploadChange(fileList: FileItem[], fileItem: FileItem) {
    switch (fileItem.status) {
      case 'init':
        uploading.value = true;
        break;
      case 'done':
      case 'error':
        uploading.value = false;
        break;
      default:
        break;
    }
  }

  function sendMsg() {
    if (msgTxt.value) {
      msgList.value.push({
        message: msgTxt.value,
        msg_show: 'right',
        send_time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      });
      nextTick(() => {
        document.querySelector('.chat-msg-box')?.scrollTo({ top: 10000 });
      });
      request('/api/thread/sendDYChat', {
        ...props.sendParams,
        message: msgTxt.value,
      });
      msgTxt.value = '';
    }
  }

  function getMessage(msg = '') {
    let reg = /\[(.+?)\]/gi;
    let emojis = msg.match(reg);
    emojis = Array.from(new Set(emojis));
    emojis?.forEach((item) => {
      msg = msg.replaceAll(
        item,
        `<img src="/${
          xiaohongshu.value ? 'xiaohongshu-emoji' : 'douyin-emoji'
        }/${item}.png" style="width: 20px;height:20px;margin: 0 2px;" />`
      );
    });
    return msg;
  }

  function selectEmmoji(msg: string) {
    let iptDom = inputRef.value.inputRef;
    let newName: string = msgTxt.value;
    let index = iptDom.selectionStart || newName?.length || 0;
    if (!newName) {
      newName = `${msg}`;
    } else {
      newName = `${newName.slice(0, index)}${msg}${newName.slice(index)}`;
    }
    msgTxt.value = newName;
    nextTick(() => {
      iptDom?.focus();
      const len = msg.length + index;
      setCaretPosition(iptDom, len);
    });
  }
</script>

<style scoped lang="less">
  .clue-chat {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .chat-msg-box {
    overflow-y: auto;
    flex: 1;
    .chat-msg-item {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }
    .chat-msg-item-right {
      align-items: flex-end;
      .chat-msg {
        background: #353845;
        min-width: 0;
      }
    }
    .chat-msg-time {
      font-size: 12px;
      padding: 0 10px;
    }
    .chat-msg {
      background: #936d56;
      padding: 8px 16px;
      border-radius: 10px;
      max-width: 66%;
      min-width: 60px;
      box-sizing: content-box;
      color: #fff;
      margin: 10px;
      margin-top: 0;
      position: relative;
      word-break: break-all;
      line-height: 1.5;
      white-space: pre-line;
      .msg-img {
        width: 200px;
      }
    }
  }
  .ipt-box {
    margin-top: 10px;
  }
</style>
