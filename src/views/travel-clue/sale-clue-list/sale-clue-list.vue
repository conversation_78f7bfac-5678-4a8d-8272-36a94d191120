<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        placeholder="请输入线索ID"
        :search-rules="searchRules"
        :base-search-rules="baseSearchRules"
        @hand-submit="handleSubmit"
      ></search-form>

      <div class="table-card-header mt-10">
        <div> </div>
        <a-space>
          <columns-select
            :the-field-map="allFieldsConfig"
            :need-format-column="false"
            :send-params="{ type: 'travel_sale_clue' }"
            :default-columns="columnsConfig.map((item) => item.dataIndex)"
            @select-columns-ok="setColumns"
          ></columns-select>
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :scroll-percent="scrollPercent"
        :send-params="tableParams"
      >
        <template #action="{ record }: TableColumnSlot">
          <a-space size="mini">
            <a-badge
              v-if="record.account_id"
              :count="noReadIds.includes(record.thread_seas_id) ? 1 : 0"
              dot
            >
              <a-link @click="detailRef?.show(record)"> 发起沟通 </a-link>
            </a-badge>
            <template v-if="['待分配', '已分配'].includes(record.status)">
              <a-link @click="nextRef?.show(record)"> 转二级线索 </a-link>
              <a-link @click="cancelRef?.show(record)"> 作废 </a-link>
            </template>
          </a-space>
        </template>
        <template #account_id="{ record }: TableColumnSlot">
          <template v-if="record.media_sec_uid">
            <a-link
              v-if="record.platform === '小红书'"
              :href="`https://www.xiaohongshu.com/user/profile/${record.media_sec_uid}`"
              target="_blank"
            >
              {{ record.account_id }}
            </a-link>
            <a-link
              v-else
              :href="`https://www.douyin.com/user/${record.media_sec_uid}`"
              target="_blank"
            >
              {{ record.account_id }}
            </a-link>
          </template>
          <span v-else>{{ record.account_id }}</span>
        </template>
        <template #remark="{ record }: TableColumnSlot">
          <span style="white-space: pre-line">{{ record.remark }}</span>
        </template>
        <template #auto_reply_template="{ record }: TableColumnSlot">
          <span style="white-space: pre-line">
            {{ record.auto_reply_template }}
          </span>
        </template>
        <template #thread_content="{ record }: TableColumnSlot">
          <a-tooltip
            v-if="record.thread_content?.length > 50"
            :content="record.thread_content.slice(0, 400)"
          >
            <span>{{ record.thread_content.slice(0, 50) }}...</span>
          </a-tooltip>
          <span v-else>{{ record.thread_content }}</span>
        </template>
        <template #status="{ record }: TableColumnSlot">
          <a-space direction="vertical" style="white-space: pre-line">
            <a-tag :color="getColor(clueStatusM, record.status)">
              {{ getText(clueStatusM, record.status) }}
            </a-tag>
            <template v-if="record.status === '作废'">
              <a-tooltip
                v-if="record.cancellation_reason?.length > 50"
                :content="record.cancellation_reason.slice(0, 400)"
              >
                <span>{{ record.cancellation_reason.slice(0, 50) }}...</span>
              </a-tooltip>
              <span v-else>{{ record.cancellation_reason }}</span>
            </template>
          </a-space>
        </template>
      </base-table>
    </a-card>
    <sale-clue-detail ref="detailRef"></sale-clue-detail>
    <sale-clue-next
      ref="nextRef"
      @refresh="theTable?.fetchData()"
    ></sale-clue-next>
    <sale-clue-cancellation
      ref="cancelRef"
      @refresh="theTable?.fetchData()"
    ></sale-clue-cancellation>
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';

  import { ref, reactive, computed, onBeforeUnmount, markRaw } from 'vue';
  import request from '@/api/request';
  import { TableColumnSlot } from '@/global';
  import { clueAssignM, clueStatusM } from '@/components/dict-select/dict-clue';
  import SaleClueDetail from '@/views/travel-clue/sale-clue-list/sale-clue-detail.vue';
  import SaleClueNext from '@/views/travel-clue/sale-clue-list/sale-clue-next.vue';
  import { useDataCacheStore } from '@/store';
  import SaleClueTwoAdd from '@/views/travel-clue/sale-clue-list-two/sale-clue-two-add.vue';
  import requestSelect from '@/components/select/request-select.vue';
  import SaleClueCancellation from '@/views/travel-clue/sale-clue-list/sale-clue-cancellation.vue';
  import ColumnsSelect from '@/components/columns-select/columns-select.vue';
  import { cloneDeep } from 'lodash';
  import { contactWayListM } from '@/components/dict-select/dict-travel';
  import { useRoute, useRouter } from 'vue-router';
  import { getColor, getText } from '../../../components/dict-select/dict-util';

  const generateFormModel = () => {
    return {
      id: null as string | null,
      thread_seas_id: null,
      distribute_rule: null,
      status: null,
      area: null,
      from_setting: null,
      time_distribute: null,
      service_user_fs_id: null,
      time_first_response: null,
      cancellation_time: null,
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const detailRef = ref();
  const nextRef = ref();
  const cancelRef = ref();
  const formModel = reactive(generateFormModel());
  const dataCacheStore = useDataCacheStore();

  const route = useRoute();
  formModel.id = (route.query.id as string) || null;

  const baseSearchRules = ref([
    {
      field: 'id',
      label: '线索ID',
      value: null,
      symbol: '是',
    },
  ]);
  const searchRules = computed(() =>
    [
      route.query.id
        ? {
            field: 'id',
            label: '线索ID',
            onlyShow: true,
            value: route.query.id,
            symbol: ' ',
          }
        : null,
      {
        field: 'area',
        label: '旅游线路',
        value: null,
        component_name: 'dict-select',
        alwaysShow: true,
        attr: {
          dataList: dataCacheStore.lineList,
          labelKey: 'line_name',
          valueKey: 'line_name',
        },
      },
      {
        field: 'platform',
        label: '来源媒体',
        value: null,
        component_name: 'dict-select',
        alwaysShow: true,
        attr: {
          dataList: contactWayListM,
        },
      },
      {
        field: 'from_setting',
        label: '来源详情',
        value: null,
        component_name: 'a-input',
        alwaysShow: true,
        attr: {
          placeholder: '来源详情',
          allowClear: true,
        },
      },
      {
        field: 'thread_seas_id',
        label: '公海线索ID',
        value: null,
        component_name: 'a-input',
        alwaysShow: true,
        attr: {
          placeholder: '公海线索ID',
          allowClear: true,
        },
      },
      {
        field: 'distribute_rule',
        label: '分配规则',
        value: null,
        component_name: 'dict-select',
        attr: {
          dataList: clueAssignM,
        },
      },
      {
        field: 'status',
        label: '状态',
        value: null,
        alwaysShow: true,
        component_name: 'dict-select',
        attr: {
          dataList: clueStatusM,
        },
      },
      {
        field: 'service_user_fs_id',
        label: '接粉专员',
        value: null,
        component_name: markRaw(requestSelect),
        alwaysShow: true,
        attr: {
          requestUrl: '/api/travel/allServiceUser',
          labelKey: 'user_name',
          valueKey: 'fs_user_id',
          multiple: true,
        },
      },
      {
        field: 'time_distribute',
        label: '分配时间',
        value: null,
        component_name: 'a-range-picker',
      },
      {
        field: 'time_first_response',
        label: '首次响应时间',
        value: null,
        component_name: 'a-range-picker',
      },
      {
        field: 'cancellation_time',
        label: '作废时间',
        value: null,
        component_name: 'a-range-picker',
      },
    ].filter((item) => item)
  );

  const getList = async (data: any) => {
    return request('/api/thread/salesThreadList', {
      ...data,
    });
  };
  const allFieldsConfig = [
    {
      title: '全部字段',
      dataIndex: '全部字段',
      keys: [] as string[],
      dataList: [
        {
          title: '公海线索ID',
          dataIndex: 'thread_seas_id',
          width: 150,
        },
        {
          title: '用户账号ID',
          dataIndex: 'account_id',
          width: 150,
        },
        {
          title: '用户账号昵称',
          dataIndex: 'account_name',
        },

        {
          title: '旅游线路',
          dataIndex: 'area',
        },
        {
          title: '来源媒体',
          dataIndex: 'platform',
        },
        {
          title: '来源详情',
          dataIndex: 'from_setting',
        },
        {
          title: '来源内容',
          dataIndex: 'thread_content',
          width: 300,
        },
        {
          title: '自定义模版消息内容',
          dataIndex: 'auto_reply_template',
          width: 300,
        },
        {
          title: '接粉专员分配规则',
          dataIndex: 'distribute_rule',
        },
        {
          title: '接粉专员',
          dataIndex: 'service_user',
        },
        {
          title: '询价单号',
          dataIndex: 'relation_inquiry_order',
        },
        {
          title: '订单编号',
          dataIndex: 'relation_order',
        },
        {
          title: '分配时间',
          dataIndex: 'time_distribute',
        },
        {
          title: '首次响应时间',
          dataIndex: 'time_first_response',
        },
        {
          title: '评论时间',
          dataIndex: 'comment_time',
        },
        {
          title: '作废时间',
          dataIndex: 'cancellation_time',
        },
        {
          title: '状态',
          dataIndex: 'status',
          width: 200,
        },
        {
          title: '备注',
          dataIndex: 'remark',
          width: 300,
        },
      ],
    },
  ];
  allFieldsConfig.forEach((item) => {
    item.keys = item.dataList.map((citem) => {
      // @ts-ignore
      citem.key = citem.dataIndex;
      return citem.dataIndex;
    });
  });
  const columnsConfig = ref(cloneDeep(allFieldsConfig[0].dataList));
  const columns = computed(() => [
    {
      title: '线索ID',
      dataIndex: 'id',
      width: 100,
      fixed: 'left',
    },
    ...columnsConfig.value,
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 220,
    },
  ]);
  const scrollPercent = computed(() => ({
    maxHeight: '67vh',
    x: columns.value.length * 160,
  }));

  // table渲染完成回调
  const changeHandler = (tableData: any) => {
    loading.value = false;
  };

  const tableParams = computed(() => ({
    ...formModel,
  }));

  const router = useRouter();
  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    if (!formModel.id) {
      route.query.id = null;
      const newUrl = window.location.hash.replace(/\?.*$/, ''); // 获取当前路径，并去除参数
      window.history.replaceState({}, '', newUrl); // 更新浏览器历史记录，不触发页面重新加载
    }
    theTable.value?.search();
  };

  function setColumns(val: any[]) {
    columnsConfig.value = val.filter((item) =>
      allFieldsConfig.some((citem) => citem.keys.includes(item.dataIndex))
    );
    handleSubmit();
  }

  function exportAction() {
    theTable.value?.exportTable({});
  }

  const noReadIds = ref([]);
  function getNoRead() {
    request('/api/thread/noReadThread', {}).then((res) => {
      noReadIds.value = res.data.thread_seas_ids || [];
    });
  }
  getNoRead();
  let timer = setInterval(() => {
    getNoRead();
  }, 5000);
  onBeforeUnmount(() => {
    clearInterval(timer);
  });
</script>

<style scoped lang="less"></style>
