<template>
  <d-modal
    :visible="visible"
    unmount-on-close
    :simple="false"
    title="转二级线索"
    :mask-closable="false"
    title-align="start"
    :ok-loading="okLoading"
    width="460px"
    @cancel="handleCancel"
    @ok="handleBeforeOk"
  >
    <a-form ref="formRef" class="mt-10" :model="formModel" auto-label-width>
      <a-form-item label="手机号">
        <a-input
          v-model="formModel.phone"
          autocomplete="off"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item label="微信号">
        <a-input
          v-model="formModel.wechat"
          autocomplete="off"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item label="成人人数">
        <a-input-number
          v-model="formModel.adult_num"
          :precision="0"
          :min="0"
          autocomplete="off"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item label="儿童人数">
        <a-input-number
          v-model="formModel.children_num"
          :precision="0"
          :min="0"
          autocomplete="off"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item label="大概出行日期">
        <a-input
          v-model="formModel.estimated_travel_date"
          autocomplete="off"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item label="客服">
        <request-select
          v-model="formModel.two_service_user_fs_id"
          request-url="/api/travel/allServiceUser"
          label-key="user_name"
          value-key="fs_user_id"
          @change="
            (val, item) => (formModel.two_service_user = item?.user_name || '')
          "
        />
      </a-form-item>
      <a-form-item label="备注">
        <a-input
          v-model="formModel.remark"
          autocomplete="off"
          placeholder="请输入"
        />
      </a-form-item>
    </a-form>
  </d-modal>
</template>

<script lang="ts" setup>
  import { computed, ref, unref } from 'vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import DModal from '@/components/d-modal/d-modal.vue';
  import { requiredRule } from '@/utils/util';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import RequestSelect from '@/components/select/request-select.vue';

  const emit = defineEmits(['refresh']);
  const formRef = ref<FormInstance>();

  const generateFormModel = () => {
    return {
      id: '',
      phone: '',
      wechat: '',
      adult_num: null,
      children_num: null,
      estimated_travel_date: '',
      two_service_user: '',
      two_service_user_fs_id: '',
      remark: '',
    };
  };
  const formModel = ref(generateFormModel());

  const visible = ref(false);
  const okLoading = ref(false);
  const show = (data: any = {}) => {
    let initForm = generateFormModel();
    formModel.value = initForm;
    if (data) {
      Object.keys(initForm).forEach((key) => {
        // @ts-ignore
        formModel.value[key] =
          data[key] || initForm[key as keyof typeof initForm];
      });
    }
    visible.value = true;
    okLoading.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
    formModel.value = generateFormModel();
    formRef.value?.clearValidate();
  };
  const handleBeforeOk = async () => {
    const res = await formRef.value?.validate();
    if (!res) {
      if (formModel.value.phone && formModel.value.phone.length !== 11) {
        return Message.error('请输入正确格式的手机号');
      }
      okLoading.value = true;
      request('/api/thread/upgradeThread', {
        ...formModel.value,
      })
        .then(() => {
          emit('refresh');
          handleCancel();
        })
        .finally(() => {
          okLoading.value = false;
        });
    }
  };
  defineExpose({
    show,
  });
</script>

<style lang="less"></style>
