<template>
  <d-modal
    :visible="visible"
    unmount-on-close
    :simple="false"
    :title="extraInfo.account_name || ''"
    :mask-closable="false"
    title-align="start"
    :ok-loading="okLoading"
    width="900px"
    :footer="false"
    @cancel="handleCancel"
    @ok="handleBeforeOk"
  >
    <div class="df" style="height: 80vh">
      <a-form :model="info" size="mini" auto-label-width style="width: 400px">
        <a-form-item label="线索ID">
          <span>{{ info.id }}</span>
        </a-form-item>
        <a-form-item label="公海线索ID">
          <span>{{ extraInfo.tread_id }}</span>
        </a-form-item>
        <a-form-item label="来源媒体">
          <span>{{ extraInfo.platform }}</span>
        </a-form-item>
        <a-form-item label="来源类型">
          <span>{{ extraInfo.source }}</span>
        </a-form-item>
        <a-form-item label="来源详情">
          <span>{{ extraInfo.from_setting }}</span>
        </a-form-item>
        <a-form-item label="用户账号ID">
          <span>{{ extraInfo.account_id }}</span>
        </a-form-item>
        <a-form-item label="线索内容">
          <span>{{ extraInfo.thread_content }}</span>
        </a-form-item>
        <!-- <a-form-item label="线索意向度">
          <span>{{ extraInfo.trhead_intentionality }}</span>
        </a-form-item>-->
        <a-form-item label="自定义模版消息内容">
          <span>{{ info.auto_reply_template }}</span>
        </a-form-item>
        <a-form-item label="分配规则">
          <span>{{ info.distribute_rule }}</span>
        </a-form-item>
        <a-form-item label="接粉专员">
          <span>{{ info.service_user }}</span>
        </a-form-item>
        <a-form-item label="分配时间">
          <span>{{ info.time_distribute }}</span>
        </a-form-item>
        <a-form-item label="状态">
          <span>{{ info.status }}</span>
        </a-form-item>
      </a-form>
      <a-divider direction="vertical" />
      <sale-clue-chat
        v-if="extraInfo.tread_id"
        :no-chat="noChat"
        :send-params="{
          thread_seas_id: extraInfo.tread_id,
          platform: extraInfo.platform,
        }"
        style="flex: 1"
      />
    </div>
  </d-modal>
</template>

<script lang="ts" setup>
  import { computed, ref, unref } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import SaleClueChat from '@/views/travel-clue/sale-clue-list/sale-clue-chat.vue';

  const emit = defineEmits(['refresh']);
  const props = defineProps({
    noChat: {
      type: Boolean,
      default: false,
    },
  });

  const visible = ref(false);
  const okLoading = ref(false);
  const info = ref<any>({});
  const extraInfo = ref<any>({});

  function getInfo() {
    request('/api/thread/seasInfo', {
      thread_seas_id: info.value.thread_seas_id,
    }).then((res) => {
      extraInfo.value = res.data || {};
    });
  }

  const show = (data: any = {}) => {
    info.value = data;
    visible.value = true;
    getInfo();
  };

  const handleCancel = () => {
    visible.value = false;
    extraInfo.value = {};
    info.value = {};
  };
  const handleBeforeOk = async () => {
    handleCancel();
  };
  defineExpose({
    show,
  });
</script>

<style lang="less"></style>
