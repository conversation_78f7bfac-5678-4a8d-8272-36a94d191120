<template>
  <div class="content-box">
    <a-card size="small">
      <div class="jc-sb ai-cen">
        <dict-radio
          v-model="formModel.area"
          :data-list="dataCacheStore.lineList"
          label-key="line_name"
          value-key="line_name"
          @change="
            dirRef?.getDept();
            queryAction();
          "
        />
        <router-link to="/clue/clue-setting" replace>
          <a-button type="primary">
            <template #icon> <icon-backward /> </template>
            返回
          </a-button>
        </router-link>
      </div>
    </a-card>
    <a-card size="small" class="mt-10">
      <div class="df">
        <folder-tree-list
          ref="dirRef"
          v-model:dir-id="formModel.dir_id"
          class="w-200"
          :multi-level="false"
          :apis="dirApis"
          :send-params="dirParams"
          @change="queryAction()"
        ></folder-tree-list>
        <a-divider :margin="10" direction="vertical" />
        <div style="flex: 1">
          <div class="jc-sb mb-10">
            <a-space>
              <a-checkbox
                :model-value="selectKeys.length === list.length"
                :indeterminate="
                  selectKeys.length !== 0 && selectKeys.length !== list.length
                "
                @change="
                  (val) => (selectKeys = val ? list.map((item) => item.id) : [])
                "
              >
                全选
              </a-checkbox>
              <span>已选择{{ selectKeys.length }}个图片</span>
            </a-space>
            <a-space>
              <a-button type="primary" @click="uploadRef?.show(formModel)">
                <template #icon><icon-upload /></template>
                批量上传
              </a-button>
              <a-button
                :loading="downloading"
                type="primary"
                @click="downloadAction()"
              >
                <template #icon><icon-download /></template>
                批量下载
              </a-button>
              <a-button
                type="primary"
                @click="moveRef?.show({ ...formModel, image_ids: selectKeys })"
              >
                <template #icon><icon-undo /></template>
                批量转移
              </a-button>
              <a-popconfirm
                :content="`确认删除${selectKeys.length}张图片吗？`"
                @ok="delAction()"
              >
                <a-button type="primary" status="danger">
                  <template #icon><icon-delete /></template>
                  批量删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </div>

          <a-divider :margin="10" />
          <a-spin :loading="loading" style="display: block">
            <template v-if="list.length">
              <a-row :gutter="6">
                <a-col
                  v-for="imgItem in list"
                  :key="imgItem.id"
                  :span="4"
                  @click="selectChange(imgItem)"
                >
                  <div
                    :style="{
                      borderColor: selectKeys.includes(imgItem.id)
                        ? 'rgb(var(--arcoblue-2))'
                        : 'transparent',
                    }"
                    class="img-item"
                  >
                    <img
                      style="width: 100%"
                      :src="imgItem.url"
                      :alt="imgItem.file_name"
                    />
                    <a-checkbox
                      class="img-check"
                      :model-value="selectKeys.includes(imgItem.id)"
                    />
                  </div>
                </a-col>
              </a-row>
              <div class="mt-10 jc-sb">
                <div></div>
                <a-pagination
                  :current="formModel.page"
                  :page-size="formModel.pageSize"
                  :total="formModel.total"
                  show-page-size
                  show-total
                  :page-size-options="[12, 24, 48, 96]"
                  @change="(page) => queryAction(page)"
                  @page-size-change="(pageSize) => queryAction(1, pageSize)"
                />
              </div>
            </template>
            <a-empty v-else />
          </a-spin>
        </div>
      </div>
    </a-card>
    <image-upload ref="uploadRef" @refresh="queryAction()" />
    <image-move ref="moveRef" @refresh="queryAction()" />
  </div>
</template>

<script setup lang="ts">
  import { useDataCacheStore } from '@/store';
  import { computed, ref } from 'vue';
  import FolderTreeList from '@/components/fold-tree/folder-tree-list.vue';
  import request from '@/api/request';
  import ImageUpload from '@/views/travel-clue/image-manage/image-upload.vue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import { downloadLinkFile } from '@/utils/table-utils/table-util';
  import ImageMove from '@/views/travel-clue/image-manage/image-move.vue';

  const dataCacheStore = useDataCacheStore();
  function defaultForm() {
    return {
      area: '哈尔滨',
      dir_id: 0,
      page: 1,
      pageSize: 24,
      total: 0,
    };
  }
  const formModel = ref(defaultForm());
  const dirApis = {
    list: '/api/material/imageDirList',
    save: '/api/material/imageDirSave',
  };
  const dirParams = computed(() => ({
    area: formModel.value.area,
  }));
  const dirRef = ref();

  const loading = ref(false);
  const uploadRef = ref();
  const moveRef = ref();
  const list = ref<any[]>([]);
  const selectKeys = ref<number[]>([]);
  let cancelToken: AbortController;
  function getList() {
    cancelToken?.abort('重复请求取消');
    cancelToken = new AbortController();
    loading.value = true;
    request(
      '/api/material/imageList',
      {
        ...formModel.value,
      },
      cancelToken.signal
    )
      .then((res) => {
        list.value = res.data.data;
        formModel.value.page = res.data.current_page;
        formModel.value.pageSize = res.data.per_page;
        formModel.value.total = res.data.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }
  getList();

  function selectChange(item: any) {
    if (selectKeys.value.includes(item.id)) {
      selectKeys.value = selectKeys.value.filter((val) => val !== item.id);
    } else {
      selectKeys.value.push(item.id);
    }
  }

  function queryAction(page?: number, pageSize?: number) {
    formModel.value.page = page || 1;
    formModel.value.pageSize = pageSize || formModel.value.pageSize;
    getList();
  }

  function delAction() {
    loading.value = true;
    request('/api/material/imageDel', {
      image_ids: selectKeys.value,
    }).then(() => {
      queryAction();
    });
  }

  const downloading = ref(false);
  function downloadAction() {
    downloading.value = true;
    let items = list.value.filter((item) => selectKeys.value.includes(item.id));
    Promise.all(
      items.map((item: any) => {
        return downloadLinkFile(item.url, item.file_name);
      })
    ).finally(() => {
      downloading.value = false;
    });
  }
</script>

<style scoped lang="less">
  .content-box {
    .img-item {
      border: 1px solid transparent;
      position: relative;
      width: 100%;
      img {
        width: 100%;
        height: 300px;
        object-fit: contain;
      }
      .img-check {
        position: absolute;
        left: 10px;
        top: 10px;
      }
      &:hover {
        box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.3);
      }
    }
  }
</style>
