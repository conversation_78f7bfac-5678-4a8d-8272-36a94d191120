<template>
  <d-modal
    :visible="visible"
    unmount-on-close
    :simple="false"
    title="上传图片"
    :mask-closable="false"
    title-align="start"
    :ok-loading="okLoading"
    width="460px"
    @cancel="handleCancel"
    @ok="handleBeforeOk"
  >
    <a-form :model="formModel">
      <a-form-item label="路线">
        <dict-select
          v-model="formModel.area"
          :data-list="dataCacheStore.lineList"
          label-key="line_name"
          value-key="line_name"
          :allow-clear="false"
          @change="formModel.dir_id = ''"
        ></dict-select>
      </a-form-item>
      <a-form-item label="文件夹">
        <request-select
          v-model="formModel.dir_id"
          :send-params="dirParams"
          value-key="id"
          label-key="dir_name"
          request-url="/api/material/imageDirList"
          :get-data-list="(arr) => arr?.children || []"
          :allow-clear="false"
        ></request-select>
      </a-form-item>
      <a-form-item v-if="formModel.area && formModel.dir_id" label="上传图片">
        <upload-image-file
          v-model="formModel.imgs"
          :send-params="{ type: 'clue_imgs' }"
          @success="sendInfo"
        >
        </upload-image-file>
      </a-form-item>
    </a-form>
  </d-modal>
</template>

<script setup lang="ts">
  import DModal from '@/components/d-modal/d-modal.vue';
  import { computed, ref } from 'vue';
  import UploadImageFile from '@/components/upload-file/upload-image-file.vue';
  import request from '@/api/request';
  import FolderTreeSelect from '@/components/fold-tree/folder-tree-select.vue';
  import { useDataCacheStore } from '@/store';
  import RequestSelect from '@/components/select/request-select.vue';

  const visible = ref(false);
  const dataCacheStore = useDataCacheStore();
  const okLoading = ref(false);
  const emits = defineEmits(['refresh']);
  function defaultForm() {
    return {
      imgs: [],
      dir_id: '',
      area: '',
    };
  }
  const formModel = ref(defaultForm());
  const dirParams = computed(() => ({
    area: formModel.value.area,
  }));

  function handleCancel() {
    visible.value = false;
    formModel.value = defaultForm();
    emits('refresh');
  }

  function handleBeforeOk() {
    handleCancel();
  }

  function sendInfo(item: any) {
    request('/api/material/imageSave', {
      ...formModel.value,
      data: [
        {
          url: item.response?.data?.file_path,
          file_name: item.name,
        },
      ],
    });
  }

  function show(params: any) {
    formModel.value.area = params.area;
    formModel.value.dir_id = params.dir_id;
    visible.value = true;
  }
  defineExpose({
    show,
  });
</script>

<style scoped lang="less"></style>
