<template>
  <div>
    <a-card size="small" class="table-card">
      <!--<search-form
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        :search-rules="searchRules"
        :continue-keys="['redemption_type']"
        show-search-btn
        @hand-submit="handleSubmit"
      ></search-form>-->
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        :continue-keys="['redemption_type']"
        @search="handleSubmit"
      >
        <template #formItemGroup>
          <a-form-item label="统计类型">
            <dict-radio
              v-model="formModel.redemption_type"
              :data-list="redemptionTypeM"
              @change="handleSubmit()"
            />
          </a-form-item>
          <a-form-item label="日期范围">
            <a-range-picker v-model="formModel.weeks" mode="week" allow-clear />
          </a-form-item>
          <a-form-item label="旅游线路">
            <dict-select
              v-model="formModel.area"
              :data-list="dataCacheStore.lineList"
              label-key="line_name"
              value-key="line_name"
            />
          </a-form-item>
          <a-form-item label="地接社">
            <dict-select
              v-model="formModel.local_travel_agency"
              :data-list="dataCacheStore.getAgencyList() || []"
              label-key="agency_name"
              value-key="agency_name"
            />
          </a-form-item>
          <a-form-item label="产品">
            <dict-select
              v-model="formModel.travel_line"
              :data-list="formModel.area
          ? dataCacheStore.travelProducts.filter(
              (item:any) => item.area === formModel.area
            )
          : dataCacheStore.travelProducts"
              :max-tag-count="2"
              allow-clear
              multiple
              :format-label="(data: SelectOptionData) => data.label?.slice(0, 3)"
            />
          </a-form-item>
          <a-form-item label="来源媒体">
            <dict-select
              v-model="formModel.source"
              :data-list="contactWayListM"
            />
          </a-form-item>
          <a-form-item label="订单来源">
            <request-select
              v-model="formModel.order_source"
              request-url="/api/report/liveRoomList"
              label-key="live_room_name"
              value-key="live_room_name"
              :max-tag-count="2"
              :get-data-list="(data: any[]) => [
                { live_room_name: '内容私域' },
                { live_room_name: '直播间私域' },
                ...data,
                ]
              "
            />
          </a-form-item>
          <a-form-item label="付款方式">
            <dict-select
              v-model="formModel.payment_type"
              :data-list="payTypeM"
            />
          </a-form-item>
          <a-form-item label="订单来源类型">
            <request-select
              v-model="formModel.source_detail"
              request-url="/api/travel/getSourceDetailMap"
              label-key="value"
              value-key="value"
              :max-tag-count="1"
            />
          </a-form-item>
          <a-form-item label="客资类型">
            <dict-select
              v-model="formModel.customer_equity_type"
              :data-list="customerEquityTypeM"
            />
          </a-form-item>
          <a-form-item label="角色">
            <request-select
              v-model="formModel.role_id"
              request-url="/api/user/roleList"
              label-key="role_name"
              @change="formModel.user_ids = []"
            />
          </a-form-item>
          <a-form-item label="姓名">
            <request-select
              v-model="formModel.user_ids"
              :disabled="!formModel.role_id"
              :placeholder="!formModel.role_id ? '请先选择角色' : ''"
              request-url="/api/user/userList"
              :send-params="{
                roles: formModel.role_id ? [formModel.role_id] : [],
              }"
              label-key="user_name"
              :max-tag-count="2"
            />
          </a-form-item>
          <a-form-item label="客服所属部门">
            <request-tree-select
              v-model="formModel.department_ids"
              request-url="/api/department/list"
              label-key="department_name"
              child-key="child"
              multiple
              :max-tag-count="2"
            />
          </a-form-item>
        </template>
      </search-form-fold>

      <div class="table-card-header mt-10">
        <div> </div>
        <a-space>
          <!--<a-button type="primary" @click="exportAction">
            <template #icon>
              <icon-export />
            </template>
            导出
          </a-button>-->
          <a-space v-if="goal.find((item) => item.area === formModel.area)">
            <a-tooltip>
              <template #content>
                <div>核销率 = 核销的总团款/所有总团款汇总</div>
              </template>
              <div>
                <span class="a-text mr-5">
                  <icon-question-circle />
                </span>
                <span>核销率目标</span>
              </div>
            </a-tooltip>
            <a-input-number
              v-model="goal.find((item) => item.area === formModel.area).value"
              :min="0.01"
              :precision="2"
              class="w-200"
              @change="setGoal"
            >
              <template #suffix> % </template>
            </a-input-number>
          </a-space>
        </a-space>
      </div>
      <!--table 区域-->
      <a-table
        v-model:loading="loading"
        :columns="allColumns"
        bordered
        :data="listData"
        :pagination="false"
        class="report-table"
      >
        <template
          v-for="item in columns"
          :key="item.dataIndex"
          #[`${item.dataIndex}_title`]="{ column: { data } }"
        >
          <div v-if="data" style="text-align: center">
            <div>
              <a-typography-text>Week {{ data.week_num }}</a-typography-text>
            </div>
            <div>
              <a-typography-text type="secondary" style="white-space: nowrap">
                {{ data.start_date }} - {{ data.end_date }}
              </a-typography-text>
            </div>
          </div>
          <div v-else>
            <div>
              <a-typography-text>总计</a-typography-text>
            </div>
          </div>
        </template>
        <template
          v-for="item in columns"
          :key="item.dataIndex"
          #[`${item.dataIndex}`]="{ column, record }: TableColumnSlot"
        >
          <!--概览行渲染-->
          <template v-if="record.week_name === '概览'">
            <a-typography-text type="secondary" style="white-space: nowrap">
              <div>
                待核销{{
                  getPathValue(record, column.dataIndex)?.wait_verification ||
                  0
                }}单 / 共{{
                  getPathValue(record, column.dataIndex)?.order_num || 0
                }}单
              </div>
              <div>
                待核销{{
                  numberFormat(
                    getPathValue(record, column.dataIndex)
                      ?.wait_verification_final
                  )
                }}
                / 共
                {{
                  numberFormat(
                    getPathValue(record, column.dataIndex)?.total_final
                  )
                }}
              </div>
            </a-typography-text>
          </template>
          <template v-else>
            <!--这几列需要背景颜色标识-->
            <div
              :style="{
                'text-align': 'center',
                'background': noRateFields.includes(record.week_name)
                  ? ''
                  : getBgColor(
                      getPathValue(record, column.dataIndex || '')
                        ?.progress_float || 0
                    ),
              }"
            >
              <div>
                <span
                  v-if="
                    !isNull(
                      getPathValue(record, column.dataIndex || '')?.progress
                    )
                  "
                  :style="{
                    color: record.week_name.includes('Gap')
                      ? getColor(
                          getPathValue(record, column.dataIndex || '')
                            ?.progress || 0
                        )
                      : null,
                  }"
                >
                  <!--此字段后端处理过 不需要格式化-->
                  <span
                    v-if="
                      ['人次(核销人次/总人次)', '发确认件人次'].includes(
                        record.week_name
                      )
                    "
                  >
                    {{
                      getPathValue(record, column.dataIndex)?.progress || '-'
                    }}
                  </span>
                  <span v-else>
                    {{
                      moneyFormat(
                        getPathValue(record, column.dataIndex)?.progress || 0
                      )
                    }}
                    <!--非百分比展示-->
                    {{ noRateFields.includes(record.week_name) ? '' : '%' }}
                  </span>
                </span>
                <span v-else>-</span>
              </div>
              <!--需要展示环比的数据-->
              <div
                v-if="
                  cacheFormModel.user_ids?.length &&
                  !noRateFields.includes(record.week_name)
                "
              >
                <a-typography-text
                  v-if="
                    !isNull(
                      getPathValue(record, column.dataIndex || '')
                        ?.progress_float
                    )
                  "
                  type="secondary"
                >
                  ({{
                    moneyFormat(
                      getPathValue(record, column.dataIndex)?.progress_float ||
                        0
                    )
                  }}%)
                </a-typography-text>
                <span v-else>-</span>
              </div>
              <!--<div
                v-if="
                  cacheFormModel.user_ids?.length &&
                  record.week_name === '总计' &&
                  column.title === '总计'
                "
              >
                总团款 ￥{{
                  numberFormat(
                    getPathValue(record, column.dataIndex)?.total_cut_price || 0
                  )
                }}
              </div>-->
            </div>
          </template>
        </template>
        <template #week_name="{ record }: TableColumnSlot">
          <a-tooltip v-if="record.week_name.includes('Gap')">
            <template #content>
              <div v-if="record.week_name === 'Gmv Gap'">
                Gmv Gap为周期内创建订单的团款 * 核销率目标 - 实际核销团款
              </div>
              <div v-else-if="record.week_name === '单数Gap'">
                单数Gap 为 Gmv Gap/周期内平均每单团款 小数四舍五入取整
              </div>
            </template>
            <span class="a-text mr-5">
              <icon-question-circle />
            </span>
          </a-tooltip>
          <span>{{ record.week_name }}</span>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch, markRaw } from 'vue';
  import request from '@/api/request';
  import dayjs from 'dayjs';
  import { moneyFormat, numberFormat } from '@/utils/table-utils/table-util';
  import {
    contactWayListM,
    customerEquityTypeM,
    payTypeM,
    redemptionTypeM,
  } from '@/components/dict-select/dict-travel';
  import { getPathValue } from '@/utils/util';
  import { cloneDeep, isNull } from 'lodash';
  import { useDataCacheStore } from '@/store';
  import { SelectOptionData } from '@arco-design/web-vue';
  import requestSelect from '@/components/select/request-select.vue';
  import dictRadio from '@/components/dict-select/dict-radio.vue';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import requestTreeSelect from '@/components/select/request-tree-select.vue';

  const generateFormModel = () => {
    return {
      source: [],
      order_source: [],
      travel_line: [],
      area: null,
      local_travel_agency: null,
      payment_type: [],
      source_detail: [],
      user_ids: [],
      role_id: '',
      customer_equity_type: '',
      redemption_type: '单量核销率',
      weeks: [
        dayjs().add(-6, 'week').startOf('w').format('YYYY-MM-DD'),
        dayjs().endOf('w').format('YYYY-MM-DD'),
      ],
      department_ids: [],
    };
  };
  const loading = ref(false);
  const goal = ref<any[]>([]);
  const formModel = reactive(generateFormModel());
  const dataCacheStore = useDataCacheStore();

  const noRateFields = [
    '核销利润',
    'Gmv Gap',
    '单数Gap',
    '人次(核销人次/总人次)',
    '人均核销利润',
    '核销金额',
    // 修正后
    '发确认件金额',
    '发确认件人次',
    '发确认件利润',
    '人均发确认件利润',
    '发确认件毛利率',
  ];

  const searchRules = computed(() => [
    {
      field: 'redemption_type',
      label: '统计类型',
      value: null,
      component_name: markRaw(dictRadio),
      alwaysShow: true,
      attr: {
        dataList: redemptionTypeM,
      },
    },
    {
      field: 'weeks',
      label: '日期范围',
      value: null,
      component_name: 'a-range-picker',
      alwaysShow: true,
      attr: {
        mode: 'week',
        allowClear: false,
      },
    },
    {
      field: 'area',
      label: '旅游线路',
      value: null,
      component_name: 'dict-select',
      alwaysShow: true,
      attr: {
        dataList: dataCacheStore.lineList,
        labelKey: 'line_name',
        valueKey: 'line_name',
      },
    },
    {
      field: 'local_travel_agency',
      label: '地接社',
      alwaysShow: true,
      value: null,
      component_name: 'dict-select',
      attr: {
        dataList: dataCacheStore.getAgencyList() || [],
        labelKey: 'agency_name',
        valueKey: 'agency_name',
      },
    },
    {
      field: 'travel_line',
      label: '产品',
      value: null,
      component_name: 'dict-select',
      alwaysShow: true,
      attr: {
        'dataList': formModel.area
          ? dataCacheStore.travelProducts.filter(
              (item) => item.area === formModel.area
            )
          : dataCacheStore.travelProducts,
        'max-tag-count': 2,
        'allow-clear': true,
        'multiple': true,
        'format-label': (data: SelectOptionData) => data.label?.slice(0, 3),
      },
    },
    {
      field: 'source',
      label: '来源媒体',
      value: null,
      component_name: 'dict-select',
      alwaysShow: true,
      attr: {
        dataList: contactWayListM,
      },
    },
    {
      field: 'order_source',
      label: '订单来源',
      value: null,
      component_name: markRaw(requestSelect),
      alwaysShow: true,
      attr: {
        'requestUrl': '/api/report/liveRoomList',
        'labelKey': 'live_room_name',
        'valueKey': 'live_room_name',
        'max-tag-count': 2,
        'get-data-list': (data: any[]) => [
          { live_room_name: '内容私域' },
          { live_room_name: '直播间私域' },
          ...data,
        ],
      },
    },
    {
      field: 'payment_type',
      label: '付款方式',
      value: null,
      component_name: 'dict-select',
      alwaysShow: true,
      attr: {
        dataList: payTypeM,
      },
    },
    {
      field: 'source_detail',
      label: '订单来源类型',
      value: null,
      component_name: markRaw(requestSelect),
      attr: {
        requestUrl: '/api/travel/getSourceDetailMap',
        labelKey: 'value',
        valueKey: 'value',
        maxTagCount: 1,
      },
    },
    {
      field: 'customer_equity_type',
      label: '客资类型',
      value: null,
      component_name: 'dict-select',
      alwaysShow: true,
      attr: {
        dataList: customerEquityTypeM,
      },
    },
    {
      field: 'role_id',
      label: '角色',
      value: null,
      component_name: markRaw(requestSelect),
      attr: {
        requestUrl: '/api/user/roleList',
        labelKey: 'role_name',
      },
    },
    {
      field: 'user_ids',
      label: '姓名',
      value: null,
      component_name: markRaw(requestSelect),
      attr: {
        requestUrl: '/api/user/userList',
        labelKey: 'user_name',
        maxTagCount: 2,
      },
    },
    {
      field: 'department_ids',
      label: '客服所属部门',
      value: null,
      component_name: markRaw(requestTreeSelect),
      attr: {
        requestUrl: '/api/department/list',
        labelKey: 'department_name',
        childKey: 'child',
        multiple: true,
        maxTagCount: 2,
      },
    },
  ]);

  watch(
    dataCacheStore.lineList,
    () => {
      request('/api/settingGet', {
        system_name: '核销率',
      }).then((res) => {
        try {
          res.data.value = JSON.parse(res.data.value) || [];
        } catch (e) {
          console.log(e);
        }
        goal.value = dataCacheStore.lineList.map((item) => ({
          area: item.line_name,
          value:
            (res.data.value &&
              res.data.value.find((citem: any) => citem.area === item.line_name)
                ?.value) ||
            null,
        }));
      });
    },
    {
      immediate: true,
    }
  );
  const tableParmas = computed(() => ({
    ...formModel,
    week_begin: dayjs(formModel.weeks[0]).startOf('w').format('YYYY-MM-DD'),
    week_end: dayjs(formModel.weeks[1]).endOf('w').format('YYYY-MM-DD'),
  }));

  const columns = ref<any[]>([]);
  const listData = ref<any[]>([]);
  const totalData = ref<any>({});

  const allColumns = computed(() => [
    {
      title: '核销时间/订单创建',
      dataIndex: 'week_name',
      align: 'center',
      fixed: 'left',
      width: 160,
      slotName: 'week_name',
    },
    ...columns.value,
  ]);

  let cancelToken: any;
  const getList = () => {
    loading.value = true;
    cancelToken?.abort('重复请求取消');
    cancelToken = new AbortController();
    request(
      '/api/report/redemptionRateReport',
      tableParmas.value,
      cancelToken.signal
    )
      .then((res) => {
        let colList: any[] = [];
        // 概览数据
        let totalStatistics: any = {
          title: '概览',
          week_name: '概览',
          sub_data: {},
        };
        res.data.total_data.forEach((item: any, index: number) => {
          totalStatistics.sub_data[index] = item;
          colList.push({
            title: item.week_num.toString(),
            dataIndex: `sub_data.${index}`,
            titleSlotName: `sub_data.${index}_title`,
            slotName: `sub_data.${index}`,
            data: item,
            align: 'center',
          });
        });
        totalStatistics.sub_data[res.data.total_data.length] =
          res.data.total_data_sum;
        colList.push({
          title: '总计',
          dataIndex: `sub_data.${res.data.total_data.length}`,
          titleSlotName: `sub_data.${res.data.total_data.length}_title`,
          slotName: `sub_data.${res.data.total_data.length}`,
          align: 'center',
        });

        columns.value = colList;
        listData.value = res.data.redemption_data.filter(
          (item: any) =>
            !['Gmv Gap', '单数Gap'].includes(item.week_name) || formModel.area
        );
        listData.value.unshift(totalStatistics);
        totalData.value = res.data.total_data_sum;
      })
      .finally(() => {
        loading.value = false;
      });
  };
  getList();

  const cacheFormModel = ref({});
  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    cacheFormModel.value = cloneDeep(formModel);
    getList();
  };

  function setGoal() {
    if (goal.value) {
      request('/api/settingSave', {
        system_name: '核销率',
        value: goal.value,
      }).then(() => {
        handleSubmit();
      });
    }
  }

  function getBgColor(num: number) {
    if (!cacheFormModel.value.user_ids?.length) {
      return '';
    }
    if (num > 5) {
      return `rgba(var(--red-3), ${num / 100})`;
    }
    if (num < -5) {
      return `rgba(var(--green-3), ${-(num / 100)})`;
    }
    return '';
  }
  function getColor(num: number) {
    if (num > 0) {
      return `rgb(var(--red-6))`;
    }
    if (num < 0) {
      return `rgb(var(--green-6))`;
    }
    return ``;
  }
</script>

<style scoped lang="less">
  .report-table.arco-table {
    :deep(.arco-table-cell) {
      padding-left: 0 !important;
      padding-right: 0;
    }
  }
</style>
