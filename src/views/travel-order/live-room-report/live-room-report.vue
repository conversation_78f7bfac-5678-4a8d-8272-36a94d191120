<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="handleSubmit()"
      >
        <template #formItemGroup>
          <a-form-item label="数据类型">
            <a-radio-group
              v-model="formModel.dataType"
              type="button"
              @change="handleSubmit()"
            >
              <a-radio value="clue">线索</a-radio>
              <a-radio value="order">订单</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="日期">
            <a-input-group class="w100p">
              <c-range-picker v-model="formModel.date" />
              <a-checkbox
                :model-value="formModel.group_fields.includes('accept_date')"
                @change="changeGroupFields('accept_date')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="直播间类型">
            <a-input-group class="w100p">
              <dict-select
                v-model="formModel.live_room_type"
                :max-tag-count="4"
                :data-list="liveRoomTypeM"
              />
              <a-checkbox
                :model-value="formModel.group_fields.includes('live_room_type')"
                @change="changeGroupFields('live_room_type')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="直播间">
            <a-input-group class="w100p">
              <request-select
                v-model="formModel.live_room_id"
                api="live_room"
                :max-tag-count="4"
                :send-params="{ type: formModel.live_room_type }"
              />
              <a-checkbox
                :model-value="formModel.group_fields.includes('live_room_id')"
                @change="changeGroupFields('live_room_id')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="直播运营">
            <a-input-group class="w100p">
              <request-select
                v-model="formModel.user_role_10"
                api="operate"
                :max-tag-count="4"
              />
              <a-checkbox
                :model-value="formModel.group_fields.includes('user_role_10')"
                @change="changeGroupFields('user_role_10')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="全职主播">
            <a-input-group class="w100p">
              <request-select
                v-model="formModel.user_role_7"
                api="user"
                :send-params="{ roles: [7] }"
                :max-tag-count="4"
              />
              <a-checkbox
                :model-value="formModel.group_fields.includes('user_role_7')"
                @change="changeGroupFields('user_role_7')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="内部KOC主播">
            <a-input-group class="w100p">
              <request-select
                v-model="formModel.user_role_8"
                api="user"
                :send-params="{ roles: [8] }"
                :max-tag-count="4"
              />
              <a-checkbox
                :model-value="formModel.group_fields.includes('user_role_8')"
                @change="changeGroupFields('user_role_8')"
              />
            </a-input-group>
          </a-form-item>
        </template>
      </search-form-fold>

      <div class="table-card-header mt-20">
        <div> </div>
        <a-space>
          <a-button type="primary" @click="exportAction">
            <template #icon>
              <icon-export />
            </template>
            导出
          </a-button>
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :send-params="tableParmas"
        :scroll-percent="{ x: columns.length * 140, maxHeight: '70vh' }"
      >
        <!--<template #cost="{ record }: TableColumnSlot">
          <a-space v-if="!record.isSummary">
            <span>{{ moneyFormat(record.cost) }}</span>
            <a-link @click="editRef?.show(record)">
              <icon-edit />
            </a-link>
          </a-space>
          <span v-else>{{ moneyFormat(record.cost) }}</span>
        </template>-->
      </base-table>
    </a-card>
    <live-room-cost-modal ref="editRef" @save="handleSubmit()" />
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';

  import { ref, reactive, computed } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import dayjs from 'dayjs';
  import { moneyFormatShow } from '@/utils/table-utils/columns-config';
  import LiveRoomCostModal from '@/views/travel-order/live-room-report/live-room-cost-modal.vue';
  import { liveRoomTypeM } from '@/components/dict-select/dict-common';
  import { moneyFormat } from '../../../utils/table-utils/table-util';

  const generateFormModel = () => {
    return {
      date: [
        dayjs().add(-6, 'days').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      dataType: 'clue',
      live_room_id: [],
      live_room_type: '',
      user_role_10: [],
      user_role_7: [],
      user_role_8: [],
      group_fields: ['accept_date'] as string[],
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const editRef = ref();
  const formModel = reactive(generateFormModel());

  const tableParmas = computed(() => ({
    ...formModel,
    date_begin: formModel.date[0],
    date_end: formModel.date[1],
    group_fields: formModel.group_fields.length
      ? formModel.group_fields
      : ['accept_date'],
  }));

  const getList = async (data: any) => {
    return request(
      formModel.dataType === 'order'
        ? '/api/report/liveRoomDayV2Report'
        : '/api/report/liveRoomDayV2ClueReport',
      {
        ...data,
      }
    );
  };

  const hasCost = computed(
    () =>
      formModel.group_fields.includes('accept_date') &&
      formModel.group_fields.includes('live_room_type') &&
      formModel.group_fields.includes('live_room_id') &&
      formModel.dataType === 'order'
  );
  function getGroupCols() {
    let arr: any[] = [];

    formModel.group_fields.forEach((item) => {
      if (item === 'accept_date') {
        arr.push({
          title: '日期',
          dataIndex: 'accept_date',
          width: 110,
        });
      }
      if (item === 'live_room_type') {
        arr.push({
          title: '直播间类型',
          dataIndex: 'live_room_type',
          width: 110,
        });
      }
      if (item === 'live_room_id') {
        arr.push(
          {
            title: '直播间',
            dataIndex: 'live_room',
            width: 200,
          },
          {
            title: '直播时长',
            dataIndex: 'live_hour_num',
            description: '直播间排班主播时长',
          }
        );
      }
      if (item === 'user_role_10') {
        arr.push({
          title: '直播运营',
          dataIndex: 'user_role_10',
          width: 110,
        });
      }
      if (item === 'user_role_7') {
        arr.push({
          title: '全职主播',
          dataIndex: 'user_role_7',
          width: 110,
        });
      }
      if (item === 'user_role_8') {
        arr.push({
          title: '内部KOC主播',
          dataIndex: 'user_role_8',
          width: 110,
        });
      }
    });

    if (hasCost.value) {
      arr.push({
        title: '投流金额',
        dataIndex: 'cost',
        render: moneyFormatShow(),
      });
    }
    if (formModel.dataType === 'order') {
      arr.push(
        {
          title: '直播间订单数',
          dataIndex: 'order_num',
        },
        {
          title: '直播间成交人数',
          dataIndex: 'order_deal_people',
        },
        hasCost.value
          ? {
              title: '直播间订单成本',
              dataIndex: 'live_room_avg_cost',
              description: '投流金额/直播间订单数',
              render: moneyFormatShow(),
            }
          : null,
        hasCost.value
          ? {
              title: '单客成本',
              dataIndex: 'avg_order_cost',
              description: '单客成本=投流金额/直播间成交人数',
              render: moneyFormatShow(),
            }
          : null
      );
    } else {
      arr.push(
        {
          title: '线索数',
          dataIndex: 'clue_num',
        },
        {
          title: '订单数',
          dataIndex: 'order_num',
        },
        {
          title: '成交人数',
          dataIndex: 'order_deal_people',
        }
      );
    }
    if (arr[0]) {
      arr[0].fixed = 'left';
    }
    return arr;
  }

  const columns = computed(() =>
    [
      ...getGroupCols(),
      {
        title: '订金',
        dataIndex: 'live_room_trading_amount',
        render: moneyFormatShow(),
      },
      {
        title: '总团款',
        dataIndex: 'estimated_trading_amount',
        render: moneyFormatShow(),
      },
      {
        title: '发确认件订单数',
        dataIndex: 'send_ensure_order_count',
      },
      {
        title: '发确认件金额',
        dataIndex: 'send_ensure_order_amount',
        render: moneyFormatShow(),
      },
      {
        title: '发确认件率',
        dataIndex: 'send_ensure_order_rate',
        description: '发确认件率 = 发确认件订单数/订单数',
      },
      {
        title: '平均客单价(订金)',
        dataIndex: 'avg_order_dj_cost',
        description: '平均客单价(订金) =订金/成交订单数',
        width: 160,
      },
      {
        title: '有效客单数',
        dataIndex: 'valid_order_num',
        description:
          '有效客单数 = 订单数 - 作废空号订单 - 作废停机订单 - 作废客户多次秒挂断订单 - 作废客户多次明确表示未下单订单',
      },
      hasCost.value
        ? {
            title: '有效客单成本',
            dataIndex: 'valid_order_cost',
            description:
              '有效订单成本 = 投流金额/（订单数 - 作废空号订单 - 作废停机订单 - 作废客户多次秒挂断订单 - 作废客户多次明确表示未下单订单）',
          }
        : null,
      {
        title: '核销平均客单价(总团款)',
        dataIndex: 'ensure_order_avg_cost',
        description: '核销平均客单价(总团款) = 核销总团款/核销总人数',
      },
      {
        title: '核销单数',
        dataIndex: 'ensure_order_count',
      },
      {
        title: '核销金额',
        dataIndex: 'ensure_order_amount',
        render: moneyFormatShow(),
      },
      {
        title: '核销率（单量）',
        dataIndex: 'ensure_order_count_rate',
      },
      {
        title: '核销率（订金）',
        dataIndex: 'ensure_order_amount_rate',
        description: '状态为‘已核销’的订单订金/订金汇总',
        width: 150,
      },
      {
        title: '退款订单数',
        dataIndex: 'refund_order_num',
        description: '状态为已退款的订单数量',
      },
      {
        title: '退单率',
        dataIndex: 'refund_order_rate',
        description: '退款订单数/成交订单数*100%',
      },
      {
        title: '退款金额',
        dataIndex: 'refund_order_amount',
        description: '退款订单的订金汇总',
        render: moneyFormatShow(),
      },
      {
        title: '核销总人数',
        dataIndex: 'ensure_people_count',
      },
      {
        title: '核销成人人数',
        dataIndex: 'ensure_adult_count',
      },
      {
        title: '核销儿童人数',
        dataIndex: 'refund_children_count',
      },
      {
        title: '核销均单人次',
        dataIndex: 'refund_order_people',
        description: '核销均单人次 = 核销的人数/核销订单数',
      },
    ].filter((item) => item)
  );

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }

  function changeGroupFields(key: string) {
    if (!formModel.group_fields.includes(key)) {
      formModel.group_fields.push(key);
    } else {
      formModel.group_fields = formModel.group_fields.filter(
        (item) => item !== key
      );
    }
    if (!formModel.group_fields.length) {
      formModel.group_fields.push('accept_date');
    }
    handleSubmit();
  }
</script>

<style scoped lang="less"></style>
