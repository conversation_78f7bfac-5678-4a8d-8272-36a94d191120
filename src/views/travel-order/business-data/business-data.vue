<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="handleSubmit()"
      >
        <template #formItemGroup>
          <a-form-item label="日期">
            <c-range-picker
              v-model="formModel.date"
              class="w100p"
              :allow-clear="false"
            />
          </a-form-item>
          <a-form-item label="直播间">
            <a-input-group class="w100p">
              <request-select
                v-model="formModel.live_room_id"
                request-url="/api/report/liveRoomList"
                label-key="live_room_name"
                value-key="live_room_id"
                :max-tag-count="2"
                @blur="handleSubmit()"
              />
              <a-checkbox
                :model-value="formModel.group_fields.includes('personage')"
                @change="changeGroupFields('personage')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="客服">
            <a-input-group class="w100p">
              <request-select
                v-model="formModel.service_user_fs_id"
                request-url="/api/travel/allServiceUser"
                label-key="user_name"
                value-key="fs_user_id"
                :send-params="{
                  no_has_dimission: true,
                }"
                @blur="handleSubmit()"
              />
              <a-checkbox
                :model-value="
                  formModel.group_fields.includes('service_user_fs_id')
                "
                @change="changeGroupFields('service_user_fs_id')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="旅游线路">
            <a-input-group class="w100p">
              <dict-select
                v-model="formModel.area"
                :data-list="dataCacheStore.lineList"
                label-key="line_name"
                value-key="line_name"
              />
              <a-checkbox
                :model-value="formModel.group_fields.includes('area')"
                @change="changeGroupFields('area')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="来源媒体">
            <a-input-group class="w100p">
              <dict-select
                v-model="formModel.source"
                :data-list="contactWayListM"
              />
              <a-checkbox
                :model-value="formModel.group_fields.includes('source')"
                @change="changeGroupFields('source')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="订单来源">
            <a-input-group class="w100p">
              <request-select
                v-model="formModel.order_source"
                request-url="/api/report/liveRoomList"
                :get-data-list="
                  (data) => [
                    { live_room_name: '内容私域' },
                    { live_room_name: '直播间私域' },
                    ...data,
                  ]
                "
                label-key="live_room_name"
                value-key="live_room_name"
                allow-clear
              />
              <a-checkbox
                :model-value="formModel.group_fields.includes('order_source')"
                @change="changeGroupFields('order_source')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="付款方式">
            <a-input-group class="w100p">
              <dict-select
                v-model="formModel.payment_type"
                :data-list="payTypeM"
              />
              <a-checkbox
                :model-value="formModel.group_fields.includes('payment_type')"
                @change="changeGroupFields('payment_type')"
              />
            </a-input-group>
          </a-form-item>
        </template>
      </search-form-fold>
      <div class="table-card-header mt-10">
        <div>
          <a-space> </a-space>
        </div>
        <a-space>
          <columns-select
            :the-field-map="allFieldsConfig"
            :need-format-column="false"
            :send-params="{ type: 'travel_business_data' }"
            :default-columns="columnsConfig.map((item) => item.dataIndex)"
            @select-columns-ok="setColumns"
          ></columns-select>
          <a-button type="primary" @click="exportAction">
            <template #icon>
              <icon-export />
            </template>
            导出
          </a-button>
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :auto-request="false"
        :send-params="tableParams"
        :scroll-percent="scrollPercent"
      >
        <template #cost="{ record }: TableColumnSlot">
          <a-space v-if="!record.isSummary && canEditCost">
            <span>{{ moneyFormat(record.cost) }}</span>
            <a-link
              @click="
                editRef?.show({
                  live_room_id: record.live_room_id,
                  live_room_name: record.live_room,
                  cost: record.cost || null,
                  accept_date: record.accept_date,
                })
              "
            >
              <icon-edit />
            </a-link>
          </a-space>
          <span v-else>{{ moneyFormat(record.cost) }}</span>
        </template>
      </base-table>
    </a-card>
    <live-room-cost-modal ref="editRef" @save="handleSubmit()" />
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive, computed } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import { TableColumnSlot } from '@/global';
  import ColumnsSelect from '@/components/columns-select/columns-select.vue';
  import { cloneDeep } from 'lodash';
  import { moneyFormatShow } from '@/utils/table-utils/columns-config';
  import dayjs from 'dayjs';
  import LiveRoomCostModal from '@/views/travel-order/live-room-report/live-room-cost-modal.vue';
  import { moneyFormat } from '@/utils/table-utils/table-util';
  import RequestSelect from '@/components/select/request-select.vue';
  import { useDataCacheStore } from '@/store';
  import {
    contactWayListM,
    payTypeM,
  } from '@/components/dict-select/dict-travel';

  const dataCacheStore = useDataCacheStore();
  const generateFormModel = () => {
    return {
      date: [
        dayjs().add(-6, 'day').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      group_fields: [] as string[],
      live_room_id: [],
      service_user_fs_id: [],
      area: [],
      source: [],
      order_source: [],
      payment_type: [],
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const editRef = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    return request('/api/report/businessDataReport', {
      ...data,
    });
  };

  const allFieldsConfig = [
    {
      title: '全部字段',
      dataIndex: '全部字段',
      keys: [] as string[],
      dataList: [
        {
          title: '成交订单数',
          dataIndex: 'order_num',
          description: '抖音来客获取的所有订单数量',
        },
        {
          title: '退款订单数',
          dataIndex: 'refund_order_num',
          description: '状态为已退款的订单数量',
        },
        {
          title: '退单率',
          dataIndex: 'refund_order_rate',
          description: '退款订单数/成交订单数*100%',
        },
        {
          title: '投流金额',
          dataIndex: 'cost',
        },
        {
          title: '订金',
          dataIndex: 'live_room_trading_amount',
          description: '订单列表的订金汇总',
          render: moneyFormatShow(),
        },
        {
          title: '退款金额',
          dataIndex: 'refund_order_amount',
          description: '退款订单的订金汇总',
          render: moneyFormatShow(),
        },
        {
          title: '平均客单成本',
          dataIndex: 'avg_order_cost',
          description: '投流金额/成交订单数',
          render: moneyFormatShow(),
        },
        {
          title: '有效客单成本',
          dataIndex: 'avg_valid_order_cost',
          description: '投流金额/（成交订单数-退款订单数）',
          render: moneyFormatShow(),
        },
        {
          title: '平均客单价',
          dataIndex: 'avg_order_amount',
          description: '订金/成交订单数',
          render: moneyFormatShow(),
        },
        {
          title: '总团款',
          dataIndex: 'estimated_trading_amount',
          description: '订单的总团款',
          render: moneyFormatShow(),
        },
        {
          title: '已添加微信数',
          dataIndex: 'add_wechat_num',
          description: '添加微信时间统计',
        },
        {
          title: '加微率',
          dataIndex: 'add_wechat_rate',
          description: '加微率=添加微信数/成交订单数*100%',
        },
        {
          title: '已确认订单数',
          dataIndex: 'ensure_order_num',
          description: '已确认订单的时间统计',
        },
        {
          title: '已发确认件订单数',
          dataIndex: 'send_ensure_order_count',
          description: '发送确认件的时间统计',
        },
        {
          title: '预约出行率',
          dataIndex: 'travel_order_rate',
          description: '已发确认件订单数/成交订单数',
        },
        {
          title: '出行订单数',
          dataIndex: 'travel_order_num',
          description: '已出行的时间统计',
        },
        {
          title: '取消预约订单数',
          dataIndex: 'cancel_order_num',
          description: '取消预约订单的时间统计',
        },
        {
          title: '核销单数',
          dataIndex: 'travel_back_num',
          description: '状态为‘已核销’的订单数',
        },
        {
          title: '核销金额',
          dataIndex: 'travel_back_amount',
          description: '状态为‘已核销’的订单总团款金额汇总',
          render: moneyFormatShow(),
        },
        {
          title: '核销率',
          dataIndex: 'travel_back_rate',
          description: '核销单数/成交订单数',
        },
        {
          title: '核销率（订金）',
          dataIndex: 'travel_back_amount_rate',
          description: '状态为‘已核销’的订单订金/订金汇总',
        },
        {
          title: '预估服务费',
          dataIndex: 'estimated_service_price',
          render: moneyFormatShow(),
        },
        {
          title: '利润',
          dataIndex: 'profit',
          description: '利润=总团款-结算成本-预估服务费',
          render: moneyFormatShow(),
        },
        {
          title: '利润率',
          dataIndex: 'profit_rate',
          description: '利润率=利润/总团款',
        },
      ],
    },
  ];
  allFieldsConfig.forEach((item) => {
    item.keys = item.dataList.map((citem) => {
      citem.key = citem.dataIndex;
      return citem.dataIndex;
    });
  });
  const columnsConfig = ref(cloneDeep(allFieldsConfig[0].dataList));

  const canEditCost = computed(
    () =>
      formModel.group_fields.length === 1 &&
      formModel.group_fields.includes('personage') &&
      !formModel.service_user_fs_id?.length &&
      !formModel.area?.length &&
      !formModel.source?.length &&
      !formModel.order_source?.length &&
      !formModel.payment_type?.length
  );
  const columns = computed(() =>
    [
      {
        title: '日期',
        dataIndex: 'accept_date',
        fixed: 'left',
        width: 110,
      },
      ...(formModel.group_fields.includes('personage')
        ? [
            {
              title: '直播间',
              dataIndex: 'live_room',
              width: 160,
            },
          ]
        : []),
      ...(((formModel.group_fields.length === 1 &&
        formModel.group_fields.includes('personage')) ||
        (!formModel.group_fields.length && formModel.live_room_id.length)) &&
      !formModel.service_user_fs_id?.length &&
      !formModel.area?.length &&
      !formModel.source?.length &&
      !formModel.order_source?.length &&
      !formModel.payment_type?.length
        ? [
            {
              title: '直播时长',
              dataIndex: 'live_hour_num',
              description: '直播间排班主播时长',
            },
          ]
        : []),
      ...(formModel.group_fields.includes('service_user_fs_id')
        ? [
            {
              title: '客服',
              dataIndex: 'service_user',
            },
          ]
        : []),
      ...(formModel.group_fields.includes('area')
        ? [
            {
              title: '旅游线路',
              dataIndex: 'area',
              width: 200,
            },
          ]
        : []),
      ...(formModel.group_fields.includes('source')
        ? [
            {
              title: '来源媒体',
              dataIndex: 'source',
            },
          ]
        : []),
      ...(formModel.group_fields.includes('order_source')
        ? [
            {
              title: '订单来源',
              dataIndex: 'order_source',
            },
          ]
        : []),
      ...(formModel.group_fields.includes('payment_type')
        ? [
            {
              title: '付款方式',
              dataIndex: 'payment_type',
            },
          ]
        : []),
      ...columnsConfig.value,
    ].filter(
      (item) =>
        !(
          !(
            (!formModel.group_fields.length ||
              (formModel.group_fields.length === 1 &&
                formModel.group_fields.includes('personage'))) &&
            !formModel.service_user_fs_id?.length &&
            !formModel.area?.length &&
            !formModel.source?.length &&
            !formModel.order_source?.length &&
            !formModel.payment_type?.length
          ) &&
          ['avg_valid_order_cost', 'avg_order_cost', 'cost'].includes(
            item.dataIndex
          )
        )
    )
  );

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function changeGroupFields(key: string) {
    if (!formModel.group_fields.includes(key)) {
      formModel.group_fields.push(key);
    } else {
      formModel.group_fields = formModel.group_fields.filter(
        (item) => item !== key
      );
    }
    if (!formModel.group_fields.length) {
      formModel.group_fields.push('accept_date');
    }
    handleSubmit();
  }

  function setColumns(val: any[]) {
    columnsConfig.value = val.filter((item) =>
      allFieldsConfig.some((citem) => citem.keys.includes(item.dataIndex))
    );
    handleSubmit();
  }

  const tableParams = computed(() => {
    return {
      ...formModel,
      date_begin: formModel.date?.[0],
      date_end: formModel.date?.[1],
    };
  });
  const scrollPercent = computed(() => ({
    maxHeight: '70vh',
    x: columns.value.length * 140,
  }));

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less"></style>
