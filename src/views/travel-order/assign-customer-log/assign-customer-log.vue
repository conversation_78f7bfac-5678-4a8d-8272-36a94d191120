<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="handleSubmit()"
      >
        <template #formItemGroup>
          <a-form-item label="发送时间">
            <a-range-picker v-model="formModel.add_time" allow-clear />
          </a-form-item>
          <a-form-item label="客服">
            <a-input v-model="formModel.service_user" allow-clear />
          </a-form-item>
          <a-form-item field="status" label="跟踪类型">
            <dict-select
              v-model="formModel.tracking_type"
              :data-list="trackingTypeListM"
            />
          </a-form-item>
        </template>
      </search-form-fold>

      <div class="table-card-header">
        <div> </div>
        <a-space>
          <!--<a-button type="primary" @click="exportAction">
            <template #icon>
              <icon-export />
            </template>
            导出
          </a-button>-->
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formModel"
        :scroll-percent="{ x: 1000, maxHeight: '80vh' }"
      >
        <template #action="{ record }: TableColumnSlot">
          <a-spin :loading="record.loading">
            <a-space>
              <a-link @click="showEditFn(record)"> <icon-edit />编辑 </a-link>
            </a-space>
          </a-spin>
        </template>
      </base-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';

  import { ref, reactive } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import {
    customerServiceStatusListM,
    trackingTypeListM,
  } from '@/components/dict-select/dict-travel';

  const generateFormModel = () => {
    return {
      add_time: [],
      tracking_type: null,
      service_user: null,
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    return request('/api/travel/sendOrderLog', {
      ...data,
    });
  };

  const editRef = ref();
  function showEditFn(record?: any) {
    editRef.value?.show(record);
  }

  const columns = [
    {
      title: '派单ID',
      dataIndex: 'id',
      width: 130,
    },
    {
      title: '客服',
      dataIndex: 'service_user',
      width: 130,
    },
    {
      title: '跟踪类型',
      dataIndex: 'tracking_type',
      width: 130,
    },
    {
      title: '询价单号',
      dataIndex: 'inquiry_order_no',
      width: 130,
    },
    {
      title: '订单编号',
      dataIndex: 'order_no',
      width: 130,
    },
    {
      title: '发送时间',
      dataIndex: 'add_time',
    },
    {
      title: '初始状态',
      dataIndex: 'status_before',
    },
    {
      title: '更新状态',
      dataIndex: 'add_time',
    },
    {
      title: '操作按钮',
      dataIndex: 'button',
    },
    {
      title: '处理时间',
      dataIndex: 'handle_time',
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less"></style>
