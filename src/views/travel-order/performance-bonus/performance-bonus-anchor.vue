<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="handleSubmit()"
      >
        <template #formItemGroup>
          <a-form-item label="类型">
            <a-radio-group
              v-model="formModel.type"
              type="button"
              @change="handleSubmit()"
            >
              <a-radio value="all">全部</a-radio>
              <a-radio value="clue">线索</a-radio>
              <a-radio value="order">订单</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="月份">
            <a-date-picker
              v-model="formModel.month"
              mode="month"
              value-format="YYYYMM"
              :allow-clear="false"
            />
          </a-form-item>
          <a-form-item label="角色">
            <request-select
              v-model="formModel.role_id"
              request-url="/api/user/roleList"
              label-key="role_name"
              @change="formModel.user_ids = []"
            />
          </a-form-item>
          <a-form-item label="姓名">
            <request-select
              v-model="formModel.user_ids"
              api="user"
              :send-params="{
                roles: formModel.role_id ? [formModel.role_id] : [],
              }"
            />
          </a-form-item>
        </template>
      </search-form-fold>

      <div class="table-card-header mt-10">
        <div> </div>
        <a-space>
          <a-button type="primary" @click="exportAction()">
            <template #icon>
              <icon-export />
            </template>
            导出
          </a-button>
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        class="mt-10"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formModel"
        table-type="simple"
      >
        <template
          v-for="item in columnTitle"
          :key="item.dataIndex"
          #[`${item.dataIndex}_title`]
        >
          <span class="ai-cen">
            <span>{{ item.dtitle }}</span>
            <icon-plus-circle
              v-if="!expandKey[item.dataIndex]"
              class="a-text ml-5"
              size="18"
              @click="expandKey[item.dataIndex] = !expandKey[item.dataIndex]"
            />
            <icon-minus-circle
              v-else
              class="a-text ml-5"
              size="18"
              @click="expandKey[item.dataIndex] = !expandKey[item.dataIndex]"
            />
          </span>
        </template>
      </base-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import dayjs from 'dayjs';
  import { moneyFormatShow } from '@/utils/table-utils/columns-config';

  const generateFormModel = () => {
    return {
      month: dayjs().add(-1, 'day').format('YYYYMM'),
      role_id: null,
      user_ids: [],
      type: 'all',
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    return request('/api/performance/anchorPerformance', {
      ...data,
    });
  };

  const expandKey = ref<any>({});
  const columns = computed(() => {
    switch (formModel.type) {
      case 'all':
        return [
          {
            title: '时间',
            dataIndex: 'month',
            width: 110,
            fixed: 'left',
          },
          {
            title: '姓名',
            dataIndex: 'user_name',
            width: 110,
            fixed: 'left',
          },
          {
            title: '角色',
            dataIndex: 'role_name',
          },
          {
            title: '直播间订金',
            dataIndex: 'advance_price',
            render: moneyFormatShow(),
          },
          {
            title: '直播账号线索订单核销总团款',
            dataIndex: 'clue_total_amount',
            render: moneyFormatShow(),
          },
          {
            title: '核销订单数',
            dataIndex: 'total_end_num',
          },
          {
            title: '核销人数',
            dataIndex: 'total_end_people_num',
          },
          {
            dtitle: '月订单确认率',
            dataIndex: 'total_order_ensure_rate',
          },
          ...(expandKey.value.total_order_ensure_rate
            ? [
                {
                  title: '总订单数',
                  dataIndex: 'total_order_num',
                  headerCellClass: 'erji',
                },
                {
                  title: '发确认件订单数',
                  dataIndex: 'total_ensure_num',
                  headerCellClass: 'erji',
                },
              ]
            : []),
          {
            dtitle: '毛利率',
            dataIndex: 'total_profit_rate',
          },
          ...(expandKey.value.total_profit_rate
            ? [
                {
                  title: '发确认件订单总团款',
                  dataIndex: 'total_amount_ensure',
                  headerCellClass: 'erji',
                  render: moneyFormatShow(),
                },
                {
                  title: '发确认件订单利润',
                  dataIndex: 'total_profit_ensure',
                  headerCellClass: 'erji',
                  render: moneyFormatShow(),
                },
              ]
            : []),
          {
            title: '实际完成度',
            dataIndex: 'complete_rate',
          },
          {
            title: '目标完成度',
            dataIndex: 'complete_goal',
          },
          {
            title: '完成度系数',
            dataIndex: 'complete_coefficient',
          },
          {
            dtitle: '提成金额',
            dataIndex: 'total_commission',
            render: moneyFormatShow(),
          },
          ...(expandKey.value.total_commission
            ? [
                {
                  title: '直播间提成',
                  dataIndex: 'commission_live_amount',
                  headerCellClass: 'erji',
                  render: moneyFormatShow(),
                },
                {
                  title: '直播账号线索提成',
                  dataIndex: 'commission_clue_amount',
                  headerCellClass: 'erji',
                  render: moneyFormatShow(),
                },
              ]
            : []),
        ].filter((item) => item);
      case 'clue':
        return [
          {
            title: '时间',
            dataIndex: 'month',
          },
          {
            title: '姓名',
            dataIndex: 'user_name',
          },
          {
            title: '角色',
            dataIndex: 'role_name',
          },
          {
            dtitle: '直播账号线索订单核销总团款',
            dataIndex: 'clue_total_amount',
            render: moneyFormatShow(),
          },
          ...(expandKey.value.clue_total_amount
            ? [
                {
                  title: '直播账号线索数',
                  dataIndex: 'clue_total_num',
                  headerCellClass: 'erji',
                },
              ]
            : []),
          {
            title: '核销订单数',
            dataIndex: 'clue_order_end_num',
          },
          {
            title: '核销人数',
            dataIndex: 'clue_order_end_people_num',
          },
          {
            dtitle: '月订单确认率',
            dataIndex: 'clue_order_ensure_rate',
          },
          ...(expandKey.value.clue_order_ensure_rate
            ? [
                {
                  title: '直播账号线索订单数',
                  dataIndex: 'clue_order_num',
                  headerCellClass: 'erji',
                },
                {
                  title: '发确认件订单数',
                  dataIndex: 'clue_order_ensure_num',
                  headerCellClass: 'erji',
                },
              ]
            : []),
          {
            dtitle: '毛利率',
            dataIndex: 'clue_profit_rate',
          },
          ...(expandKey.value.clue_profit_rate
            ? [
                {
                  title: '发确认件订单总团款',
                  dataIndex: 'clue_amount_ensure',
                  headerCellClass: 'erji',
                  render: moneyFormatShow(),
                },
                {
                  title: '发确认件订单利润',
                  dataIndex: 'clue_profit_ensure',
                  headerCellClass: 'erji',
                  render: moneyFormatShow(),
                },
              ]
            : []),
        ].filter((item) => item);
      case 'order':
        return [
          {
            title: '时间',
            dataIndex: 'month',
          },
          {
            title: '姓名',
            dataIndex: 'user_name',
          },
          {
            title: '角色',
            dataIndex: 'role_name',
          },
          {
            title: '直播间订金',
            dataIndex: 'advance_price',
            render: moneyFormatShow(),
          },
          {
            title: '核销订单数',
            dataIndex: 'order_end_num',
          },
          {
            title: '核销人数',
            dataIndex: 'order_end_people_num',
          },
          {
            dtitle: '月订单确认率',
            dataIndex: 'order_ensure_rate',
          },
          ...(expandKey.value.order_ensure_rate
            ? [
                {
                  title: '直播账号线索订单数',
                  dataIndex: 'order_num',
                  headerCellClass: 'erji',
                },
                {
                  title: '发确认件订单数',
                  dataIndex: 'order_ensure_num',
                  headerCellClass: 'erji',
                },
              ]
            : []),
          {
            dtitle: '毛利率',
            dataIndex: 'profit_rate',
          },
          ...(expandKey.value.profit_rate
            ? [
                {
                  title: '发确认件订单总团款',
                  dataIndex: 'amount_ensure',
                  headerCellClass: 'erji',
                  render: moneyFormatShow(),
                },
                {
                  title: '发确认件订单利润',
                  dataIndex: 'profit_ensure',
                  headerCellClass: 'erji',
                  render: moneyFormatShow(),
                },
              ]
            : []),
        ].filter((item) => item);
      default:
        return [];
    }
  });

  const columnTitle = computed(() =>
    columns.value.filter((item) => item.dtitle)
  );

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less"></style>
