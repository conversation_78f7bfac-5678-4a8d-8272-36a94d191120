<template>
  <a-card>
    <a-tabs type="card-gutter" lazy-load>
      <a-tab-pane
        v-if="userStore.hasPermission(1)"
        key="完成度目标"
        title="完成度目标"
      >
        <performance-bonus-goal></performance-bonus-goal>
      </a-tab-pane>
      <a-tab-pane key="主播提成" title="主播提成">
        <performance-bonus-anchor></performance-bonus-anchor>
      </a-tab-pane>
      <a-tab-pane key="新媒体运营提成" title="新媒体运营提成">
        <performance-bonus-media-operator></performance-bonus-media-operator>
      </a-tab-pane>
      <a-tab-pane key="电销提成" title="电销提成">
        <performance-bonus-telemarketing></performance-bonus-telemarketing>
      </a-tab-pane>
      <a-tab-pane key="直播运营提成" title="直播运营提成">
        <performance-bonus-other
          data-type="10"
          :get-columns="(expandKey:any)=>[
            {
              dtitle: '核销金额',
              dataIndex: 'order_end_amount',
              render: moneyFormatShow()
            },
            ...(expandKey.value.order_end_amount
              ? [
                  {
                    title: '直播间核销金额',
                    dataIndex: 'order_end_account_1',
                    headerCellClass: 'erji'
                  },
                  {
                    title: '直播账号线索核销金额',
                    dataIndex: 'order_end_account_2',
                    headerCellClass: 'erji'
                  }
            ]: [])
          ]"
        ></performance-bonus-other>
      </a-tab-pane>
      <a-tab-pane key="流量投放提成" title="流量投放提成">
        <performance-bonus-other
          data-type="11"
          :get-columns="(expandKey:any)=>[
            {
              dtitle: '核销金额',
              dataIndex: 'order_end_amount',
              render: moneyFormatShow()
            },
            ...(expandKey.value.order_end_amount
              ? [
                  {
                    title: '内容号线索订单核销金额',
                    dataIndex: 'order_end_account_1',
                    headerCellClass: 'erji'
                  },
                  {
                    title: '公海线索订单核销金额',
                    dataIndex: 'order_end_account_2',
                    headerCellClass: 'erji'
                  }
            ]: [])
          ]"
        ></performance-bonus-other>
      </a-tab-pane>
      <a-tab-pane key="经纪人提成" title="经纪人提成">
        <performance-bonus-other
          data-type="9"
          :get-columns="(expandKey:any)=>[
            {
              dtitle: '核销金额',
              dataIndex: 'order_end_amount',
              render: moneyFormatShow()
            },
            ...(expandKey.value.order_end_amount
              ? [
                  {
                    title: '直播间订单核销金额',
                    dataIndex: 'order_end_account_1',
                    headerCellClass: 'erji',
                    render: moneyFormatShow()
                  },
                  {
                    title: '直播账号线索订单核销金额',
                    dataIndex: 'order_end_account_2',
                    headerCellClass: 'erji',
                    render: moneyFormatShow()
                  }
            ]: [])
          ]"
        ></performance-bonus-other>
      </a-tab-pane>
      <a-tab-pane key="产品规划师提成" title="产品规划师提成">
        <performance-bonus-other
          data-type="13"
          :get-columns="
            () => [
              {
                dtitle: '核销金额',
                dataIndex: 'order_end_amount',
                render: moneyFormatShow(),
              },
            ]
          "
        ></performance-bonus-other>
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>

<script setup lang="ts">
  import { moneyFormatShow } from '@/utils/table-utils/columns-config';
  import { useUserStore } from '@/store';
  import PerformanceBonusOther from './performance-bonus-other.vue';
  import PerformanceBonusAnchor from './performance-bonus-anchor.vue';
  import PerformanceBonusGoal from './performance-bonus-goal.vue';
  import PerformanceBonusMediaOperator from './performance-bonus-media-operator.vue';
  import PerformanceBonusTelemarketing from './performance-bonus-telemarketing.vue';

  const userStore = useUserStore();
</script>

<style scoped lang="less">
  :deep(.arco-tabs-content) {
    padding-top: 0;
  }

  :deep(.table-card) {
    margin-top: 0;
  }
</style>
