<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="handleSubmit()"
      >
        <template #formItemGroup>
          <a-form-item label="月份">
            <a-date-picker
              v-model="formModel.month"
              mode="month"
              value-format="YYYYMM"
              :allow-clear="false"
            />
          </a-form-item>
          <a-form-item label="角色">
            <request-select
              v-model="formModel.role_id"
              request-url="/api/user/roleList"
              label-key="role_name"
              @change="formModel.user_ids = []"
            />
          </a-form-item>
          <a-form-item label="姓名">
            <request-select
              v-model="formModel.user_ids"
              api="user"
              :send-params="{
                roles: formModel.role_id ? [formModel.role_id] : [],
              }"
            />
          </a-form-item>
        </template>
      </search-form-fold>

      <div class="table-card-header mt-10">
        <div> </div>
        <a-space>
          <a-button type="primary" @click="exportAction()">
            <template #icon>
              <icon-export />
            </template>
            导出
          </a-button>
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        class="mt-10"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formModel"
        table-type="simple"
      >
        <template
          v-for="item in columnTitle"
          :key="item.dataIndex"
          #[`${item.dataIndex}_title`]
        >
          <span class="ai-cen">
            <span>{{ item.dtitle }}</span>
            <icon-plus-circle
              v-if="!expandKey[item.dataIndex]"
              class="a-text ml-5"
              size="18"
              @click="expandKey[item.dataIndex] = !expandKey[item.dataIndex]"
            />
            <icon-minus-circle
              v-else
              class="a-text ml-5"
              size="18"
              @click="expandKey[item.dataIndex] = !expandKey[item.dataIndex]"
            />
          </span>
        </template>
      </base-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import dayjs from 'dayjs';
  import { moneyFormatShow } from '@/utils/table-utils/columns-config';

  const props = defineProps({
    getColumns: {
      type: Function,
      default: () => () => [],
    },
    dataType: {
      type: String,
      default: '',
    },
  });
  const generateFormModel = () => {
    return {
      month: dayjs().add(-1, 'day').format('YYYYMM'),
      role_id: null,
      user_ids: [],
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    return request('/api/performance/otherPerformance', {
      ...data,
      data_type: props.dataType,
    });
  };

  const expandKey = ref<any>({});
  const columns = computed(() => {
    return [
      {
        title: '时间',
        dataIndex: 'month',
        width: 110,
        fixed: 'left',
      },
      {
        title: '姓名',
        dataIndex: 'user_name',
        width: 110,
        fixed: 'left',
      },
      {
        title: '角色',
        dataIndex: 'role_name',
      },
      ...props.getColumns(expandKey),
      {
        title: '核销订单数',
        dataIndex: 'order_end_num',
      },
      {
        title: '核销人数',
        dataIndex: 'order_end_people_num',
      },
      {
        dtitle: '月订单确认率',
        dataIndex: 'order_ensure_rate',
      },
      ...(expandKey.value.order_ensure_rate
        ? [
            {
              title: '总订单数',
              dataIndex: 'order_num',
              headerCellClass: 'erji',
            },
            {
              title: '发确认件订单数',
              dataIndex: 'order_ensure_num',
              headerCellClass: 'erji',
            },
          ]
        : []),
      {
        dtitle: '毛利率',
        dataIndex: 'profit_rate',
      },
      ...(expandKey.value.profit_rate
        ? [
            {
              title: '发确认件订单总团款',
              dataIndex: 'amount_ensure',
              headerCellClass: 'erji',
              render: moneyFormatShow(),
            },
            {
              title: '发确认件订单利润',
              dataIndex: 'profit_ensure',
              headerCellClass: 'erji',
              render: moneyFormatShow(),
            },
          ]
        : []),
      {
        title: '实际完成度',
        dataIndex: 'complete_rate',
      },
      {
        title: '目标完成度',
        dataIndex: 'complete_goal',
      },
      {
        title: '完成度系数',
        dataIndex: 'complete_coefficient',
      },
      {
        title: '提成金额',
        dataIndex: 'commission_amount',
        render: moneyFormatShow(),
      },
    ].filter((item) => item);
  });

  const columnTitle = computed(() =>
    columns.value.filter((item) => item.dtitle)
  );

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less"></style>
