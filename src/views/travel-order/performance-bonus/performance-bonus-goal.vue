<template>
  <div>
    <a-card size="small" class="table-card">
      <div class="table-card-header">
        <a-space>
          <a-date-picker
            v-model="formModel.year"
            mode="year"
            :allow-clear="false"
            @change="handleSubmit()"
          ></a-date-picker>
        </a-space>
        <a-space>
          <a-button type="primary" :loading="exporting" @click="exportAction">
            <template #icon>
              <icon-export />
            </template>
            导出
          </a-button>
        </a-space>
      </div>
      <!--table 区域-->
      <a-table
        :loading="loading"
        :columns="columns"
        :data="list"
        :pagination="false"
        row-key="field"
      >
        <template
          v-for="item in 12"
          :key="item"
          #[`slot_${item}`]="{ record, column }: TableColumnSlot"
        >
          <span v-if="column[record.field]">{{ column[record.field] }}%</span>
          <span v-else>-</span>
          <icon-edit
            v-if="['profit_goal', 'order_ensure_goal'].includes(record.field)"
            class="a-text"
            @click="showModal(record, column)"
          />
        </template>
      </a-table>
    </a-card>
    <d-modal
      :visible="visible"
      :title="title"
      width="500px"
      :ok-loading="modalLoading"
      unmount-on-close
      @ok="setGoal"
      @cancel="visible = false"
    >
      <a-form-item label="目标">
        <a-input-number v-model="modalGoal" :min="0" :precision="2" hide-button>
          <template #suffix>%</template>
        </a-input-number>
      </a-form-item>
    </d-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed } from 'vue';
  import request from '@/api/request';
  import dayjs from 'dayjs';
  import { Message, TableColumnData } from '@arco-design/web-vue';
  import DModal from '@/components/d-modal/d-modal.vue';

  const generateFormModel = () => {
    return {
      year: dayjs().format('YYYY'),
    };
  };
  const loading = ref(false);
  const visible = ref(false);
  const modalLoading = ref(false);
  const exporting = ref(false);
  const list = ref([
    { goalTitle: '月订单确认率目标', field: 'order_ensure_goal' },
    { goalTitle: '毛利率目标', field: 'profit_goal' },
    { goalTitle: '目标完成度', field: 'completion_goal' },
  ]);
  const formModel = reactive(generateFormModel());
  const columns = ref<TableColumnData[]>([]);
  const modalGoal = ref<number | null>(null);
  const currentRow = ref<any>({});
  const title = computed(() => {
    return `${currentRow.value?.goalTitle}【${currentRow.value?.year}年${currentRow.value?.month}月】`;
  });

  const getList = () => {
    loading.value = true;
    request('/api/performance/list', {
      ...formModel,
    })
      .then(({ data }) => {
        columns.value = data.map((item: any) => ({
          ...item,
          title: `${item.month}月`,
          dataIndex: String(item.month),
          slotName: `slot_${item.month}`,
        }));
        columns.value.unshift({
          title: `目标完成度配置`,
          dataIndex: 'goalTitle',
        });
      })
      .finally(() => {
        loading.value = false;
      });
  };
  getList();

  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    getList();
  };
  function exportAction() {
    exporting.value = true;
    request('/api/performance/list', {
      ...formModel,
      export: true,
      export_now: true,
    }).finally(() => {
      exporting.value = false;
    });
  }

  function showModal(row: any, column: any) {
    currentRow.value = { ...row, ...column };
    visible.value = true;
    modalGoal.value = column[row.field] ? parseFloat(column[row.field]) : null;
  }

  function setGoal() {
    modalLoading.value = true;
    request('/api/performance/save', {
      ...currentRow.value,
      year: currentRow.value.year,
      month: currentRow.value.month,
      [currentRow.value.field]: modalGoal.value,
    })
      .then(() => {
        Message.success('保存成功');
        getList();
        visible.value = false;
      })
      .finally(() => {
        modalLoading.value = false;
      });
  }
</script>

<style scoped lang="less"></style>
