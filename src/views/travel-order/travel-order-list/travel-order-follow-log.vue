<template>
  <d-modal
    v-model:visible="visible"
    title="电话跟进记录"
    width="800px"
    unmount-on-close
    :footer="null"
  >
    <base-table :columns-config="columns" :data-config="getList" no-pagination>
    </base-table>
  </d-modal>
</template>

<script lang="ts" setup>
  import DModal from '@/components/d-modal/d-modal.vue';
  import { computed, ref } from 'vue';
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import request from '@/api/request';

  const columns = [
    { title: '跟进人', dataIndex: 'service_user' },
    { title: '跟进时间', dataIndex: 'add_time' },
  ];

  const visible = ref(false);
  const info = ref<any>({});

  const show = (data: any) => {
    visible.value = true;
    info.value = data;
  };

  const getList = async () => {
    return request('/api/travel/phoneFollowUpDetail', {
      order_no: info.value.order_no,
    });
  };

  defineExpose({
    show,
  });
</script>

<style scoped></style>
