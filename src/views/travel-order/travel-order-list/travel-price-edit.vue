<template>
  <div>
    <d-modal
      v-model:visible="visible"
      :width="shouldShowConfirmationInfo ? '1200px' : '860px'"
      :body-style="{
        maxHeight: 'calc(100vh - 150px)',
      }"
      :mask-closable="false"
      :esc-to-close="false"
      @cancel="handleCancel"
    >
      <template #title>
        <div class="modal-title-container">
          <span class="modal-title-text">
            <template v-if="formModel.transfer_offline_order">
              转线下（{{ formModel.transfer_offline_order }}）
            </template>
            <template v-else-if="formModel.order_settle">
              订单结算（{{ formModel.order_info.order_no }}）
            </template>
            <template v-else-if="isEdit">
              编辑订单【{{ formModel.order_info.order_no || '' }}】
            </template>
            <template v-else> 新建订单 </template>
          </span>
          <!-- 订单状态标签 - 只在编辑模式下显示 -->
          <a-tag
            v-if="isEdit && originOrderInfo.status"
            :color="getOrderStatusColor(originOrderInfo.status)"
            size="small"
          >
            {{ originOrderInfo.status }}
          </a-tag>
        </div>
      </template>
      <!-- 主要内容区域 -->
      <div
        class="modal-content-wrapper"
        :class="{ 'with-confirmation': shouldShowConfirmationInfo }"
      >
        <!-- 左侧编辑区域 -->
        <div class="edit-section">
          <a-form ref="formRef" :model="formModel" :auto-label-width="true">
            <div class="form-title">询价单信息</div>
            <a-divider>基础信息</a-divider>
            <a-row :gutter="[10, 0]">
              <a-col :span="12">
                <a-form-item label="客户昵称">
                  <a-input
                    v-model="formModel.inquiry_order_info.customer_name"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="旅游线路"
                  field="inquiry_order_info.line_id"
                  :rules="requiredRule"
                >
                  <dict-select
                    v-model="formModel.inquiry_order_info.line_id"
                    :data-list="dataStore.lineList"
                    label-key="line_name"
                    value-key="line_id"
                    :allow-clear="false"
                    @change="lineChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="地接社">
                  <dict-select
                    v-model="formModel.inquiry_order_info.agency_id"
                    :data-list="
                      formModel.inquiry_order_info.line_id
                        ? dataStore.getProductAboutItem(
                            formModel.inquiry_order_info.line_id
                          )?.child || []
                        : []
                    "
                    label-key="agency_name"
                    value-key="agency_id"
                    :allow-clear="false"
                    @change="agencyChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item
                  label="产品"
                  field="inquiry_order_info.product_id"
                  :rules="requiredRule"
                >
                  <dict-select
                    v-model="formModel.inquiry_order_info.product_id"
                    label-key="product_name"
                    value-key="product_id"
                    :data-list="
                      formModel.inquiry_order_info.line_id
                        ? dataStore.getProductList(
                            formModel.inquiry_order_info.line_id,
                            formModel.inquiry_order_info.agency_id
                          )
                        : []
                    "
                    :allow-clear="false"
                    @change="productChange"
                  />
                </a-form-item>
              </a-col>
              <template v-if="!fromClueCreate">
                <a-col :span="12">
                  <a-form-item
                    label="成人人数"
                    field="inquiry_order_info.adult_num"
                    :rules="requiredRule"
                  >
                    <a-input-number
                      v-model="formModel.inquiry_order_info.adult_num"
                      :min="1"
                      placeholder="请输入"
                      :precision="0"
                      @change="
                        formModel.inquiry_order_info.standard_room =
                          formModel.inquiry_order_info.adult_num
                      "
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="儿童人数">
                    <a-input-number
                      v-model="formModel.inquiry_order_info.children_num"
                      placeholder="请输入"
                      :min="0"
                      :precision="0"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item
                    label="房间信息"
                    field="order_info.room_info"
                    :rules="requiredRule"
                  >
                    <a-input
                      v-model="formModel.order_info.room_info"
                      allow-clear
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
                <!-- <a-col :span="12">
                <a-form-item label="床位数">
                  <a-input-number
                    v-model="formModel.inquiry_order_info.standard_room"
                    placeholder="请输入"
                    :precision="0"
                  />
                </a-form-item>
              </a-col>-->
                <a-col :span="12">
                  <a-form-item
                    label="总团款"
                    field="inquiry_order_info.total_cut_price"
                    :rules="requiredRule"
                  >
                    <a-input-number
                      v-model="formModel.inquiry_order_info.total_cut_price"
                      placeholder="请输入"
                      :precision="2"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item
                    label="结算价"
                    field="inquiry_order_info.total_final_price"
                    :rules="requiredRule"
                  >
                    <a-input-number
                      v-model="formModel.inquiry_order_info.total_final_price"
                      placeholder="请输入"
                      :precision="2"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item
                    label="结算价说明"
                    field="inquiry_order_info.total_final_price_detail"
                    :rules="requiredRule"
                  >
                    <a-textarea
                      v-model="
                        formModel.inquiry_order_info.total_final_price_detail
                      "
                      auto-size
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="预估服务费">
                    <a-input-number
                      v-model="formModel.order_info.estimated_service_price"
                      placeholder="请输入"
                      :precision="2"
                    />
                  </a-form-item>
                </a-col>
                <!-- 折叠按钮 -->
                <a-col :span="24" class="advanced-fields-toggle">
                  <a-button
                    type="text"
                    size="small"
                    @click="
                      isAdvancedFieldsExpanded = !isAdvancedFieldsExpanded
                    "
                  >
                    <template #icon>
                      <icon-down
                        v-if="!isAdvancedFieldsExpanded"
                        :style="{ transition: 'transform 0.3s' }"
                      />
                      <icon-up
                        v-else
                        :style="{ transition: 'transform 0.3s' }"
                      />
                    </template>
                    {{ isAdvancedFieldsExpanded ? '收起' : '展开更多' }}
                  </a-button>
                </a-col>
                <!-- 可折叠的高级字段 -->
                <template v-if="isAdvancedFieldsExpanded">
                  <a-col :span="12">
                    <a-form-item label="机票支出">
                      <a-input-number
                        v-model="formModel.order_info.air_ticket_expend"
                        placeholder="请输入"
                        :precision="2"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="票务公司">
                      <dict-select
                        v-model="formModel.inquiry_order_info.ticket_company"
                        :data-list="ticketingPlatformM"
                      />
                    </a-form-item>
                  </a-col>
                </template>
                <!--编辑才有的选项-->
                <template v-if="isEdit && isAdvancedFieldsExpanded">
                  <a-col :span="24">
                    <a-form-item label="赔付金额">
                      <a-input-number
                        v-model="formModel.order_info.compensate_amount"
                        placeholder="请输入"
                        :precision="2"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="24">
                    <a-form-item label="赔付证明">
                      <a-space align="center">
                        <upload-image-file
                          v-model="
                            formModel.order_info.compensate_explain_image_1
                          "
                          :multiple="false"
                          :limit="1"
                          :send-params="{ type: 'order_compensate' }"
                          tip="转账记录截图"
                        >
                        </upload-image-file>
                        <upload-image-file
                          v-model="
                            formModel.order_info.compensate_explain_image_2
                          "
                          :multiple="false"
                          :limit="1"
                          :send-params="{ type: 'order_compensate' }"
                          tip="和解协议截图"
                        >
                        </upload-image-file>
                      </a-space>
                    </a-form-item>
                  </a-col>
                  <a-col :span="24">
                    <a-form-item
                      label="客服其他收入和支出"
                      tooltip="用于填写后续增加的线路、升级内容、车辆、赔款等信息"
                    >
                      <a-card class="w100p card-b">
                        <a-row :gutter="10" align="center">
                          <a-col flex="200px">内容</a-col>
                          <a-col flex="1"> 客服其他收入</a-col>
                          <a-col flex="1"> 客服其他支出</a-col>
                          <a-col flex="100px"> 操作</a-col>
                        </a-row>
                        <a-row :gutter="10" align="center" class="mt-10">
                          <a-col flex="200px">汇总</a-col>
                          <a-col flex="1">
                            {{
                              formModel.order_info.service_other_income_total
                            }}
                          </a-col>
                          <a-col flex="1">
                            {{
                              formModel.order_info.service_other_expend_total
                            }}
                          </a-col>
                          <a-col flex="100px"></a-col>
                        </a-row>
                        <a-row
                          v-for="(item, index) in formModel.order_info
                            .service_other_detail"
                          :key="index"
                          :gutter="10"
                          class="mt-10"
                          align="center"
                        >
                          <a-col flex="200px">
                            <span v-if="item.name === '其他'">{{
                              item.name
                            }}</span>
                            <request-select
                              v-else
                              v-model="item.name"
                              placeholder="请输入关键词"
                              allow-create
                              request-url="/api/getEnums"
                              add-url="/api/setEnums"
                              add-key="name"
                              label-key="name"
                              value-key="name"
                              :allow-clear="false"
                              :send-params="{
                                type: 'service_other_detail',
                                num: enmuNum,
                              }"
                              @save="enmuNum++"
                            />
                          </a-col>
                          <a-col flex="1">
                            <a-input-number
                              v-model="item.income"
                              :precision="2"
                            />
                          </a-col>
                          <a-col flex="1">
                            <a-input-number
                              v-model="item.expend"
                              :precision="2"
                            />
                          </a-col>
                          <a-col flex="100px">
                            <a-space>
                              <a-button
                                type="primary"
                                size="small"
                                @click="
                                  () =>
                                    formModel.order_info.service_other_detail.splice(
                                      index + 1,
                                      0,
                                      { name: '', income: null, expend: null }
                                    )
                                "
                              >
                                添加
                              </a-button>
                              <a-button
                                type="primary"
                                size="small"
                                status="danger"
                                :disabled="item.name === '其他'"
                                @click="
                                  () =>
                                    formModel.order_info.service_other_detail.splice(
                                      index,
                                      1
                                    )
                                "
                              >
                                删除
                              </a-button>
                            </a-space>
                          </a-col>
                        </a-row>
                      </a-card>
                    </a-form-item>
                  </a-col>
                  <a-col :span="24">
                    <a-form-item
                      label="合同其他收入和支出"
                      tooltip="用于填写后续增加的古城维护费、保险、游船、索道等收入和退款支出"
                    >
                      <a-card class="w100p card-b">
                        <a-row :gutter="10" align="center">
                          <a-col flex="200px">内容</a-col>
                          <a-col flex="1"> 合同其他收入</a-col>
                          <a-col flex="1"> 合同其他支出</a-col>
                          <a-col flex="100px"> 操作</a-col>
                        </a-row>
                        <a-row :gutter="10" align="center" class="mt-10">
                          <a-col flex="200px">汇总</a-col>
                          <a-col flex="1">
                            {{
                              formModel.order_info.contract_other_income_total
                            }}
                          </a-col>
                          <a-col flex="1">
                            {{
                              formModel.order_info.contract_other_expend_total
                            }}
                          </a-col>
                          <a-col flex="100px"></a-col>
                        </a-row>
                        <a-row
                          v-for="(item, index) in formModel.order_info
                            .contract_other_detail"
                          :key="index"
                          :gutter="10"
                          class="mt-10"
                          align="center"
                        >
                          <a-col flex="200px">
                            <span v-if="item.name === '其他'">{{
                              item.name
                            }}</span>
                            <request-select
                              v-else
                              v-model="item.name"
                              placeholder="请输入关键词"
                              allow-create
                              request-url="/api/getEnums"
                              add-url="/api/setEnums"
                              add-key="name"
                              label-key="name"
                              value-key="name"
                              :allow-clear="false"
                              :send-params="{
                                type: 'contract_other_detail',
                                num: enmuNum,
                              }"
                              @save="enmuNum++"
                            />
                          </a-col>
                          <a-col flex="1">
                            <a-input-number
                              v-model="item.income"
                              :precision="2"
                            />
                          </a-col>
                          <a-col flex="1">
                            <a-input-number
                              v-model="item.expend"
                              :precision="2"
                            />
                          </a-col>
                          <a-col flex="100px">
                            <a-space>
                              <a-button
                                type="primary"
                                size="small"
                                @click="
                                  () =>
                                    formModel.order_info.contract_other_detail.splice(
                                      index + 1,
                                      0,
                                      { name: '', income: null, expend: null }
                                    )
                                "
                              >
                                添加
                              </a-button>
                              <a-button
                                type="primary"
                                size="small"
                                status="danger"
                                :disabled="item.name === '其他'"
                                @click="
                                  () =>
                                    formModel.order_info.contract_other_detail.splice(
                                      index,
                                      1
                                    )
                                "
                              >
                                删除
                              </a-button>
                            </a-space>
                          </a-col>
                        </a-row>
                      </a-card>
                    </a-form-item>
                  </a-col>
                </template>
                <a-col :span="24">
                  <a-form-item label="利润">
                    <template #label>
                      <span style="color: rgb(var(--arcoblue-6))">利润</span>
                    </template>
                    <span style="color: rgb(var(--arcoblue-6))">
                      ¥{{ formModel.order_info.profit || '-' }}
                    </span>
                  </a-form-item>
                </a-col>
              </template>
            </a-row>
            <template v-if="!fromClueCreate">
              <a-divider>出行信息</a-divider>
              <a-row :gutter="[10, 0]">
                <a-col :span="12">
                  <a-form-item
                    label="出行日期"
                    field="inquiry_order_info.real_travel_start_date"
                    :rules="requiredRule"
                  >
                    <a-date-picker
                      v-model="
                        formModel.inquiry_order_info.real_travel_start_date
                      "
                      class="w100p"
                      allow-clear
                    ></a-date-picker>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item
                    label="返程日期"
                    field="inquiry_order_info.real_travel_end_date"
                    :rules="requiredRule"
                  >
                    <a-date-picker
                      v-model="
                        formModel.inquiry_order_info.real_travel_end_date
                      "
                      class="w100p"
                      allow-clear
                    ></a-date-picker>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="出发城市">
                    <a-input
                      v-model="formModel.order_info.departure_city"
                      allow-clear
                    ></a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="返回城市">
                    <a-input
                      v-model="formModel.order_info.return_city"
                      allow-clear
                    ></a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="订单明细说明">
                    <a-textarea
                      v-model="formModel.inquiry_order_info.remark"
                      placeholder="请输入"
                      :auto-size="{ minRows: 3, maxRows: 10 }"
                    ></a-textarea>
                  </a-form-item>
                </a-col>
              </a-row>
            </template>
            <div class="form-title">订单信息</div>
            <a-divider>基础信息</a-divider>
            <a-row :gutter="[10, 0]">
              <a-col :span="12">
                <a-form-item
                  label="来源媒体"
                  field="order_info.source"
                  :rules="requiredRule"
                >
                  <dict-select
                    v-model="formModel.order_info.source"
                    :data-list="contactWayListM"
                    :allow-clear="false"
                  />
                </a-form-item>
              </a-col>

              <a-col :span="12">
                <a-form-item
                  label="付款方式"
                  field="order_info.payment_type"
                  :rules="{
                    required: true,
                    message: '请选择',
                    trigger: 'blur',
                  }"
                >
                  <dict-select
                    v-model="formModel.order_info.payment_type"
                    :data-list="payTypeM"
                    :allow-clear="false"
                  />
                </a-form-item>
              </a-col>
              <template v-if="!formModel.inquiry_order_info.thread_id">
                <a-col :span="12">
                  <a-form-item
                    label="订单类型"
                    field="order_info.order_new_type"
                    :rules="requiredRule"
                  >
                    <dict-select
                      v-model="formModel.order_info.order_new_type"
                      :data-list="orderTypeListM"
                      @change="
                    (val:string) =>
                      (formModel.order_info.order_new_type_detail =
                        val === '其他线索订单' ? '客户转介绍' : '')
                  "
                    />
                  </a-form-item>
                </a-col>
                <template v-if="formModel.order_info.order_new_type">
                  <a-col :span="12">
                    <a-form-item
                      label="订单细分类型"
                      field="order_info.order_new_type_detail"
                      :rules="requiredRule"
                    >
                      <dict-select
                        v-if="
                          formModel.order_info.order_new_type === '公海线索订单'
                        "
                        v-model="formModel.order_info.order_new_type_detail"
                        :data-list="clueSeasTypeM"
                      />
                      <request-select
                        v-if="
                          formModel.order_info.order_new_type ===
                          '内容号线索订单'
                        "
                        v-model="formModel.order_info.order_new_type_detail"
                        request-url="/api/contentAccount/list"
                        label-key="account_name"
                      />

                      <request-select
                        v-else-if="
                          formModel.order_info.order_new_type === '店铺线索订单'
                        "
                        v-model="formModel.order_info.order_new_type_detail"
                        request-url="/api/store/list"
                        label-key="store_name"
                      />

                      <request-select
                        v-else-if="
                          [
                            '直播账号线索订单',
                            '全职直播间订单',
                            '内部KOC直播间订单',
                            '外部KOC直播间订单',
                          ].includes(formModel.order_info.order_new_type)
                        "
                        v-model="formModel.order_info.order_new_type_detail"
                        request-url="/api/liveRoom/list"
                        label-key="live_name"
                      />
                      <dict-select
                        v-else-if="
                          formModel.order_info.order_new_type === '其他线索订单'
                        "
                        v-model="formModel.order_info.order_new_type_detail"
                        :data-list="clueDetailTypeM"
                        :allow-clear="false"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col
                    v-if="
                      formModel.order_info.order_new_type === '其他线索订单'
                    "
                    :span="12"
                  >
                    <a-form-item
                      label="原客户昵称及电话"
                      field="order_info.order_new_type_origin_customer_phone"
                      :rules="requiredRule"
                    >
                      <request-select
                        v-model="
                          formModel.order_info
                            .order_new_type_origin_customer_phone
                        "
                        request-url="/api/thread/getCustomers"
                        label-key="customer_name"
                        value-key="phone"
                        :get-label="
                (item:any) => `${item.customer_name || '无'}(${item.phone})`
              "
                        @change="
                (val:any, item:any) =>
                  (formModel.order_info.order_new_type_origin_customer_nickname =
                    item.customer_name || '')
              "
                      />
                    </a-form-item>
                  </a-col>
                </template>
              </template>

              <a-col :span="12">
                <a-form-item
                  label="客服"
                  field="order_info.service_user_fs_id"
                  :rules="requiredRule"
                >
                  <request-select
                    v-model="formModel.order_info.service_user_fs_id"
                    request-url="/api/travel/allServiceUser"
                    label-key="user_name"
                    value-key="fs_user_id"
                    @change="
                  (val:string, item:any) => (formModel.order_info.service_user = item.user_name)
                "
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="手机号">
                  <a-input
                    v-model="formModel.order_info.phone"
                    allow-clear
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="联系人姓名"
                  field="order_info.customer_name"
                  :rules="requiredRule"
                >
                  <a-input
                    v-model="formModel.order_info.customer_name"
                    allow-clear
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="联系人电话"
                  :rules="requiredRule"
                  field="order_info.customer_phone"
                >
                  <a-input
                    v-model="formModel.order_info.customer_phone"
                    allow-clear
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <template v-if="!fromClueCreate">
                <a-col :span="24">
                  <a-form-item label="出行航班时间">
                    <a-textarea
                      v-model="formModel.order_info.fs_doc_travel_time"
                      :placeholder="`出行航班示例参考：\n接机时间：5月22日：国航 CA2725 05-22 成都-西宁，09:25  曹家堡机场T2\n送机时间：5月29日：国航 CA2726 05-29 西宁-成都，10:15  曹家堡机场T2`"
                      :auto-size="{ minRows: 3, maxRows: 10 }"
                    ></a-textarea>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item
                    label="游客信息"
                    :rules="touristInfoRules"
                    :help="
                      formModel.order_info.fs_doc_travel_people &&
                      formModel.order_info.fs_doc_travel_people.trim() &&
                      !touristInfoValidation.isValid
                        ? touristInfoValidation.message
                        : ''
                    "
                    :validate-status="
                      formModel.order_info.fs_doc_travel_people &&
                      formModel.order_info.fs_doc_travel_people.trim() &&
                      !touristInfoValidation.isValid
                        ? 'warning'
                        : ''
                    "
                    field="order_info.fs_doc_travel_people"
                  >
                    <tourist-info-editor
                      v-model="formModel.order_info.fs_doc_travel_people"
                      :placeholder="`游客信息示例参考：\n张三 440881200509050098 ，15361148809`"
                      :auto-size="{ minRows: 5 }"
                      @validation-change="handleTouristInfoValidation"
                    />
                  </a-form-item>
                </a-col>
                <!-- 编辑的时候才展示 -->
                <template v-if="isEdit">
                  <a-form-item
                    v-if="visible"
                    label="预约出行截图"
                    help="请确保图片上传完成以后再提交"
                    field="order_info.confirmation_images"
                    :rules="requiredRule"
                  >
                    <upload-image-file
                      v-model="formModel.order_info.confirmation_images"
                      file-type="image/*"
                    />
                  </a-form-item>
                  <a-col :span="24">
                    <a-col :span="24">
                      <a-form-item label="支付方式">
                        <span>
                          <span>
                            <span>{{
                              originOrderInfo.pay_type_text || '-'
                            }}</span>
                          </span>
                          <span v-if="originOrderInfo.pay_type == 1"
                            >(成交渠道：{{
                              originOrderInfo.sale_channel || '-'
                            }})</span
                          >
                        </span>
                      </a-form-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-item label="媒体订单号">
                        <miniapp-order-manager
                          v-if="formModel.order_info.pay_type === 2"
                          v-model="miniappOrders"
                          :media-order-info="
                            formModel.order_info.media_order_info[0]
                          "
                          :clue-id="formModel.order_info.thread_id"
                          @update:media-order-info="updateMediaOrderInfo"
                        />
                        <a-input
                          v-else-if="formModel.order_info.pay_type === 3"
                          v-model="formModel.order_info.media_order_no"
                          placeholder="请输入"
                        ></a-input>

                        <span v-else>{{
                          originOrderInfo.media_order_no || '-'
                        }}</span>
                      </a-form-item>
                    </a-col>
                    <!-- 编辑模式下的小程序订单订金汇总 -->
                    <a-col
                      v-if="
                        formModel.order_info.pay_type === 2 &&
                        miniappOrders.length > 0
                      "
                      :span="24"
                    >
                      <a-form-item label="订金">
                        <div class="deposit-summary">
                          <span class="deposit-amount">{{
                            formattedDepositTotal
                          }}</span>
                          <span class="deposit-count"
                            >（共{{
                              miniappOrders.filter(
                                (order) => order.details && !order.error
                              ).length
                            }}个有效订单）</span
                          >
                        </div>
                      </a-form-item>
                    </a-col>
                    <a-form-item
                      v-else
                      label="订金"
                      field="order_info.advance_price"
                      :rules="[
                        { required: true, message: '请输入', trigger: 'blur' },
                      ]"
                    >
                      <a-input-number
                        v-model="formModel.order_info.advance_price"
                        placeholder="请输入"
                        :precision="2"
                      />
                    </a-form-item>
                  </a-col>

                  <a-form-item label="是否退款再跟进">
                    <dict-select
                      v-model="formModel.order_info.is_refund_follow_up_again"
                      :data-list="yesOrNo"
                    />
                  </a-form-item>
                  <a-form-item
                    label="电话是否接通"
                    tooltip="电话接通的含义是有效接通，通话时长超过10秒及以上，打通没人说话或者打通秒挂均不算接通"
                  >
                    <dict-select
                      v-model="formModel.order_info.is_call_connected"
                      :data-list="yesOrNo"
                    />
                  </a-form-item>
                  <a-form-item
                    label="客户意向度"
                    tooltip="A：确定出行时间，确认人数。B：确定人数，不确定出行时间/不确定人数。C：不确定人数，不确定出行时间。D：无意向，且非空号。"
                  >
                    <dict-select
                      v-model="formModel.order_info.customer_intentionality"
                      :data-list="orderIntentionalityM"
                    />
                  </a-form-item>
                </template>
              </template>
            </a-row>
            <template v-if="!isEdit">
              <!-- 订金支付 -->
              <div class="form-title">订金支付</div>
              <a-form-item label="支付方式">
                <dict-radio
                  v-model="formModel.order_info.pay_type"
                  :data-list="depositPayTypeM2"
                  @change="handlePayTypeChange"
                />
              </a-form-item>
              <!--小程序支付-->
              <template v-if="formModel.order_info.pay_type === 2">
                <!-- <a-form-item
                  label="小程序商品"
                  field="order_info.miniapp_product_id"
                  :rules="requiredRule"
                >
                  <request-select
                    v-model="formModel.order_info.miniapp_product_id"
                    request-url="/api/miniApp/miniAppProduct"
                    label-key="product_name"
                    @change="
                  (val:any, item:any) =>{
                    formModel.order_info.miniapp_product_name = item?.product_name || ''
                    formModel.order_info.media_order_info[0].advance_price = parseFloat(item?.price || 0)
                    getShareInfo()
                  }
                "
                  />
                </a-form-item>
                <a-form-item label="小程序信息">
                  <a-spin :loading="shareLoading">
                    <div v-if="shareInfo.img" class="fd-cl ai-cen">
                      <a-image
                        width="160"
                        :src="'data:image/png;base64,' + shareInfo.img"
                      />
                      <a-typography-paragraph copyable class="mt-10">
                        {{ shareInfo.url_link }}
                      </a-typography-paragraph>
                    </div>
                    <a-empty v-if="!shareInfo.url_link">
                      请先选择小程序商品
                    </a-empty>
                  </a-spin>
                </a-form-item> -->

                <!-- 小程序订单号管理 -->
                <!-- <a-divider>关联媒体订单号</a-divider> -->
                <a-form-item
                  field="order_info.media_order_no"
                  :rules="[
                    {
                      required: true,
                      message: '请至少添加一个有效的小程序订单号',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <template #label>
                    <span>媒体订单号</span>
                    <a-tooltip
                      content="客人通过小程序支付后，在此添加已支付订单"
                    >
                      <icon-question-circle />
                    </a-tooltip>
                  </template>
                  <miniapp-order-manager
                    v-model="miniappOrders"
                    :media-order-info="formModel.order_info.media_order_info[0]"
                    :clue-id="formModel.inquiry_order_info.thread_id"
                    @update:media-order-info="updateMediaOrderInfo"
                  />
                  <template #help>
                    <icon-info-circle />
                    <span class="ml-5"> 请在此添加已支付的小程序订单号 </span>
                  </template>
                </a-form-item>

                <!-- 小程序订单订金汇总 -->
                <a-form-item v-if="miniappOrders.length > 0" label="订金">
                  <div class="deposit-summary">
                    <span class="deposit-amount">{{
                      formattedDepositTotal
                    }}</span>
                    <span class="deposit-count"
                      >（共{{
                        miniappOrders.filter(
                          (order) => order.details && !order.error
                        ).length
                      }}个有效订单）</span
                    >
                  </div>
                </a-form-item>
              </template>
              <!--其他支付-->
              <template v-else>
                <a-form-item
                  label="订金收款方"
                  field="order_info.payee_id"
                  :rules="requiredRule"
                >
                  <request-select
                    v-model="formModel.order_info.payee_id"
                    request-url="/api/getPayees"
                    label-key="payee_name"
                    :send-params="paymentParams"
                    @change="
                  (val:any, item:any) =>{
                    formModel.order_info.payee_name = item?.payee_name || ''
                  }"
                  />
                  <a-link
                    href="/#/manage/receive-payment"
                    target="_blank"
                    style="white-space: nowrap"
                  >
                    管理收款方
                  </a-link>
                </a-form-item>
                <a-form-item
                  v-if="visible"
                  label="收款截图凭证"
                  field="order_info.payee_images"
                  :rules="requiredUploadRuleArr"
                  help="请确保图片上传完成以后再提交"
                >
                  <upload-image-file
                    v-model="formModel.order_info.payee_images"
                    file-type="image/*"
                  />
                </a-form-item>
                <a-form-item label="媒体订单号">
                  <a-input
                    v-model="formModel.order_info.media_order_no"
                    style="width: 100%"
                    placeholder="请输入"
                    allow-clear
                  />
                </a-form-item>
                <!-- 订金 -->
                <a-form-item
                  label="订金"
                  field="order_info.advance_price"
                  :rules="[
                    { required: true, message: '请输入', trigger: 'blur' },
                  ]"
                >
                  <a-input-number
                    v-model="formModel.order_info.advance_price"
                    :precision="2"
                    placeholder="请输入"
                  />
                </a-form-item>
              </template>

              <!-- <a-divider>订单信息</a-divider>
              <a-row
                v-for="(item, index) in formModel.order_info.media_order_info"
                :key="index"
                class="single-order-item"
                :gutter="[10, 0]"
              >
                <div class="form-title">
                  <a-avatar
                    :size="24"
                    :style="{
                      backgroundColor: '#14a9f8',
                    }"
                  >
                    {{ index + 1 }}
                  </a-avatar>
                </div>
                <div
                  v-if="
                    !formModel.transfer_offline_order &&
                    !formModel.inquiry_order_info.thread_id
                  "
                  class="form-button-add"
                >
                  <a-space>
                    <a-button
                      type="outline"
                      size="mini"
                      @click="addOrderFn(index)"
                    >
                      新增
                    </a-button>
                    <a-button
                      v-if="formModel.order_info.media_order_info.length > 1"
                      type="outline"
                      size="mini"
                      @click="deleteOrderFn(index)"
                    >
                      删除
                    </a-button>
                  </a-space>
                </div>
                <a-col :span="12">
                  <a-form-item
                    label="媒体订单号"
                    :field="`order_info.media_order_info.${index}.media_order_no`"
                    :rules="{
                      //required: formModel.order_info.source !== '线下',
                      message: '请输入',
                      trigger: 'blur',
                    }"
                  >
                    <a-input
                      v-model="item.media_order_no"
                      placeholder="请输入"
                      allow-clear
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item
                    label="订金"
                    :field="`order_info.media_order_info.${index}.advance_price`"
                    :rules="[
                      { required: true, message: '请输入', trigger: 'blur' },
                      { type: 'number', min: 0.01, message: '请填写订金' },
                    ]"
                  >
                    <span
                      v-if="
                        [1, 2].includes(formModel.order_info.is_miniapp_pay)
                      "
                    >
                      {{ item.advance_price || '-' }}
                    </span>
                    <a-input-number
                      v-else
                      v-model="item.advance_price"
                      placeholder="请输入"
                      :precision="2"
                      :min="0"
                      allow-clear
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item
                    label="订单创建时间"
                    :field="`order_info.media_order_info.${index}.order_create_time`"
                    :rules="{
                      required: true,
                      message: '请选择',
                      trigger: 'blur',
                    }"
                  >
                    <a-date-picker
                      v-model="item.order_create_time"
                      placeholder="请选择"
                      show-time
                      class="w100p"
                      allow-clear
                    ></a-date-picker>
                  </a-form-item>
                </a-col>
              </a-row> -->
            </template>
          </a-form>
        </div>

        <!-- 右侧确认件信息展示区域 -->
        <div v-if="shouldShowConfirmationInfo" class="confirmation-section">
          <div class="confirmation-header">
            <h3>确认件信息</h3>
            <div class="header-actions">
              <span v-if="confirmationLoading" class="update-indicator">
                <icon-loading />
                实时更新中...
              </span>
              <a-button
                type="outline"
                size="small"
                :loading="copyLoading"
                @click="copyConfirmationInfo"
              >
                <template #icon>
                  <icon-copy />
                </template>
                全部复制
              </a-button>
            </div>
          </div>
          <div class="confirmation-content">
            <a-spin :loading="confirmationLoading" style="width: 100%">
              <div v-if="confirmationInfo" class="confirmation-text">
                {{ confirmationInfo }}
              </div>
              <div v-else class="no-confirmation"> 暂无确认件信息</div>
            </a-spin>
          </div>
        </div>
      </div>

      <template
        v-if="
          originOrderInfo.status !== '已发确认件' || userStore.hasPermission(14)
        "
        #footer
      >
        <a-spin :loading="loading">
          <a-space>
            <!-- 编辑模式的新按钮布局 -->
            <template v-if="isEdit">
              <!-- 取消 -->
              <a-button @click="handleCancel">取消</a-button>
              <a-button type="primary" @click="handleBeforeOk(true)">
                <template #icon>
                  <icon-save />
                </template>
                暂存
              </a-button>
              <!-- 提交计调审核 -->
              <a-button
                v-if="
                  originOrderInfo.status === '已受理' &&
                  userStore.hasPermission(3)
                "
                type="primary"
                status="success"
                @click="handleConfirmOrder"
              >
                <template #icon>
                  <icon-stamp />
                </template>
                提交给计调审核
              </a-button>

              <a-button
                v-if="
                  originOrderInfo.status === '待计调审核' &&
                  userStore.hasPermission(14)
                "
                type="primary"
                status="success"
                @click="handleApprovalConfirm()"
              >
                <template #icon>
                  <icon-subscribed />
                </template>
                审核通过
              </a-button>
            </template>
            <template v-else>
              <a-button @click="handleCancel">取消</a-button>
              <!-- 暂存 -->
              <a-button type="primary" @click="setOrderCache(true)">
                <template #icon>
                  <icon-save />
                </template>
                暂存
              </a-button>
              <!-- <a-button
                v-if="formModel.order_info.is_miniapp_pay === 1"
                type="primary"
                @click="validateAndSubmit()"
              >
                确定
              </a-button> -->
              <a-button
                type="primary"
                status="success"
                @click="handleBeforeOk(true)"
              >
                <!-- 确定 -->
                <template #icon>
                  <icon-check />
                </template>
                生成订单
              </a-button>
            </template>
          </a-space>
        </a-spin>
      </template>
    </d-modal>
    <travel-order-send
      ref="sendRef"
      @save="
        emit('save');
        handleCancel();
      "
    />
  </div>
</template>

<script lang="ts" setup>
  import {
    computed,
    nextTick,
    onMounted,
    onUnmounted,
    ref,
    unref,
    watch,
    watchEffect,
    h,
    toRaw,
  } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message, Modal } from '@arco-design/web-vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import {
    contactWayListM,
    orderIntentionalityM,
    orderStatusListM,
    orderTypeListM,
    payTypeM,
    ticketingPlatformM,
    yesOrNo,
  } from '@/components/dict-select/dict-travel';
  import { getColor } from '@/components/dict-select/dict-util';
  import dayjs from 'dayjs';
  import { isEmpty, isUndefined, debounce, cloneDeep } from 'lodash';
  import { useDataCacheStore, useUserStore } from '@/store';
  import {
    preciseAdd,
    preciseSubtract,
    requiredRule,
    requiredUploadRule,
    requiredUploadRuleArr,
  } from '@/utils/util';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import {
    clueDetailTypeM,
    clueSeasTypeM,
    depositPayTypeM,
    depositPayTypeM2,
  } from '@/components/dict-select/dict-clue';
  import numeral from 'numeral';
  import { OrderAndPrice } from '@/types/travel-order';
  import UploadImageFile from '@/components/upload-file/upload-image-file.vue';
  import { useClipboard } from '@vueuse/core';
  import { IconDown, IconUp, IconLoading } from '@arco-design/web-vue/es/icon';
  import TravelOrderSend from '@/views/travel-order/travel-order-list/travel-order-send.vue';
  import MiniappOrderManager from '@/components/miniapp-order-manager/MiniappOrderManager.vue';
  import TouristInfoEditor from '@/components/tourist-info-editor/tourist-info-editor.vue';
  import type { ValidationResult } from '@/utils/tourist-info-validator';

  const defaultForm = (): OrderAndPrice => ({
    // 询价单信息
    inquiry_order_info: {
      customer_name: '',
      line_id: null,
      area: '',
      product_id: null,
      travel_line: '',
      agency_id: null,
      local_travel_agency: '',
      adult_num: null,
      children_num: null,
      standard_room: null,
      total_cut_price: null,
      total_final_price: null,
      total_final_price_detail: '',
      ticket_company: '',
      real_travel_start_date: null,
      real_travel_end_date: null,
      remark: null,
      thread_id: null,
      order_no: '', // 订单编号 编辑用
    },
    transfer_offline_order: null, // 转线下的订单编号
    order_settle: false, // 订单结算
    // 订单信息
    order_info: {
      order_no: '', // 订单编号 编辑用
      order_source: '',
      order_new_type: '',
      order_new_type_detail: '',
      order_new_type_origin_customer_nickname: '',
      order_new_type_origin_customer_phone: '',
      payment_type: '线下', // 付款方式
      source: '',
      service_user: '',
      service_user_fs_id: '',
      media_order_no: '',
      media_order_info: [
        {
          media_order_no: '',
          advance_price: null,
          order_create_time: '',
        },
      ],
      estimated_service_price: null,
      air_ticket_expend: null,
      compensate_amount: null,
      compensate_explain_image_1: '',
      compensate_explain_image_2: '',
      service_other_income_total: 0,
      service_other_expend_total: 0,
      service_other_detail: [{ name: '其他', income: null, expend: null }],
      contract_other_income_total: 0,
      contract_other_expend_total: 0,
      contract_other_detail: [{ name: '其他', income: null, expend: null }],
      profit: null,
      departure_city: '',
      return_city: '',
      room_info: '',
      phone: '',
      is_miniapp_pay: 1,
      pay_type: 2, // 支付方式 1抖音来客2小程序支付3对公账户
      miniapp_product_id: '',
      miniapp_product_name: '',
      miniapp_orders: [], // 新增：小程序订单列表
      customer_name: '',
      customer_phone: '',
      fs_doc_travel_time: '',
      fs_doc_travel_people: '',
      advance_price: null,
      is_refund_follow_up_again: null,
      is_call_connected: null,
      customer_intentionality: '',
      confirmation_images: [],
      payee_name: '',
      payee_id: null,
      payee_images: [],
      thread_id: null,
    },
  });

  const emit = defineEmits(['save']);
  const userStore = useUserStore();
  const visible = ref(false);
  const loading = ref(false);
  const enmuNum = ref(0); // 更新枚举值用
  const formRef = ref();
  const formModel = ref<OrderAndPrice>(defaultForm());
  // 接口拉取的订单信息全字段
  const originOrderInfo = ref<any>({});
  // 由线索来的订单是否支付成功
  const isPay = ref(false);
  let pageConfig = ref<any>({});
  const dataStore = useDataCacheStore();

  // 确认件相关状态
  const confirmationInfo = ref('');
  const confirmationLoading = ref(false);
  const copyLoading = ref(false);
  const isDetail = ref(false); // 是否为详情模式
  const { copy } = useClipboard();

  const sendRef = ref();

  // 折叠功能相关状态
  const isAdvancedFieldsExpanded = ref(false); // 高级字段展开状态

  // 小程序订单管理相关状态
  const miniappOrders = ref<any>([]); // 小程序订单列表

  // 计算小程序订单订金总额
  const miniappOrdersDepositTotal = computed(() => {
    if (!miniappOrders.value || miniappOrders.value.length === 0) {
      return 0;
    }

    return miniappOrders.value.reduce((total: number, order: any) => {
      // 检查订单是否有效且有详情信息
      if (order.details && !order.error && order.details.total_amount) {
        const amount = parseFloat(order.details.total_amount) || 0;
        return total + amount;
      }
      return total;
    }, 0);
  });

  // 格式化订金总额显示
  const formattedDepositTotal = computed(() => {
    const total = miniappOrdersDepositTotal.value;
    if (total === 0) return '¥0.00';
    return `¥${total.toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  });

  const isEdit = computed(() => !!formModel.value.order_info.order_no);
  // 线索新建
  const fromClueCreate = computed(
    () => formModel.value.inquiry_order_info.thread_id && !isEdit.value
  );

  // 获取订单状态颜色
  const getOrderStatusColor = (status: string) => {
    return getColor(orderStatusListM, status) || '';
  };

  // 小程序二维码相关
  function initShareInfo() {
    return {
      img: '',
      url_link: '',
    };
  }

  const shareInfo = ref(initShareInfo());
  const shareLoading = ref(false);

  function getShareInfo() {
    shareLoading.value = true;
    request('/api/miniApp/miniAppCode', {
      product_id: formModel.value.order_info.miniapp_product_id,
      clue_id: formModel.value.inquiry_order_info.thread_id,
    })
      .then((res) => {
        shareInfo.value.img = res.data.qr;
        shareInfo.value.url_link = res.data.link;
      })
      .finally(() => {
        shareLoading.value = false;
      });
  }

  // 存储上一次的支付方式，用于比较
  const previousPayType = ref<number | null>(null);

  // 处理支付方式切换
  const handlePayTypeChange = (newPayType: number) => {
    const oldPayType = previousPayType.value;

    // 只在新建模式下或者真正切换支付方式时才清空相关字段
    const shouldClearFields =
      !isEdit.value || (oldPayType !== null && oldPayType !== newPayType);

    if (shouldClearFields) {
      // 清空小程序相关字段
      formModel.value.order_info.miniapp_product_id = '';
      formModel.value.order_info.miniapp_product_name = '';

      // 只在切换到非小程序支付或从小程序支付切换出来时清空 media_order_no
      if (newPayType !== 2 || (oldPayType === 2 && newPayType !== 2)) {
        formModel.value.order_info.media_order_no = '';
      }

      // 清空订金（小程序支付模式下会重新计算）
      if (newPayType !== 2) {
        formModel.value.order_info.advance_price = null;
      }
    }

    // 小程序二维码重置（总是执行）
    shareInfo.value = initShareInfo();

    // 清空小程序订单列表（在切换支付方式时总是清空）
    if (newPayType !== 2) {
      miniappOrders.value = [];
    }

    // 清空媒体订单信息数组中的相关字段（只在需要时）
    if (shouldClearFields && formModel.value.order_info.media_order_info[0]) {
      if (newPayType !== 2 || (oldPayType === 2 && newPayType !== 2)) {
        formModel.value.order_info.media_order_info[0].media_order_no = '';
      }
      if (newPayType !== 2) {
        formModel.value.order_info.media_order_info[0].advance_price = null;
      }
    }

    // 更新上一次的支付方式
    previousPayType.value = newPayType;
  };

  // 更新媒体订单信息
  const updateMediaOrderInfo = (mediaOrderInfo: any) => {
    if (formModel.value.order_info.media_order_info[0]) {
      Object.assign(
        formModel.value.order_info.media_order_info[0],
        mediaOrderInfo
      );
      // 同时更新主订单的 media_order_no 字段（仅在小程序支付模式下）
      if (formModel.value.order_info.pay_type === 2) {
        formModel.value.order_info.media_order_no =
          mediaOrderInfo.media_order_no;
      }

      // 在小程序支付模式下，同步订金总额到 advance_price
      if (formModel.value.order_info.pay_type === 2) {
        const depositTotal = miniappOrdersDepositTotal.value;
        formModel.value.order_info.advance_price =
          depositTotal > 0 ? depositTotal : null;
      }
    }
    // 校验
    formRef.value.validate('order_info.media_order_no');
  };

  // 判断是否应该显示确认件信息
  const shouldShowConfirmationInfo = computed(() => {
    const { status } = originOrderInfo.value;
    console.log('status', status);
    const validStatuses = [
      '已支付订金',
      '已受理',
      '待计调审核',
      '已发确认件',
      '已出行',
      '已核销',
    ];
    return validStatuses.includes(status) && isEdit.value;
  });

  // 判断是否应该自动展开确认件信息（在特定状态下）
  const shouldAutoExpandConfirmation = computed(() => {
    const { status } = originOrderInfo.value;
    const autoExpandStatuses = ['待计调审核', '已发确认件', '已出行', '已核销'];
    return autoExpandStatuses.includes(status) && isEdit.value;
  });

  watchEffect(() => {
    // 计算收入
    formModel.value.order_info.service_other_income_total =
      formModel.value.order_info.service_other_detail.reduce(
        (sum, item) => sum + (item.income || 0),
        0
      );
  });
  watchEffect(() => {
    // 计算支出
    formModel.value.order_info.service_other_expend_total =
      formModel.value.order_info.service_other_detail.reduce(
        (sum, item) => sum + (item.expend || 0),
        0
      );
  });
  watchEffect(() => {
    // 计算收入
    formModel.value.order_info.contract_other_income_total =
      formModel.value.order_info.contract_other_detail.reduce(
        (sum, item) => sum + (item.income || 0),
        0
      );
  });
  watchEffect(() => {
    // 计算支出
    formModel.value.order_info.contract_other_expend_total =
      formModel.value.order_info.contract_other_detail.reduce(
        (sum, item) => sum + (item.expend || 0),
        0
      );
  });

  function lineChange(val: number, item: any) {
    formModel.value.inquiry_order_info.area = item?.line_name || '';
    formModel.value.inquiry_order_info.product_id = null;
    formModel.value.inquiry_order_info.travel_line = '';
    formModel.value.inquiry_order_info.agency_id = null;
    formModel.value.inquiry_order_info.local_travel_agency = '';
  }

  function agencyChange(val: number, item: any) {
    formModel.value.inquiry_order_info.local_travel_agency =
      item?.agency_name || '';
    formModel.value.inquiry_order_info.product_id = null;
    formModel.value.inquiry_order_info.travel_line = '';
  }

  function productChange(val: number, item: any) {
    formModel.value.inquiry_order_info.travel_line = item.product_name;
    formModel.value.inquiry_order_info.agency_id = item.agency_id;
    const agencyItem = dataStore.getLineItem(
      'agency_id',
      item.agency_id,
      'agency_name'
    );
    formModel.value.inquiry_order_info.local_travel_agency =
      agencyItem?.agency_name;
  }

  const validate = () => {
    return formRef.value?.validate();
  };
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  // 空数组数据格式化
  function formatArrayInfo() {
    const initForm = defaultForm();
    formModel.value.order_info.service_other_detail = isEmpty(
      formModel.value.order_info.service_other_detail
    )
      ? initForm.order_info.service_other_detail
      : formModel.value.order_info.service_other_detail;
    formModel.value.order_info.contract_other_detail = isEmpty(
      formModel.value.order_info.contract_other_detail
    )
      ? initForm.order_info.contract_other_detail
      : formModel.value.order_info.contract_other_detail;
  }

  function getOrderCache() {
    loading.value = true;
    request('/api/travel/getOrderTmp', {
      clue_id: formModel.value.inquiry_order_info.thread_id,
    })
      .then((res) => {
        if (res.data.order_info) {
          const data = res.data.order_info;
          Object.assign(
            formModel.value.inquiry_order_info,
            data.inquiry_order_info
          );
          Object.assign(formModel.value.order_info, data.order_info);
          // miniapp_orders
          miniappOrders.value = data.order_info.miniapp_orders || [];
          formatArrayInfo();
          if (formModel.value.order_info.miniapp_product_id) {
            getShareInfo();
          }
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }

  // 利润 = 总团款 - 结算价 - 预估服务费 + 客服其他收入 - 客服其他支出 + 合同其他收入 - 合同其他支出 -  机票支出 - 赔付金额
  watchEffect(() => {
    // 用lodash数学计算函数，防止精度丢失
    // 总团款 - 结算价
    const num1 = preciseSubtract(
      formModel.value.inquiry_order_info.total_cut_price || 0,
      formModel.value.inquiry_order_info.total_final_price || 0
    );
    // - 预估服务费
    const num2 = preciseSubtract(
      num1,
      formModel.value.order_info.estimated_service_price || 0
    );
    // + 客服其他收入
    const num3 = preciseAdd(
      num2,
      formModel.value.order_info.service_other_income_total || 0
    );
    // - 客服其他支出
    const num4 = preciseSubtract(
      num3,
      formModel.value.order_info.service_other_expend_total || 0
    );
    // + 合同其他收入
    const num5 = preciseAdd(
      num4,
      formModel.value.order_info.contract_other_income_total || 0
    );
    // - 合同其他支出
    const num6 = preciseSubtract(
      num5,
      formModel.value.order_info.contract_other_expend_total || 0
    );
    // - 机票支出
    const num7 = preciseSubtract(
      num6,
      formModel.value.order_info.air_ticket_expend || 0
    );
    // - 赔付金额
    const num8 = preciseSubtract(
      num7,
      formModel.value.order_info.compensate_amount || 0
    );
    formModel.value.order_info.profit = num8 || 0;
  });
  // 人均利润=利润/(成人+儿童)
  const profitAverage = computed(() =>
    !formModel.value.inquiry_order_info.adult_num
      ? '-'
      : numeral(
          (formModel.value.order_info.profit || 0) /
            ((formModel.value.inquiry_order_info.adult_num || 0) +
              (formModel.value.inquiry_order_info.children_num || 0))
        ).format('0,0.00')
  );
  // 利润率=利润/总团款
  const profitRate = computed(() =>
    numeral(
      (formModel.value.order_info.profit || 0) /
        (formModel.value.inquiry_order_info.total_cut_price || 1)
    ).format('0,0.00%')
  );

  // 确认件相关方法
  const getConfirmationInfo = async (isRealTimeUpdate = false) => {
    if (!shouldShowConfirmationInfo.value) return;

    // 实时更新时只在确认件信息区域展开时才更新
    if (isRealTimeUpdate && !shouldShowConfirmationInfo.value) return;

    confirmationLoading.value = true;
    try {
      // 组装确认件信息
      const orderInfo = formModel.value;
      const inquiry = orderInfo.inquiry_order_info;
      const order = orderInfo.order_info;

      // 获取线路名称
      const lineInfo = inquiry.line_id
        ? dataStore.getLineItem('line_id', inquiry.line_id, 'line_name')
        : null;
      const lineName = lineInfo?.line_name || '';

      // 根据实际数据组装确认件文本
      const confirmationText = `${inquiry?.real_travel_start_date || ''} ${
        inquiry?.travel_line || lineName
      }

出行人信息：
${order.fs_doc_travel_people || ''}
联系方式：${order.customer_phone || order.phone || ''}
房间信息：${order.room_info || ''}
航班信息：
${order.fs_doc_travel_time || ''}
订单明细说明：
${inquiry.remark || ''}`;

      confirmationInfo.value = confirmationText;
    } catch (error) {
      console.error('获取确认件信息失败:', error);
      confirmationInfo.value = '获取确认件信息失败';
    } finally {
      confirmationLoading.value = false;
    }
  };

  // 创建防抖的实时更新函数
  const debouncedUpdateConfirmation = debounce(() => {
    if (shouldShowConfirmationInfo.value && visible.value) {
      getConfirmationInfo(true);
    }
  }, 500); // 500ms 防抖延迟

  // 不要继续追加参数个数了 把参数都加到config里面做判断用
  const show = async (dinfo?: any, config?: any) => {
    // transfer_offline 是否是转线下
    // order_settle 是否是订单结算
    const { transfer_offline, order_settle } = config || {};
    pageConfig.value = config;
    originOrderInfo.value = {};
    // 重置小程序订单相关状态
    miniappOrders.value = [];
    // 重置其他状态
    confirmationInfo.value = '';
    isAdvancedFieldsExpanded.value = false;
    let initForm = defaultForm();
    formModel.value = initForm;
    loading.value = true;
    isPay.value = false;
    const order_no = dinfo?.order_no || '';
    if (order_no) {
      const allInfo = (
        await request('/api/travel/orderInfoV2', {
          order_no: dinfo.order_no,
        })
      ).data;
      let { inquiry_order_info, order_info } = allInfo;
      originOrderInfo.value = order_info;
      inquiry_order_info = inquiry_order_info || {};
      order_info = order_info || {};
      Object.keys(initForm.inquiry_order_info).forEach((key) => {
        if (
          inquiry_order_info[key] &&
          [
            'adult_num',
            'children_num',
            'standard_room',
            'total_cut_price',
            'total_final_price',
            'other_cost_amount',
          ].includes(key)
        ) {
          // @ts-ignore
          formModel.value.inquiry_order_info[key] = parseFloat(
            inquiry_order_info[key]
          );
        } else {
          // @ts-ignore
          formModel.value.inquiry_order_info[key] = isUndefined(
            inquiry_order_info[key]
          )
            ? // @ts-ignore
              initForm.inquiry_order_info[key]
            : inquiry_order_info[key];
        }
      });

      Object.keys(initForm.order_info).forEach((key) => {
        if (
          order_info[key] &&
          [
            'estimated_service_price',
            'air_ticket_expend',
            'compensate_amount',
            'service_other_income_total',
            'service_other_expend_total',
            'contract_other_income_total',
            'contract_other_expend_total',
            'profit',
            'advance_price',
          ].includes(key)
        ) {
          // @ts-ignore
          formModel.value.order_info[key] = parseFloat(order_info[key]);
        } else {
          // @ts-ignore
          formModel.value.order_info[key] = isUndefined(order_info[key])
            ? // @ts-ignore
              initForm.order_info[key]
            : order_info[key];
        }
      });
      let order_new_type_detail = parseInt(
        formModel.value.order_info.order_new_type_detail,
        10
      );
      if (
        // @ts-ignore
        // eslint-disable-next-line eqeqeq
        order_new_type_detail ==
        formModel.value.order_info.order_new_type_detail
      ) {
        // @ts-ignore
        formModel.value.order_info.order_new_type_detail =
          order_new_type_detail;
      }

      // 转线下
      if (transfer_offline) {
        formModel.value.transfer_offline_order = order_no;
      }
      // 订单结算
      if (order_settle) {
        formModel.value.order_settle = order_settle;
      }
      formModel.value.order_info.media_order_info.forEach((item: any) => {
        item.advance_price = parseFloat(item.advance_price);
      });

      // 如果是编辑模式且有小程序订单号，初始化订单列表
      if (
        formModel.value.order_info.media_order_no &&
        formModel.value.order_info.pay_type === 2
      ) {
        miniappOrders.value =
          order_info.order_items ||
          formModel.value.order_info.miniapp_orders ||
          [];
      }
    }

    // 新建
    if (dinfo && !dinfo.order_no) {
      Object.assign(
        formModel.value.inquiry_order_info,
        dinfo.inquiry_order_info
      );
      Object.assign(formModel.value.order_info, dinfo.order_info);
      formModel.value.order_info.media_order_info[0].advance_price = 0;
      formModel.value.order_info.media_order_info[0].order_create_time =
        dayjs().format('YYYY-MM-DD HH:mm:ss');
    }

    formatArrayInfo();

    // 初始化支付方式记录，用于后续比较
    previousPayType.value = formModel.value.order_info.pay_type;

    nextTick(() => {
      loading.value = false;
      visible.value = true;
      if (formModel.value.inquiry_order_info.thread_id && !order_no) {
        getOrderCache();
      }
      // 如果是符合条件的订单状态，自动获取确认件信息
      if (shouldAutoExpandConfirmation.value) {
        nextTick(() => {
          getConfirmationInfo();
        });
      }
    });
  };

  // 新增订单
  const addOrderFn = (index: number) => {
    formModel.value.order_info.media_order_info.splice(index + 1, 0, {
      media_order_no: '',
      advance_price: null,
      order_create_time: '',
    });
  };

  // 删除订单
  const deleteOrderFn = (index: number) => {
    formModel.value.order_info.media_order_info.splice(index, 1);
  };

  const handleCancel = () => {
    visible.value = false;
    clearValidate();
  };

  const validateData = async () => {
    // 检查高级字段的验证错误，如果有错误则自动展开
    let hasAdvancedFieldError = false;

    if (
      formModel.value.order_info.compensate_amount &&
      (!formModel.value.order_info.compensate_explain_image_1 ||
        !formModel.value.order_info.compensate_explain_image_2)
    ) {
      Message.error('请上传赔付证明');
      hasAdvancedFieldError = true;
    }
    if (
      formModel.value.order_info.service_other_detail.some(
        function checkServiceOtherName(item) {
          return !item.name;
        }
      )
    ) {
      Message.error('请选择客服其他收入和支出的内容');
      hasAdvancedFieldError = true;
    }
    if (
      formModel.value.order_info.contract_other_detail.some(
        function checkContractOtherName(item) {
          return !item.name;
        }
      )
    ) {
      Message.error('请选择合同其他收入和支出的内容');
      hasAdvancedFieldError = true;
    }

    // 如果有高级字段验证错误，自动展开
    if (hasAdvancedFieldError) {
      isAdvancedFieldsExpanded.value = true;
      return false;
    }
    if (
      formModel.value.inquiry_order_info.real_travel_end_date &&
      formModel.value.inquiry_order_info.real_travel_start_date
    ) {
      if (
        dayjs(
          formModel.value.inquiry_order_info.real_travel_start_date
        ).isAfter(
          dayjs(formModel.value.inquiry_order_info.real_travel_end_date)
        )
      ) {
        Message.error('出行日期不能晚于返程日期');
        return false;
      }
    }
    if (
      !fromClueCreate.value &&
      !formModel.value.inquiry_order_info.total_cut_price
    ) {
      Message.error('请填写总团款');
      return false;
    }
    if (
      !fromClueCreate.value &&
      !formModel.value.inquiry_order_info.adult_num
    ) {
      Message.error('请填写成人人数');
      return false;
    }

    // 游客信息必填校验（仅在编辑模式下的提交计调操作时触发）
    if (
      !fromClueCreate.value &&
      (!formModel.value.order_info.fs_doc_travel_people ||
        !formModel.value.order_info.fs_doc_travel_people.trim())
    ) {
      Message.error('请填写游客信息');
      return false;
    }

    // 验证小程序订单 新建模式才校验
    if (!isEdit.value && formModel.value.order_info.pay_type === 2) {
      const hasValidOrders = miniappOrders.value.some(
        (order) => order.details && !order.error
      );
      if (!hasValidOrders) {
        formRef.value.validate('order_info.media_order_no');
        Message.error('请至少添加一个有效的小程序订单号');
        return false;
      }
    }

    const error = await validate();
    return !error;
  };

  // 订单受理
  function acceptOrderAction(record: any) {
    loading.value = true;
    request('/api/travel/acceptOrder', {
      order_no: record.order_no,
    })
      .then((res) => {
        Message.success('订单已受理,请继续补充信息');
        emit('save');
        // 打开编辑页面
        show({ order_no: record.order_no });
      })
      .catch((err) => {
        loading.value = false;
      });
  }

  function cleanUndefined(obj) {
    if (obj === null || typeof obj !== 'object') {
      return obj === undefined ? null : obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(cleanUndefined);
    }

    return Object.fromEntries(
      Object.entries(obj).map(([key, value]) => [key, cleanUndefined(value)])
    );
  }

  const handleBeforeOk = async (closeModalFlag = true) => {
    loading.value = true;
    let APIUrl = '/api/travel/addOrderAndInquiryOrder';
    if (isEdit.value) {
      APIUrl = '/api/travel/inquiryOrderSave';
    }
    if (formModel.value.transfer_offline_order) {
      APIUrl = '/api/travel/transferOfflineOrder';
    }
    if (APIUrl === '/api/travel/addOrderAndInquiryOrder') {
      const result = await validateData();
      if (!result) {
        loading.value = false;
        return '';
      }
    }
    if (formModel.value.order_settle) {
      // @ts-ignore
      formModel.value.order_info.status = '已完成';
    }
    // const params = cloneDeep(formModel.value);
    // 转换undefined为null
    // 先转换为普通对象
    const rawData = toRaw(formModel.value);
    const params = cleanUndefined(rawData);

    // 提交的时候需要把 is_miniapp_pay 2 改成 1
    if (params.order_info.pay_type === 2) {
      params.order_info.is_miniapp_pay = 1;
    } else {
      params.order_info.is_miniapp_pay = -1;
    }

    // 处理小程序订单号：将多个订单号用逗号拼接存储到 media_order_no 字段
    // 如果是小程序支付 判断媒体订单号
    if (params.order_info.pay_type === 2) {
      if (miniappOrders.value.length > 0) {
        const validOrderNos = miniappOrders.value
          .filter((order) => order.details && !order.error)
          .map((order) => order.order_no);

        if (validOrderNos.length > 0) {
          params.order_info.media_order_no = validOrderNos.join(',');
          // 同时更新第一个订单的信息
          if (params.order_info.media_order_info[0]) {
            params.order_info.media_order_info[0].media_order_no =
              validOrderNos.join(',');
          }
        }
      } else {
        params.order_info.media_order_no = '';
      }
    }
    request(APIUrl, params)
      .then((resData) => {
        if (!isEdit.value && formModel.value.inquiry_order_info.thread_id) {
          // Modal.success({
          //   title: '保存成功',
          //   content: `询价单号：${resData.data.inquiry_order_no}，订单编号：${resData.data.order_no}。`,
          // });
          let record = {
            order_no: resData.data.order_no,
            customer_name: formModel.value.inquiry_order_info.customer_name,
            status: '已支付订金（1级）',
            travel_line: formModel.value.inquiry_order_info.travel_line,
          };
          Modal.confirm({
            title: '生成订单成功！是否继续受理此订单？',
            content: () =>
              h(
                'div',
                {
                  style: {
                    textAlign: 'left',
                    lineHeight: '1.6',
                  },
                },
                [
                  h('div', { style: { marginBottom: '5px' } }, [
                    h('strong', '订单编号：'),
                    record.order_no,
                  ]),
                  h('div', { style: { marginBottom: '5px' } }, [
                    h('strong', '客户昵称：'),
                    record.customer_name || '-',
                  ]),
                  h('div', { style: { marginBottom: '5px' } }, [
                    h('strong', '订单状态：'),
                    record.status || '-',
                  ]),
                  // h('div', { style: { marginBottom: '0px' } }, [
                  //   h('strong', '产品名称：'),
                  //   record.travel_line || '-',
                  // ]),
                ]
              ),
            okText: '确定受理',
            cancelText: '取消',
            width: 380,
            onOk: () => {
              emit('save');
              acceptOrderAction(record);
            },
          });
        } else {
          Message.success('保存成功');
        }
        loading.value = false;
        emit('save');
        if (closeModalFlag) {
          handleCancel();
        }
        // 保存成功后刷新确认件信息
        if (shouldShowConfirmationInfo.value) {
          getConfirmationInfo();
        }
      })
      .catch(() => {
        loading.value = false;
      });
  };

  // 复制确认件信息
  const copyConfirmationInfo = async () => {
    if (!confirmationInfo.value) return;

    copyLoading.value = true;
    try {
      await copy(confirmationInfo.value);
      Message.success('确认件信息已复制到剪贴板');
    } catch (error) {
      Message.error('复制失败，请手动复制');
    } finally {
      copyLoading.value = false;
    }
  };

  // 审核通过确认
  const handleApprovalConfirm = async () => {
    const result = await validateData();
    if (result) {
      sendRef.value?.show({
        ...formModel.value.order_info,
        real_travel_date: [
          formModel.value.inquiry_order_info.real_travel_start_date,
          formModel.value.inquiry_order_info.real_travel_end_date,
        ],
        real_travel_end_date:
          formModel.value.inquiry_order_info.real_travel_end_date,
        real_travel_start_date:
          formModel.value.inquiry_order_info.real_travel_start_date,
      });
    }
    // Modal.confirm({
    //   title: '审核确认',
    //   content: '审核通过后，订单状态将向后流转，是否确认审核通过？',
    //   onOk: async () => {
    //     try {
    //       sendRef.value?.show(formModel.value.order_info);
    // loading.value = true;
    // await request('/api/travel/addOrderAndInquiryOrder', {
    //   order_no: formModel.value.order_info.order_no,
    // });
    // Message.success('审核通过成功');
    // emit('save');
    // handleCancel();
    //     } catch (error) {
    //       Message.error('审核失败');
    //     } finally {
    //       loading.value = false;
    //     }
    //   },
    // });
  };

  let cancelTokenCache: AbortController;

  // 订单信息缓存
  function setOrderCache(closeFlag = false) {
    if (
      formModel.value.inquiry_order_info.thread_id &&
      !isPay.value &&
      visible.value &&
      !loading.value
    ) {
      cancelTokenCache?.abort('重复请求取消');
      cancelTokenCache = new AbortController();
      // 处理小程序订单号：将多个订单号用逗号拼接存储到 media_order_no 字段
      if (formModel.value.order_info.pay_type === 2) {
        if (miniappOrders.value.length > 0) {
          const validOrderNos = miniappOrders.value
            .filter((order) => order.details && !order.error)
            .map((order) => order.order_no);

          if (validOrderNos.length > 0) {
            formModel.value.order_info.media_order_no = validOrderNos.join(',');
            // 同时更新第一个订单的信息
            if (formModel.value.order_info.media_order_info[0]) {
              formModel.value.order_info.media_order_info[0].media_order_no =
                validOrderNos.join(',');
            }
          }
        } else {
          formModel.value.order_info.media_order_no = '';
        }
      }

      request(
        '/api/travel/orderTmpSave',
        {
          clue_id: formModel.value.inquiry_order_info.thread_id,
          order_info: formModel.value,
        },
        cancelTokenCache.signal
      )
        .then(() => {
          if (closeFlag) {
            Message.success('暂存成功');
            handleCancel();
          }
        })
        .catch(() => {});
    }
  }

  watch(
    () => formModel.value,
    () => {
      setOrderCache(false);
    },
    { deep: true }
  );

  async function validateAndSubmit() {
    const validateRes = await validateData();
    if (!validateRes) {
      return;
    }
    loading.value = true;
    request('/api/travel/orderPayStatus', {
      clue_id: formModel.value.inquiry_order_info.thread_id,
    })
      .then((res) => {
        isPay.value = true;
        formModel.value.order_info.media_order_info[0].order_create_time =
          res.data?.pay_time;
        formModel.value.order_info.media_order_info[0].media_order_no =
          res.data?.order_sn;
        handleBeforeOk(true);
      })
      .catch(() => {
        loading.value = false;
      });
  }

  // 验证订单是否支付成功（保留原有逻辑用于兼容）
  // async function validateIsPay() {
  //   const validateRes = await validateData();
  //   if (!validateRes) {
  //     return;
  //   }
  //   if (formModel.value.order_info.is_miniapp_pay === 2) {
  //     loading.value = true;
  //     request('/api/travel/miniOrderThreadInfo', {
  //       clue_id: formModel.value.inquiry_order_info.thread_id,
  //       miniapp_no: formModel.value.order_info.miniapp_no,
  //     })
  //       .then((res) => {
  //         if (!res.data.is_pay) {
  //           Message.error('该小程序订单未查询到支付信息');
  //         } else if (res.data.is_already_bind_order) {
  //           Message.error('该小程序订单已被其他线索绑定');
  //         } else {
  //           isPay.value = true;
  //           formModel.value.order_info.media_order_info[0].advance_price =
  //             parseFloat(res.data?.total_amount || 0);
  //           formModel.value.order_info.media_order_info[0].order_create_time =
  //             res.data?.pay_time;
  //           formModel.value.order_info.media_order_info[0].media_order_no =
  //             formModel.value.order_info.miniapp_no;
  //         }
  //       })
  //       .finally(() => {
  //         loading.value = false;
  //       });
  //   } else {
  //     isPay.value = true;
  //   }
  // }

  const paymentParams = ref({ num: 0 });

  function handleTabVisibilityChange() {
    if (document.visibilityState === 'visible') {
      // 执行当Tab回到前台时的逻辑
      if (visible.value) {
        paymentParams.value.num += 1;
      }
    } else if (document.visibilityState === 'hidden') {
      // console.log('Tab被切换到了后台');
      // 执行当Tab隐藏时的逻辑
    }
  }

  // 订单已完成
  function ensureOrderAction(record: any) {
    loading.value = true;
    request('/api/travel/confirmOrder', {
      order_no: record.order_no,
    })
      .then((res) => {
        Message.success('操作成功');
        emit('save');
        handleCancel();
        // refreshData();
      })
      .catch((err) => {
        loading.value = false;
        // 如果报错，则打开编辑页面
      });
  }
  // 新增：处理确认订单（使用 Modal.confirm）
  async function handleConfirmOrder() {
    console.log(originOrderInfo.value);
    // 校验表单
    const result = await validateData();
    if (!result) {
      console.log('校验失败');
      return '';
    }

    await handleBeforeOk(false);

    let record = {
      order_no: formModel.value.order_info.order_no,
      customer_name: formModel.value.inquiry_order_info.customer_name,
      status: originOrderInfo.value.status,
      travel_line: formModel.value.inquiry_order_info.travel_line,
    };

    Modal.confirm({
      title: '确认将此订单提交给计调审核吗？',
      content: () =>
        h(
          'div',
          {
            style: {
              textAlign: 'left',
              lineHeight: '1.6',
            },
          },
          [
            h('div', { style: { marginBottom: '5px' } }, [
              h('strong', '订单编号：'),
              record.order_no,
            ]),
            h('div', { style: { marginBottom: '5px' } }, [
              h('strong', '客户昵称：'),
              record.customer_name || '-',
            ]),
            h('div', { style: { marginBottom: '5px' } }, [
              h('strong', '订单状态：'),
              record.status || '-',
            ]),
            h('div', { style: { marginBottom: '0px' } }, [
              h('strong', '产品名称：'),
              record.travel_line || '-',
            ]),
          ]
        ),
      okText: '确认提交',
      cancelText: '取消',
      width: 380,
      onOk: () => {
        ensureOrderAction(record);
      },
    });
  }

  const touristInfoValidation = ref<ValidationResult>({
    isValid: true,
    invalidPhones: [],
    invalidIdCards: [],
    message: '',
  });

  // 处理游客信息验证结果
  const handleTouristInfoValidation = (result: ValidationResult) => {
    console.log('🔄 [表单验证] 收到组件验证结果:', result);
    touristInfoValidation.value = result;
  };

  // 游客信息自定义验证规则（非阻塞性验证）
  const touristInfoRules = [
    {
      required: true,
      message: '请填写游客信息',
      trigger: 'blur',
    },
    {
      validator: (value: string, callback: (error?: string) => void) => {
        console.log('🔍 [表单自定义验证器] 开始验证:', value);
        console.log(
          '🔍 [表单自定义验证器] 当前组件验证状态:',
          touristInfoValidation.value
        );

        if (!value || !value.trim()) {
          console.log('✅ [表单自定义验证器] 空值，由 required 规则处理');
          // 空值由 required 规则处理
          callback();
          return;
        }

        // 对于格式错误，不阻止表单提交，只显示警告信息
        // 通过 :help 属性显示错误消息，而不是通过 callback(error) 阻止提交
        if (!touristInfoValidation.value.isValid) {
          console.log(
            '⚠️ [表单自定义验证器] 格式有误但允许提交:',
            touristInfoValidation.value.message
          );
          // 不调用 callback(error)，而是调用 callback() 允许提交
          callback();
          return;
        }

        console.log('✅ [表单自定义验证器] 验证通过');
        callback();
      },
      trigger: 'blur',
    },
  ];

  onMounted(() => {
    document.addEventListener('visibilitychange', handleTabVisibilityChange);
  });

  onUnmounted(() => {
    document.removeEventListener('visibilitychange', handleTabVisibilityChange);
  });

  // 监听确认件信息显示状态变化
  watch(shouldShowConfirmationInfo, (newVal) => {
    if (newVal && visible.value) {
      nextTick(() => {
        getConfirmationInfo();
      });
    }
  });

  // 监听关键字段变化，实时更新确认件信息
  watch(
    [
      // 监听询价单信息的关键字段
      () => formModel.value.inquiry_order_info.customer_name,
      () => formModel.value.inquiry_order_info.real_travel_start_date,
      () => formModel.value.inquiry_order_info.real_travel_end_date,
      () => formModel.value.inquiry_order_info.line_id,
      () => formModel.value.inquiry_order_info.travel_line,
      () => formModel.value.inquiry_order_info.adult_num,
      () => formModel.value.inquiry_order_info.children_num,
      () => formModel.value.inquiry_order_info.standard_room,
      () => formModel.value.inquiry_order_info.total_cut_price,
      () => formModel.value.inquiry_order_info.total_final_price,
      () => formModel.value.inquiry_order_info.remark,
      // 监听订单信息的关键字段
      () => formModel.value.order_info.customer_phone,
      () => formModel.value.order_info.phone,
      () => formModel.value.order_info.departure_city,
      () => formModel.value.order_info.return_city,
      () => formModel.value.order_info.room_info,
      () => formModel.value.order_info.fs_doc_travel_time,
      () => formModel.value.order_info.fs_doc_travel_people,
    ],
    () => {
      // 只在确认件信息区域展开且弹窗可见时才触发实时更新
      if (shouldShowConfirmationInfo.value && visible.value) {
        debouncedUpdateConfirmation();
      }
    },
    { deep: false } // 不需要深度监听，只监听字段值的变化
  );

  // 监听小程序订单列表变化，更新表单验证状态和订金总额
  watch(
    miniappOrders,
    (newOrders) => {
      // 更新表单验证字段
      const hasValidOrders = newOrders.some(
        (order) => order.details && !order.error
      );
      formModel.value.order_info.miniapp_orders = hasValidOrders
        ? newOrders
        : [];

      // 自动同步订金总额到 advance_price 字段（仅在小程序支付模式下）
      if (formModel.value.order_info.pay_type === 2) {
        const depositTotal = miniappOrdersDepositTotal.value;
        // 处理边界情况：订单数组为空或无有效订单时设置为 null
        formModel.value.order_info.advance_price =
          depositTotal > 0 ? depositTotal : null;
      }
    },
    { deep: true }
  );

  // 监听支付方式变化，处理订金字段的同步
  watch(
    () => formModel.value.order_info.pay_type,
    (newPayType, oldPayType) => {
      // 当切换到小程序支付模式时，自动同步订金总额
      if (newPayType === 2 && miniappOrders.value.length > 0) {
        const depositTotal = miniappOrdersDepositTotal.value;
        formModel.value.order_info.advance_price =
          depositTotal > 0 ? depositTotal : null;
      }

      // 当从小程序支付模式切换到其他支付方式时，确保 media_order_no 被清空
      if (oldPayType === 2 && newPayType !== 2 && !isEdit.value) {
        formModel.value.order_info.media_order_no = '';
      }
    }
  );

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped>
  .arco-modal-body {
    padding: 0 20px;
  }

  .arco-divider-horizontal.arco-divider-with-text {
    margin: 10px 0 20px;
  }

  .form-title {
    font-size: 16px;
    font-weight: bold;
  }

  .single-order-item {
    position: relative;
    padding: 40px 10px 10px;
    border: 1px solid var(--color-border-1);
    margin-bottom: 2%;
    border-radius: 4px;

    .form-title {
      position: absolute;
      top: 10px;
    }

    .form-button-add {
      position: absolute;
      right: 16px;
      top: 10px;
    }
  }

  // 确认件信息展示相关样式
  .modal-content-wrapper {
    display: flex;
    gap: 20px;

    &.with-confirmation {
      .edit-section {
        flex: 1;
        min-width: 0;
        max-height: 75vh;
        overflow-x: hidden;
        overflow-y: scroll;
      }

      .confirmation-section {
        width: 400px;
        flex-shrink: 0;
        border-left: 1px solid var(--color-border-2);
        padding-left: 20px;
      }
    }

    &:not(.with-confirmation) {
      .edit-section {
        width: 100%;
      }
    }
  }

  .confirmation-section {
    .confirmation-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--color-text-1);
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 12px;

        .update-indicator {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 12px;
          color: var(--color-text-3);

          .arco-icon {
            animation: spin 1s linear infinite;
          }
        }
      }
    }

    .confirmation-content {
      .confirmation-text {
        background: var(--color-fill-1);
        border: 1px solid var(--color-border-2);
        border-radius: 6px;
        padding: 16px;
        font-size: 13px;
        line-height: 1.6;
        color: var(--color-text-2);
        white-space: pre-wrap;
        word-break: break-all;
        max-height: 500px;
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: var(--color-fill-2);
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: var(--color-border-3);
          border-radius: 3px;

          &:hover {
            background: var(--color-border-4);
          }
        }
      }

      .no-confirmation {
        text-align: center;
        color: var(--color-text-3);
        padding: 40px 20px;
        font-size: 14px;
      }
    }
  }

  // 订金汇总样式
  .deposit-summary {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 6px 12px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #bae6fd;
    border-radius: 8px;

    .deposit-amount {
      font-size: 14px;
      font-weight: 600;
      color: #0369a1;
      letter-spacing: 0.5px;
    }

    .deposit-count {
      font-size: 13px;
      color: #64748b;
      font-weight: 400;
    }
  }

  // 折叠按钮样式
  .advanced-fields-toggle {
    text-align: center;
    margin: 16px 0;

    .arco-btn {
      color: var(--color-text-2);
      font-size: 14px;

      &:hover {
        color: var(--color-primary-6);
      }

      .arco-icon {
        margin-right: 4px;
        transition: transform 0.3s ease;
      }
    }
  }

  // 旋转动画
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  // Modal 标题样式
  .modal-title-container {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;

    .modal-title-text {
      font-size: 16px;
      font-weight: 500;
      color: var(--color-text-1);
    }
  }

  // 响应式处理
  @media (max-width: 768px) {
    .modal-title-container {
      gap: 6px;

      .modal-title-text {
        font-size: 14px;
      }
    }
  }
</style>
