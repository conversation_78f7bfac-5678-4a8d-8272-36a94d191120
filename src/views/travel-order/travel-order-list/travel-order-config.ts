import { TableColumnSlot } from '@/global';
import { moneyFormat } from '@/utils/table-utils/table-util';
import {
  getDictTxtRender,
  moneyFormatShow,
} from '@/utils/table-utils/columns-config';
import {
  contactWayListM,
  orderIntentionalityM,
  orderStatusListM,
  payTypeM,
  yesOrNo,
  yesOrNo2,
  yesOrNo3,
  yesOrNoOrNull,
} from '@/components/dict-select/dict-travel';
import {
  clueSuorceM,
  depositPayTypeM2,
  isFlowM,
} from '@/components/dict-select/dict-clue';
import { computed, markRaw } from 'vue';
import { Descriptions, SelectOptionData } from '@arco-design/web-vue';
import requestSelect from '@/components/select/request-select.vue';
import requestTreeSelect from '@/components/select/request-tree-select.vue';
import dictSelect from '@/components/dict-select/dict-select.vue';
import OrderTypeSelect from '@/views/travel-order/travel-order-list/order-type-select.vue';
import ProductSelectWithAgency from '@/components/select/product-select-with-agency.vue';
import { useDataCacheStore, useUserStore } from '@/store';

const userStore = useUserStore();

export const allFieldsConfig = [
  {
    title: '基础信息',
    dataIndex: '基础信息',
    keys: [] as string[],
    dataList: [
      {
        title: '是否添加微信',
        dataIndex: 'is_add_wechat',
        width: 120,
      },
      {
        title: '电话是否接通',
        dataIndex: 'is_call_connected',
        defaultHide: true,
        width: 140,
        description:
          '电话接通的含义是有效接通，通话时长超过10秒及以上，打通没人说话或者打通秒挂均不算接通',
      },
      {
        title: '订单明细说明',
        dataIndex: 'remark',
        slotName: 'multiLineText',
      },
      // 备注
      {
        title: '跟进情况',
        dataIndex: 'new_remark',
        slotName: 'new_remark',
      },
      {
        title: '旅游线路',
        dataIndex: 'area',
        width: 120,
      },
      {
        title: '地接社',
        dataIndex: 'local_travel_agency',
      },
      {
        title: '产品',
        dataIndex: 'travel_line',
        width: 200,
        slotName: 'multiLineText',
      },
      // {
      //  title: '床位数',
      //  dataIndex: 'standard_room',
      // },
      // {
      //  title: '车型',
      //  dataIndex: 'car_type',
      //  width: 120,
      // },
      {
        title: '商品ID',
        dataIndex: 'goods_id',
        width: 174,
      },
      {
        title: '来源媒体',
        dataIndex: 'source',
        width: 120,
      },
      {
        title: '媒体订单号',
        dataIndex: 'media_order_no',
        width: 195,
      },
      {
        title: '成交渠道',
        dataIndex: 'sale_channel',
        description: '取自媒体订单的成交渠道',
        width: 200,
      },
      {
        title: '支付方式',
        dataIndex: 'pay_type_text',
      },
      {
        title: '客服',
        dataIndex: 'service_user',
        width: 80,
      },
      {
        title: '询价客服',
        dataIndex: 'inquiry_service_user',
        width: 100,
      },
      {
        title: '接粉专员',
        dataIndex: 'service_user_jiefen',
        width: 100,
      },
      {
        title: '媒体账号',
        dataIndex: 'media_account',
      },
      {
        title: '主播',
        dataIndex: 'anchor_name',
        width: 80,
      },
      // 确认件审核人
      {
        title: '确认件审核人',
        dataIndex: 'confirm_doc_audit_user',
      },
      {
        title: '达人',
        dataIndex: 'personage',
      },
      // {
      //   title: '状态',
      //   dataIndex: 'status',
      //   width: 120,
      // },
      {
        title: '退款说明',
        dataIndex: 'refund_reason',
        slotName: 'multiLineText',
      },
      {
        title: '询价单号',
        dataIndex: 'inquiry_order_no',
      },
      {
        title: '跟踪次数',
        dataIndex: 'tracking_num',
        width: 100,
      },
      {
        dataIndex: 'order_source',
        title: '订单来源',
        defaultHide: true,
      },
      {
        dataIndex: 'payment_type',
        title: '付款方式',
        defaultHide: true,
        width: 100,
      },
      {
        dataIndex: 'is_refund_follow_up_again',
        title: '是否退款有效客户',
        width: 180,
        defaultHide: true,
      },
      {
        dataIndex: 'customer_intentionality',
        title: '客户意向度',
        width: 180,
        defaultHide: true,
        description:
          'A：确定出行时间，确认人数。\nB：确定人数，不确定出行时间/不确定人数。\nC：不确定人数，不确定出行时间。\nD：无意向，且非空号。',
      },
      // 是否投流
      {
        dataIndex: 'is_flow_text',
        title: '是否投流',
        slotName: 'is_flow_text',
        defaultHide: true,
        width: 120,
      },
      {
        dataIndex: 'cancellation_reason',
        title: '作废原因',
        defaultHide: true,
        render: ({ record }: TableColumnSlot) =>
          record.cancellation_reason
            ? record.cancellation_reason +
              (record.cancellation_reason_supplement
                ? `-${record.cancellation_reason_supplement}`
                : '')
            : '-',
        width: 120,
      },
      {
        title: '挽单池流转人',
        dataIndex: 'detainment_order_user',
        defaultHide: true,
      },
      {
        title: '流转挽单池理由',
        dataIndex: 'detainment_order_reason',
        render: getDictTxtRender(orderIntentionalityM),
        defaultHide: true,
      },
      {
        title: '是否经过挽单池',
        dataIndex: 'detainment_order_flag',
        render: ({ record }: TableColumnSlot) =>
          record.exchange_service_record ? '是' : '否',
        defaultHide: true,
      },
      {
        title: '是否秒拍秒退',
        description:
          '秒拍秒退的订单为抖音来客【酒旅销售明细】存在且已退款而【预约管理】中不存在的订单',
        dataIndex: 'is_quick_refund',
        render: getDictTxtRender(yesOrNo2),
        defaultHide: true,
      },
      {
        title: '电话跟进次数',
        dataIndex: 'phone_follow_up_num',
        defaultHide: true,
      },
      {
        title: '线索来源类型',
        dataIndex: 'thread_source',
        render: getDictTxtRender(clueSuorceM),
        defaultHide: true,
      },
      {
        title: '订单来源类型',
        dataIndex: 'source_detail',
        defaultHide: true,
      },
      {
        title: '落地尾款收款方',
        dataIndex: 'service_receiving_name',
        defaultHide: true,
      },
      {
        title: '服务地接名称',
        dataIndex: 'service_name',
        defaultHide: true,
      },
      {
        title: '订单类型',
        dataIndex: 'order_new_type',
      },
      {
        title: '订单细分类型',
        dataIndex: 'order_new_type_detail_show',
        slotName: 'multiLineText',
      },
      {
        title: '原客户信息',
        dataIndex: 'order_new_type_origin_customer_nickname',
        render: ({ record }: TableColumnSlot) =>
          record.order_new_type_origin_customer_phone
            ? `${record.order_new_type_origin_customer_nickname}(${record.order_new_type_origin_customer_phone})`
            : '-',
      },
      {
        title: '线索ID',
        dataIndex: 'clue_id',
        defaultHide: true,
        width: 100,
      },
    ],
  },
  {
    title: '客户信息',
    dataIndex: '客户信息',
    keys: [] as string[],
    dataList: [
      {
        title: '客户昵称',
        dataIndex: 'customer_name',
        width: 120,
      },
      {
        title: '手机号',
        dataIndex: 'phone',
        slotName: 'phone',
        width: 150,
      },
      {
        title: '联系人姓名',
        dataIndex: 'contact_name',
      },
      {
        title: '联系人手机号',
        dataIndex: 'customer_phone',
      },
      {
        title: '微信昵称',
        dataIndex: 'wechat',
      },
    ],
  },
  {
    title: '收支信息',
    dataIndex: '收支信息',
    keys: [] as string[],
    dataList: [
      {
        title: '总团款',
        dataIndex: 'total_cut_price',
        render: ({ record }: TableColumnSlot) =>
          moneyFormat(record.total_cut_price || record.advance_price),
        width: 180,
      },
      {
        title: '订金',
        dataIndex: 'advance_price',
        render: moneyFormatShow(),
        width: 160,
      },
      {
        title: '结算价',
        dataIndex: 'total_final_price',
        render: moneyFormatShow(),
        width: 160,
      },
      {
        title: '利润',
        dataIndex: 'profit',
        render: moneyFormatShow(),
        description:
          '利润 = 总团款 - 结算价 - 预估服务费 + 客服其他收入 - 客服其他支出 + 合同其他收入 - 合同其他支出 -  机票支出 - 赔付金额',
        width: 160,
      },
      {
        title: '地接代收款',
        dataIndex: 'tripartite_need_charge_amount',
        render: moneyFormatShow(),
        width: 180,
      },
      {
        title: '门市价',
        dataIndex: 'total_price',
        render: moneyFormatShow(),
        defaultHide: true,
        width: 120,
      },
      {
        title: '商品原价',
        dataIndex: 'goods_price',
        render: moneyFormatShow(),
        defaultHide: true,
        width: 120,
      },
      {
        title: '预估服务费',
        dataIndex: 'estimated_service_price',
        render: moneyFormatShow(),
        width: 180,
      },
      {
        title: '纸质合同费',
        dataIndex: 'price_paper_contract',
        defaultHide: true,
      },
      {
        title: '合同服务费',
        dataIndex: 'price_contract_service',
        defaultHide: true,
      },
      {
        title: '机票支出',
        dataIndex: 'air_ticket_expend',
        render: moneyFormatShow(),
        defaultHide: true,
        width: 100,
      },
      {
        title: '赔付金额',
        dataIndex: 'compensate_amount',
        render: moneyFormatShow(),
        defaultHide: true,
        width: 100,
      },
      {
        title: '赔付证明',
        dataIndex: 'compensate_explain_image_1',
        width: 100,
        defaultHide: true,
      },
      {
        title: '客服其他收入和支出',
        dataIndex: 'service_other_detail',
        defaultHide: true,
      },
      {
        title: '合同其他收入和支出',
        dataIndex: 'contract_other_detail',
        defaultHide: true,
      },
      {
        title: '旅游意外险费用',
        dataIndex: 'price_accident_insurance',
        defaultHide: true,
      },
    ],
  },
  {
    title: '出行信息',
    dataIndex: '出行信息',
    keys: [] as string[],
    dataList: [
      {
        title: '出发城市',
        dataIndex: 'departure_city',
        defaultHide: true,
        width: 100,
      },
      {
        title: '返回城市',
        dataIndex: 'return_city',
        defaultHide: true,
        width: 100,
      },
      {
        title: '出行人数',
        dataIndex: 'adult_num',
        render: ({ record }: TableColumnSlot) =>
          record.adult_num
            ? `${record.adult_num || 0}大 ${record.children_num || 0}小`
            : '-',
        width: 180,
      },
      {
        title: '房间信息',
        dataIndex: 'room_info',
      },
      {
        title: '出行航班时间',
        dataIndex: 'fs_doc_travel_time',
        slotName: 'popTxt',
        width: 140,
      },
      {
        title: '游客信息',
        dataIndex: 'fs_doc_travel_people',
        slotName: 'fs_doc_travel_people',
      },
    ],
  },
  {
    title: '日期时间',
    dataIndex: '日期时间',
    keys: [] as string[],
    dataList: [
      {
        title: '出行日期',
        dataIndex: 'real_travel_start_date',
        width: 120,
      },
      {
        title: '返程日期',
        dataIndex: 'real_travel_end_date',
        width: 120,
      },
      {
        title: '创建时间',
        dataIndex: 'order_create_time',
      },
      {
        title: '更新时间',
        dataIndex: 'update_time',
      },
      {
        dataIndex: 'time_add_wechat',
        title: '加微信时间',
        defaultHide: true,
      },
      {
        dataIndex: 'time_order_confirmed',
        title: '待计调审核时间',
        defaultHide: true,
      },
      {
        dataIndex: 'time_send_confirmation',
        title: '已发确认件时间',
        defaultHide: true,
      },
      {
        dataIndex: 'time_travel',
        title: '已出行时间',
        defaultHide: true,
      },
      {
        dataIndex: 'time_travel_back',
        title: '已核销时间',
        defaultHide: true,
      },
      {
        dataIndex: 'time_settle',
        title: '完成时间',
        defaultHide: true,
      },
      {
        dataIndex: 'refund_time',
        title: '已退款时间',
        defaultHide: true,
      },
      {
        dataIndex: 'time_distribute',
        title: '分配时间',
        defaultHide: true,
      },
      {
        dataIndex: 'time_cancel',
        title: '取消预约时间',
        defaultHide: true,
      },
      {
        dataIndex: 'time_accept',
        title: '受理时间',
        defaultHide: true,
      },
    ],
  },
];
allFieldsConfig.forEach((item: any) => {
  // 如果是运营，则不过滤
  // 如果是客服/计调，则隐藏是否投流
  if (
    !userStore.roles.includes(6) &&
    (userStore.roles.includes(3) || userStore.roles.includes(14))
  ) {
    item.dataList = item.dataList.filter(
      (citem: any) => citem.dataIndex !== 'is_flow_text'
    );
  }
  item.keys = item.dataList.map((citem: any) => {
    // @ts-ignore
    citem.key = citem.dataIndex;
    return citem.dataIndex;
  });
});

/*
 *
 *
 *
 *
 *
 *
 * */

const dataCacheStore = useDataCacheStore();
export const searchRules = computed(() => {
  let arr = [
    {
      field: 'area',
      label: '旅游线路',
      value: null,
      component_name: 'dict-select',
      alwaysShow: false,
      attr: {
        dataList: dataCacheStore.lineList,
        labelKey: 'line_name',
        valueKey: 'line_name',
        multiple: true,
      },
    },

    {
      field: 'local_travel_agency',
      label: '地接社',
      value: null,
      component_name: 'dict-select',
      attr: {
        dataList: dataCacheStore.getAgencyList() || [],
        labelKey: 'agency_name',
        valueKey: 'agency_name',
      },
    },
    {
      field: 'travel_line',
      label: '产品',
      value: null,
      component_name: markRaw(ProductSelectWithAgency),
      alwaysShow: false,
      attr: {
        'max-tag-count': 2,
        'allow-clear': true,
        'multiple': true,
        'format-label': (data: SelectOptionData) => data.label?.slice(0, 3),
      },
    },
    {
      field: 'service_user_fs_id',
      label: '客服',
      value: null,
      component_name: markRaw(requestSelect),
      alwaysShow: false,
      attr: {
        requestUrl: '/api/travel/allServiceUser',
        labelKey: 'user_name',
        valueKey: 'fs_user_id',
        sendParams: { no_has_dimission: true },
      },
    },
    {
      field: 'department_ids',
      label: '客服所属部门',
      value: null,
      component_name: markRaw(requestTreeSelect),
      alwaysShow: false,
      attr: {
        requestUrl: '/api/department/list',
        labelKey: 'department_name',
        childKey: 'child',
        multiple: true,
        maxTagCount: 2,
      },
    },
    {
      field: 'phoneKeyword',
      label: '手机号',
      value: null,
      component_name: 'a-input',
      alwaysShow: false,
      attr: {
        placeholder: '支持多个手机号，空格或逗号拼接',
        allowClear: true,
      },
    },
    {
      field: 'customer_name',
      label: '客户昵称',
      value: null,
      component_name: 'a-input',
      alwaysShow: false,
      attr: {
        placeholder: '客户昵称',
        allowClear: true,
      },
    },
    {
      field: 'customer_intentionality',
      label: '客户意向度',
      value: null,
      component_name: 'dict-select',
      alwaysShow: false,
      attr: {
        dataList: orderIntentionalityM,
      },
    },
    {
      field: 'goods_id',
      label: '商品ID',
      value: null,
      component_name: 'a-input',
      attr: {
        placeholder: '商品ID',
        allowClear: true,
      },
    },
    {
      field: 'is_add_wechat',
      label: '是否添加微信',
      value: null,
      component_name: 'dict-select',
      alwaysShow: false,
      attr: {
        dataList: [...yesOrNo, { label: '未选择', value: -1 }],
      },
    },
    {
      field: 'is_call_connected',
      label: '电话是否接通',
      value: null,
      component_name: 'dict-select',
      alwaysShow: false,
      attr: {
        dataList: yesOrNoOrNull,
      },
    },
    {
      field: 'personage',
      label: '达人',
      value: null,
      component_name: markRaw(requestSelect),
      attr: {
        requestUrl: '/api/travel/personage',
        labelKey: 'label',
        valueKey: 'value',
      },
    },
    {
      field: 'anchor_id',
      label: '主播',
      value: null,
      component_name: markRaw(requestSelect),
      attr: {
        // requestUrl: '/api/travel/anchorUserList',
        requestUrl: '/api/user/userList',
        labelKey: 'user_name',
        // valueKey: 'anchor_user_id',
        pageSize: 999999,
        maxTagCount: 4,
        sendParams: { pageSize: 999999, show_empty_flag: true },
      },
    },
    // 确认件审核人
    {
      field: 'confirm_doc_audit_user_id',
      label: '确认件审核人',
      value: null,
      component_name: 'department-user-tree-select',
      attr: {
        placeholder: '请选择',
        maxDisplayCount: 2,
        sendParams: {
          is_order_filter_confirm_doc_audit_user_id: true,
        },
      },
    },
    {
      field: 'status',
      label: '订单状态',
      value: null,
      component_name: 'dict-select',
      alwaysShow: true,
      attr: {
        maxTagCount: 1,
        multiple: true,
        dataList: orderStatusListM,
      },
    },
    {
      field: 'source',
      label: '来源媒体',
      value: null,
      component_name: 'dict-select',
      attr: {
        dataList: contactWayListM,
      },
    },
    {
      field: 'order_source',
      label: '订单来源',
      value: null,
      component_name: markRaw(requestSelect),
      attr: {
        'request-url': '/api/report/liveRoomList',
        'get-data-list': (data: any[]): any[] => [
          { live_room_name: '内容私域' },
          { live_room_name: '直播间私域' },
          ...data,
        ],
        'label-key': 'live_room_name',
        'value-key': 'live_room_name',
        'max-tag-count': 2,
        'allow-clear': true,
      },
    },
    {
      field: 'payment_type',
      label: '付款方式',
      value: null,
      component_name: 'dict-select',
      attr: {
        dataList: payTypeM,
      },
    },
    {
      field: 'media_order_no',
      label: '媒体订单号',
      value: null,
      component_name: 'a-input',
      attr: {
        placeholder: '媒体订单号',
        allowClear: true,
      },
    },
    {
      field: 'sale_channel',
      label: '成交渠道',
      value: null,
      component_name: markRaw(requestSelect),
      attr: {
        'request-url': '/api/travel/getLiveSaleChannel',
        'label-key': 'value',
        'value-key': 'value',
        'get-data-list': (data: any[]): any[] =>
          data.map((item) => ({ value: item })),
        'allow-clear': true,
      },
    },
    {
      field: 'pay_type',
      label: '支付方式',
      value: null,
      component_name: 'dict-select',
      attr: {
        dataList: depositPayTypeM2,
      },
    },
    {
      field: 'redistribution',
      label: '是否再分配',
      value: null,
      component_name: 'dict-select',
      attr: {
        dataList: yesOrNo,
      },
    },
    {
      field: 'is_refund_follow_up_again',
      label: '是否退款有效客户',
      value: null,
      component_name: 'dict-select',
      attr: {
        dataList: yesOrNoOrNull,
      },
    },
    {
      field: 'real_travel_start_date',
      label: '出行日期',
      value: null,
      component_name: 'c-range-picker',
      alwaysShow: false,
      attr: {
        needDefault: false,
        allowClear: true,
        maxDateDisabled: false,
      },
    },
    {
      field: 'order_create_time',
      label: '创建时间',
      value: null,
      alwaysShow: false,
      component_name: 'c-range-picker',
    },
    {
      field: 'time_add_wechat',
      label: '加微信时间',
      value: null,
      component_name: 'c-range-picker',
    },
    {
      field: 'time_order_confirmed',
      label: '待计调审核时间',
      value: null,
      component_name: 'c-range-picker',
    },
    {
      field: 'time_send_confirmation',
      label: '已发确认件时间',
      value: null,
      component_name: 'c-range-picker',
    },
    {
      field: 'time_travel',
      label: '已出行时间',
      value: null,
      component_name: 'c-range-picker',
    },
    {
      field: 'time_travel_back',
      label: '已核销时间',
      value: null,
      component_name: 'c-range-picker',
    },
    {
      field: 'time_settle',
      label: '完成时间',
      value: null,
      component_name: 'c-range-picker',
    },
    {
      field: 'refund_time',
      label: '已退款时间',
      value: null,
      component_name: 'c-range-picker',
    },
    {
      field: 'time_distribute',
      label: '分配时间',
      value: null,
      component_name: 'c-range-picker',
    },
    {
      field: 'time_cancel',
      label: '取消预约时间',
      value: null,
      component_name: 'c-range-picker',
    },
    {
      field: 'time_accept',
      label: '受理时间',
      value: null,
      component_name: 'c-range-picker',
    },
    {
      field: 'thread_source',
      label: '线索来源类型',
      value: null,
      component_name: 'dict-select',
      attr: {
        dataList: clueSuorceM,
      },
    },
    {
      field: 'source_detail',
      label: '订单来源类型',
      value: null,
      component_name: markRaw(requestSelect),
      attr: {
        requestUrl: '/api/travel/getSourceDetailMap',
        labelKey: 'value',
        valueKey: 'value',
        maxTagCount: 1,
      },
    },
    {
      field: 'is_exchange_user',
      label: '是否经过挽单池',
      value: null,
      component_name: markRaw(dictSelect),
      attr: {
        dataList: yesOrNo3,
      },
    },
    {
      field: 'order_new_type_custom',
      label: '订单类型',
      value: null,
      component_name: markRaw(OrderTypeSelect),
    },
    {
      field: 'order_new_type_origin_customer',
      label: '原客户昵称及电话',
      value: null,
      component_name: 'a-input',
      attr: {
        allowClear: true,
        placeholder: '原客户昵称及电话',
      },
    },
    {
      field: 'fs_doc_travel_people',
      label: '游客信息',
      value: null,
      component_name: 'a-input',
      attr: {
        allowClear: true,
        placeholder: '游客信息',
      },
    },
    {
      field: 'is_flow',
      label: '是否投流',
      value: null,
      component_name: 'dict-select',
      alwaysShow: false,
      attr: {
        dataList: isFlowM,
      },
    },
  ];
  // 如果是运营 不过滤
  // 客服/计调 不展示是否投流
  if (
    !userStore.roles.includes(6) &&
    (userStore.roles.includes(3) || userStore.roles.includes(14))
  ) {
    arr = arr.filter(
      // 投流有两个字段 兼容一下
      (item) => item.field !== 'is_flow_text' && item.field !== 'is_flow'
    );
  }
  return arr;
});
