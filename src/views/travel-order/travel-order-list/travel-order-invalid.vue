<template>
  <d-modal
    v-model:visible="visible"
    width="400px"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    :mask-closable="false"
    :esc-to-close="false"
    title="作废订单"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formModel" :auto-label-width="true">
      <a-form-item label="订单编号">
        <a-input v-model="formModel.order_no" readonly />
      </a-form-item>
      <a-form-item
        label="作废原因"
        field="cancellation_reason"
        :rules="requiredRule"
      >
        <dict-select
          v-model="formModel.cancellation_reason"
          :data-list="cancelReasonM"
          placeholder="请选择作废原因"
        />
      </a-form-item>
      <a-form-item
        v-if="formModel.cancellation_reason === '其他'"
        label="原因说明"
        field="cancellation_reason_supplement"
        :rules="requiredRule"
      >
        <a-textarea
          v-model="formModel.cancellation_reason_supplement"
          :auto-size="{
            minRows: 3,
            maxRows: 6,
          }"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { cancelReasonM } from '@/components/dict-select/dict-travel';
  import { requiredRule } from '@/utils/util';

  const defaultForm = () => ({
    id: null,
    order_no: null,
    cancellation_reason: null,
    cancellation_reason_supplement: null,
  });

  const emit = defineEmits(['save']);

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const formModel = ref(defaultForm());
  const info = ref<any>({});

  const validate = () => {
    return formRef.value?.validate();
  };
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  const show = (dinfo: any) => {
    info.value = dinfo;
    let initForm = defaultForm();
    formModel.value = initForm;
    Object.keys(initForm).forEach((key) => {
      // @ts-ignore
      formModel.value[key] =
        dinfo[key] || initForm[key as keyof typeof initForm];
    });
    clearValidate();
    visible.value = true;
    loading.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  const handleBeforeOk = async () => {
    const result = await validate();
    if (!result) {
      loading.value = true;
      request('/api/travel/finalOrderSave', {
        ...formModel.value,
        status: '已作废',
      })
        .then(() => {
          Message.success('保存成功');
          handleCancel();
          emit('save');
        })
        .catch(() => {
          loading.value = false;
        });
    }
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>
