<template>
  <d-modal
    v-model:visible="visible"
    width="700px"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    :mask-closable="false"
    :esc-to-close="false"
    title="编辑订单信息"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formModel" :auto-label-width="true">
      <a-form-item label="客服">
        <request-select
          v-model="formModel.service_user_fs_id"
          request-url="/api/travel/allServiceUser"
          label-key="user_name"
          value-key="fs_user_id"
          @change="(val, item) => (formModel.service_user = item.user_name)"
        />
      </a-form-item>
      <a-form-item v-if="info.inquiry_order_no" label="备注">
        <a-textarea
          v-model="formModel.remark"
          allow-clear
          :auto-size="{ minRows: 2, maxRows: 6 }"
        />
      </a-form-item>
      <a-form-item label="出行航班时间">
        <a-textarea
          v-model="formModel.fs_doc_travel_time"
          :auto-size="{ minRows: 5 }"
          allow-clear
        />
      </a-form-item>
      <a-form-item label="游客信息">
        <a-textarea
          v-model="formModel.fs_doc_travel_people"
          :auto-size="{ minRows: 5 }"
          allow-clear
        />
      </a-form-item>

      <a-form-item label="询价单号">
        <a-input v-model="formModel.inquiry_order_no" allow-clear />
      </a-form-item>
      <a-form-item label="联系人姓名">
        <a-input v-model="formModel.customer_name_and_phone" allow-clear />
      </a-form-item>
      <a-form-item label="联系人手机号">
        <a-input v-model="formModel.customer_phone" allow-clear />
      </a-form-item>
      <a-form-item label="是否添加微信">
        <dict-select
          v-model="formModel.is_add_wechat"
          :data-list="yesOrNo"
          :allow-clear="false"
        />
      </a-form-item>
      <a-form-item label="微信昵称">
        <a-input v-model="formModel.wechat" allow-clear />
      </a-form-item>
      <a-form-item
        v-if="visible"
        label="预约出行截图"
        help="请确保图片上传完成以后再提交"
        field="confirmation_images"
        :rules="requiredUploadRule"
      >
        <upload-image-file v-model="formModel.confirmation_images" />
      </a-form-item>
      <a-form-item v-if="showAdvancePrice" label="订金">
        <a-input-number v-model="formModel.advance_price" />
      </a-form-item>

      <a-form-item label="是否退款再跟进">
        <dict-select
          v-model="formModel.is_refund_follow_up_again"
          :data-list="yesOrNo"
        />
      </a-form-item>
      <a-form-item
        label="电话是否接通"
        tooltip="电话接通的含义是有效接通，通话时长超过10秒及以上，打通没人说话或者打通秒挂均不算接通"
      >
        <dict-select
          v-model="formModel.is_call_connected"
          :data-list="yesOrNo"
        />
      </a-form-item>
      <a-form-item
        label="客户意向度"
        tooltip="A：确定出行时间，确认人数。B：确定人数，不确定出行时间/不确定人数。C：不确定人数，不确定出行时间。D：无意向，且非空号。"
      >
        <dict-select
          v-model="formModel.customer_intentionality"
          :data-list="orderIntentionalityM"
        />
      </a-form-item>
      <!--<a-form-item label="最终成交价">
        <a-input-number v-model="formModel.final_deal_price" />
      </a-form-item>
      <a-form-item label="最终成本价">
        <a-input-number v-model="formModel.final_cost_price" />
      </a-form-item>-->
    </a-form>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { cloneDeep, toString } from 'lodash';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import {
    requiredRule,
    requiredRuleArr,
    requiredUploadRule,
  } from '@/utils/util';
  import RequestSelect from '@/components/select/request-select.vue';
  import {
    contactWayListM,
    orderIntentionalityM,
    priceStatusListM,
    yesOrNo,
  } from '@/components/dict-select/dict-travel';
  import UploadImageFile from '@/components/upload-file/upload-image-file.vue';

  const defaultForm = () => ({
    id: null,
    service_user_fs_id: null,
    service_user: null,
    final_deal_price: null,
    final_cost_price: null,

    remark: null,
    fs_doc_travel_time: null,
    fs_doc_travel_people: null,
    inquiry_order_no: null,
    customer_name_and_phone: null,
    customer_phone: null,
    advance_price: null,
    is_refund_follow_up_again: null,
    is_call_connected: null,
    customer_intentionality: null,
    is_add_wechat: '否',
    wechat: null,
    confirmation_images: [],
  });

  const emit = defineEmits(['save']);

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const formModel = ref(defaultForm());
  const info = ref<any>({});

  const showAdvancePrice = computed(
    () =>
      info.value?.children?.find((item: any) => item.status === '已转线下') ||
      info.value?.payment_type === '线下'
  );
  const validate = () => {
    return formRef.value?.validate();
  };
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  const show = (dinfo: any) => {
    info.value = dinfo;
    let initForm = defaultForm();
    formModel.value = initForm;
    Object.keys(initForm).forEach((key) => {
      if (dinfo[key] && ['advance_price'].includes(key)) {
        // @ts-ignore
        formModel.value[key] = parseFloat(dinfo[key]);
      } else {
        // @ts-ignore
        formModel.value[key] =
          dinfo[key] || initForm[key as keyof typeof initForm];
      }
    });
    clearValidate();
    visible.value = true;
    loading.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  const handleBeforeOk = async () => {
    const result = await validate();
    if (!result) {
      loading.value = true;
      request('/api/travel/finalOrderSave', {
        ...formModel.value,
      })
        .then(() => {
          Message.success('保存成功');
          handleCancel();
          emit('save');
        })
        .catch(() => {
          loading.value = false;
        });
    }
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>
