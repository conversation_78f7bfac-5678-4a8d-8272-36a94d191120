<template>
  <d-modal
    v-model:visible="visible"
    width="400px"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    :mask-closable="false"
    :esc-to-close="false"
    title="分配"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formModel" :auto-label-width="true">
      <a-form-item label="订单编号">
        <a-input v-model="formModel.order_no" readonly />
      </a-form-item>
      <a-form-item
        label="客服"
        field="service_user_fs_id"
        :rules="requiredRule"
      >
        <request-select
          v-model="formModel.service_user_fs_id"
          request-url="/api/travel/allServiceUser"
          label-key="user_name"
          value-key="fs_user_id"
          @change="
            (val, item) => (formModel.service_user = item?.user_name || '')
          "
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { requiredRule } from '@/utils/util';
  import RequestSelect from '@/components/select/request-select.vue';

  const defaultForm = () => ({
    id: null,
    order_no: null,
    service_user: null,
    service_user_fs_id: null,
  });

  const emit = defineEmits(['save']);

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const formModel = ref(defaultForm());
  const info = ref<any>({});

  const validate = () => {
    return formRef.value?.validate();
  };
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  const show = (dinfo: any) => {
    info.value = dinfo;
    let initForm = defaultForm();
    formModel.value = initForm;
    Object.keys(initForm).forEach((key) => {
      // @ts-ignore
      formModel.value[key] =
        dinfo[key] || initForm[key as keyof typeof initForm];
    });
    clearValidate();
    visible.value = true;
    loading.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  const handleBeforeOk = async () => {
    const result = await validate();
    if (!result) {
      loading.value = true;
      request('/api/travel/redistributionTransferOrder', {
        ...formModel.value,
      })
        .then(() => {
          Message.success('保存成功');
          handleCancel();
          emit('save');
        })
        .catch(() => {
          loading.value = false;
        });
    }
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>
