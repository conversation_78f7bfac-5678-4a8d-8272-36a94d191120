<template>
  <d-modal
    v-model:visible="visible"
    :width="shouldShowConfirmationInfo ? '1400px' : '1000px'"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
      paddingTop: 0,
    }"
    :title="`详情【${info.order_info?.order_no || ''}】`"
  >
    <!-- 主要内容区域 -->
    <div
      class="modal-content-wrapper"
      :class="{ 'with-confirmation': shouldShowConfirmationInfo }"
    >
      <!-- 左侧详情区域 -->
      <div class="detail-section">
        <div class="travel-order-detail">
          <template v-for="item in config" :key="item">
            <a-divider>{{ item.name }}</a-divider>
            <a-form auto-label-width :model="item">
              <a-row :gutter="12">
                <a-col
                  v-for="citem in item.list"
                  :key="citem.key"
                  :span="citem.span || 8"
                >
                  <a-form-item :label="citem.label" :tooltip="citem.tips">
                    <!-- <a-textarea
                      v-if="
                        citem.key === 'inquiry_order_info.remark' &&
                        formModel.id
                      "
                      v-model="formModel.remark"
                      allow-clear
                      :auto-size="{ minRows: 2, maxRows: 6 }"
                    />
                    <dict-select
                      v-else-if="
                        citem.key === 'order_info.is_add_wechat' && formModel.id
                      "
                      v-model="formModel.is_add_wechat"
                      :data-list="yesOrNo"
                      :allow-clear="false"
                    /> -->
                    <template
                      v-if="citem.key === 'order_info.confirmation_link'"
                    >
                      <a-link
                        v-if="info.order_info?.confirmation_link"
                        :href="`/ensureLinkRedirect?order_no=${info.order_info?.order_no}`"
                        target="_blank"
                        size="mini"
                      >
                        查看
                      </a-link>
                      <span v-else>-</span>
                    </template>
                    <template v-else-if="citem.key === 'order_info.status'">
                      <a-tag
                        :color="
                          info.order_info?.status === '已退款'
                            ? colors.error
                            : info.order_info?.status === '已核销'
                            ? colors.success
                            : ''
                        "
                        size="mini"
                      >
                        {{ info.order_info?.status }}
                      </a-tag>
                    </template>
                    <template v-else-if="citem.type === 'imgs'">
                      <a-image
                        v-for="(ccitem, index) in getPathValue(
                          info,
                          citem.key
                        ) || []"
                        :key="index"
                        :src="ccitem"
                        :width="60"
                        :height="60"
                        fit="contain"
                      />
                    </template>
                    <template
                      v-else-if="
                        citem.key === 'order_info.compensate_explain_image_1'
                      "
                    >
                      <a-space>
                        <a-image
                          :src="info.order_info?.compensate_explain_image_1"
                          :width="60"
                          :height="60"
                          fit="contain"
                        />
                        <a-image
                          :src="info.order_info?.compensate_explain_image_2"
                          :width="60"
                          :height="60"
                          fit="contain"
                        />
                      </a-space>
                    </template>
                    <template
                      v-else-if="
                        citem.key === 'order_info.service_other_detail'
                      "
                    >
                      <a-card class="w100p card-b">
                        <a-row :gutter="10" align="center">
                          <a-col flex="200px">内容 </a-col>
                          <a-col flex="1"> 客服其他收入 </a-col>
                          <a-col flex="1"> 客服其他支出 </a-col>
                        </a-row>
                        <a-row :gutter="10" align="center" class="mt-10">
                          <a-col flex="200px">汇总 </a-col>
                          <a-col flex="1">
                            {{ info.order_info?.service_other_income_total }}
                          </a-col>
                          <a-col flex="1">
                            {{ info.order_info?.service_other_expend_total }}
                          </a-col>
                        </a-row>
                        <a-row
                          v-for="(ccitem, index) in info.order_info
                            ?.service_other_detail"
                          :key="index"
                          :gutter="10"
                          class="mt-10"
                          align="center"
                        >
                          <a-col flex="200px">
                            <span>{{ ccitem.name }}</span>
                          </a-col>
                          <a-col flex="1">
                            {{ ccitem.income || '-' }}
                          </a-col>
                          <a-col flex="1">
                            {{ ccitem.expend || '-' }}
                          </a-col>
                        </a-row>
                      </a-card>
                    </template>
                    <template
                      v-else-if="
                        citem.key === 'order_info.contract_other_detail'
                      "
                    >
                      <a-card class="w100p card-b">
                        <a-row :gutter="10" align="center">
                          <a-col flex="200px">内容 </a-col>
                          <a-col flex="1"> 客服其他收入 </a-col>
                          <a-col flex="1"> 客服其他支出 </a-col>
                        </a-row>
                        <a-row :gutter="10" align="center" class="mt-10">
                          <a-col flex="200px">汇总 </a-col>
                          <a-col flex="1">
                            {{ info.order_info?.contract_other_income_total }}
                          </a-col>
                          <a-col flex="1">
                            {{ info.order_info?.contract_other_expend_total }}
                          </a-col>
                        </a-row>
                        <a-row
                          v-for="(ccitem, index) in info.order_info
                            ?.contract_other_detail"
                          :key="index"
                          :gutter="10"
                          class="mt-10"
                          align="center"
                        >
                          <a-col flex="200px">
                            <span>{{ ccitem.name }}</span>
                          </a-col>
                          <a-col flex="1">
                            {{ ccitem.income || '-' }}
                          </a-col>
                          <a-col flex="1">
                            {{ ccitem.expend || '-' }}
                          </a-col>
                        </a-row>
                      </a-card>
                    </template>
                    <span v-else class="text-wrap">
                      {{ citem.value || getPathValue(info, citem.key) || '-' }}
                    </span>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </template>
        </div>
      </div>

      <!-- 右侧确认件信息展示区域 -->
      <div v-if="shouldShowConfirmationInfo" class="confirmation-section">
        <div class="confirmation-header">
          <h3>确认件信息</h3>
          <div class="header-actions">
            <span v-if="confirmationLoading" class="update-indicator">
              <icon-loading />
              实时更新中...
            </span>
            <a-button
              type="outline"
              size="small"
              :loading="copyLoading"
              @click="copyConfirmationInfo"
            >
              <template #icon>
                <icon-copy />
              </template>
              全部复制
            </a-button>
          </div>
        </div>
        <div class="confirmation-content">
          <a-spin :loading="confirmationLoading" style="width: 100%">
            <div v-if="confirmationInfo" class="confirmation-text">
              {{ confirmationInfo }}
            </div>
            <div v-else class="no-confirmation"> 暂无确认件信息 </div>
          </a-spin>
        </div>
      </div>
    </div>
    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">确定</a-button>
          <!-- <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button> -->
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { computed, ref, nextTick, watch } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { moneyFormat } from '@/utils/table-utils/table-util';
  import {
    orderIntentionalityM,
    yesOrNo,
  } from '@/components/dict-select/dict-travel';
  import { TableColumnSlot } from '@/global';
  import { colors } from '@/components/dict-select/dict-common';
  import { getText } from '@/components/dict-select/dict-util';
  import { useClipboard } from '@vueuse/core';
  import { IconLoading } from '@arco-design/web-vue/es/icon';
  import { useDataCacheStore } from '@/store';
  import { getPathValue } from '../../../utils/util';

  const emit = defineEmits(['save']);
  const dataStore = useDataCacheStore();
  const { copy } = useClipboard();

  const defaultForm = () => ({
    id: null,
    remark: null,
    is_add_wechat: null,
  });

  const visible = ref(false);
  const loading = ref(false);
  const info = ref<any>({});
  const formModel = ref(defaultForm());

  // 确认件相关状态
  const confirmationInfo = ref('');
  const confirmationLoading = ref(false);
  const copyLoading = ref(false);

  // 计算属性：判断是否应该显示确认件信息
  const shouldShowConfirmationInfo = computed(() => {
    const status = info.value.order_info?.status;
    const level = info.value.order_info?.level;

    // 当订单状态为已确认(level 2)、已发确认件(level 3)、已出行或已核销时显示
    return (
      level === 2 || // 已确认
      level === 3 || // 已发确认件
      status === '已出行' ||
      status === '已核销'
    );
  });
  const config = computed(() => [
    {
      name: '订单信息',
      list: [
        { label: '订单编号', key: 'order_info.order_no' },
        { label: '状态', key: 'order_info.status' },
        { label: '媒体订单号', key: 'order_info.media_order_no' },
        { label: '来源媒体', key: 'order_info.source' },
        // { label: '订单来源', key: 'order_info.order_source' },
        { label: '付款方式', key: 'order_info.payment_type' },
        { label: '客服', key: 'order_info.service_user' },
        { label: '手机号', key: 'order_info.phone' },
        // { label: '达人', key: 'order_info.personage' },
        { label: '商品原价', key: 'order_info.goods_price' },
        { label: '商品ID', key: 'order_info.goods_id' },
        { label: '退款说明', key: 'order_info.refund_reason' },
        { label: '主播', key: 'order_info.anchor_name' },
        { label: '创建时间', key: 'order_info.order_create_time' },
        { label: '更新时间', key: 'order_info.update_time' },
        { label: '订单类型', key: 'order_info.order_new_type' },
        { label: '订单细分类型', key: 'order_info.order_new_type_detail' },
        {
          label: '原客户昵称',
          key: 'order_info.order_new_type_origin_customer_nickname',
          hide: info.value.order_info?.order_new_type_detail !== '客户转介绍',
        },
        {
          label: '原客户电话',
          key: 'order_info.order_new_type_origin_customer_phone',
          hide: info.value.order_info?.order_new_type_detail !== '客户转介绍',
        },
        { label: '是否添加微信', key: 'order_info.is_add_wechat' },
        {
          label: '是否退款再跟进',
          key: 'order_info.is_refund_follow_up_again',
        },
        { label: '电话是否接通', key: 'order_info.is_call_connected' },
        {
          label: '客户意向度',
          key: 'order_info.customer_intentionality',
          value: getText(
            orderIntentionalityM,
            getPathValue(info.value, 'order_info.customer_intentionality')
          ),
        },
        {
          label: '线索ID',
          key: 'thread_id',
          hide: !info.value.thread_id,
        },
        {
          label: '支付方式',
          key: 'order_info.is_miniapp_pay',
          value:
            info.value.order_info?.is_miniapp_pay === 1
              ? '小程序支付'
              : '对公账户',
          hide: !info.value.thread_id,
        },
        {
          label: '小程序商品',
          key: 'order_info.miniapp_product_name',
          hide:
            !info.value.thread_id ||
            info.value.order_info?.is_miniapp_pay !== 1,
        },
        {
          label: '订金收款方',
          key: 'order_info.payee_name',
          hide:
            !info.value.thread_id ||
            info.value.order_info?.is_miniapp_pay === 1,
        },
        {
          label: '收款截图凭证',
          key: 'order_info.payee_images',
          type: 'imgs',
          hide:
            !info.value.thread_id ||
            info.value.order_info?.is_miniapp_pay === 1,
        },
      ].filter((item) => !item.hide),
    },
    {
      name: '询价信息',
      list: [
        { label: '询价单号', key: 'inquiry_order_info.order_no' },
        { label: '客户昵称', key: 'inquiry_order_info.customer_name' },
        { label: '旅游线路', key: 'inquiry_order_info.area' },
        { label: '地接社', key: 'inquiry_order_info.local_travel_agency' },
        { label: '产品', key: 'inquiry_order_info.travel_line' },
        { label: '微信昵称', key: 'inquiry_order_info.wechat' },
        { label: '询价客服', key: 'inquiry_order_info.service_user' },
        { label: '媒体账号', key: 'inquiry_order_info.media_account' },
      ],
    },
    {
      name: '出行信息',
      list: [
        { label: '出行日期', key: 'inquiry_order_info.real_travel_start_date' },
        { label: '返程日期', key: 'inquiry_order_info.real_travel_end_date' },
        { label: '出发城市', key: 'order_info.departure_city' },
        { label: '返回城市', key: 'order_info.return_city' },
        {
          label: '出行人数',
          key: 'inquiry_order_info.adult_num',
          value: info.value.inquiry_order_info?.adult_num
            ? `${info.value.inquiry_order_info?.adult_num}大 ${info.value.inquiry_order_info?.children_num}小`
            : '-',
        },
        { label: '房间信息', key: 'order_info.room_info' },
        {
          label: '出行航班时间',
          key: 'order_info.fs_doc_travel_time',
          span: 24,
        },
        { label: '游客信息', key: 'order_info.fs_doc_travel_people', span: 24 },

        { label: '备注', key: 'inquiry_order_info.remark', span: 24 },
        { label: '联系人姓名', key: 'order_info.customer_name' },
        { label: '联系人手机号', key: 'order_info.customer_phone' },
        { label: '床位数', key: 'inquiry_order_info.standard_room' },
        { label: '车型', key: 'inquiry_order_info.car_type' },
        { label: '确认件链接', key: 'order_info.confirmation_link' },
        {
          label: '预约出行截图',
          key: 'order_info.confirmation_images',
          type: 'imgs',
          span: 24,
        },
      ],
    },
    {
      name: '收支信息',
      list: [
        {
          label: '总团款',
          key: 'inquiry_order_info.total_cut_price',
          value: moneyFormat(
            info.value.inquiry_order_info?.total_cut_price ||
              info.value.order_info?.advance_price
          ),
        },
        { label: '订金', key: 'order_info.advance_price' },
        { label: '结算价', key: 'inquiry_order_info.total_final_price' },
        {
          label: '利润',
          key: 'order_info.profit',
          // tips: '利润 = 总团款-结算价-预估服务费',
        },
        // {
        //  label: '地接代收款',
        //  key: 'tripartite_need_charge_amount',
        //  tips: '地接代收款 = 总团款-订金',
        // },
        // { label: '门市价', key: 'total_price' },
        { label: '预估服务费', key: 'order_info.estimated_service_price' },
        { label: '机票支出', key: 'order_info.air_ticket_expend' },
        { label: '赔付金额', key: 'order_info.compensate_amount' },
        {
          label: '赔付证明',
          key: 'order_info.compensate_explain_image_1',
          hide: !getPathValue(info.value, 'order_info.compensate_amount'),
          span: 16,
        },
        {
          label: '客服其他收入和支出',
          key: 'order_info.service_other_detail',
          span: 24,
        },
        {
          label: '合同其他收入和支出',
          key: 'order_info.contract_other_detail',
          span: 24,
        },
      ].filter((item) => !item.hide),
    },
    {
      name: '操作统计',
      list: [
        { label: '加微信时间', key: 'order_info.time_add_wechat' },
        { label: '受理时间', key: 'order_info.time_accept' },
        { label: '订单确认时间', key: 'order_info.time_order_confirmed' },
        { label: '发确认件时间', key: 'order_info.time_send_confirmation' },
        { label: '出行时间', key: 'order_info.time_travel' },
        { label: '核销时间', key: 'order_info.time_travel_back' },
        { label: '完成时间', key: 'order_info.time_settle' },
        { label: '退款时间', key: 'order_info.refund_time' },
        { label: '分配时间', key: 'order_info.time_distribute' },
        { label: '取消预约时间', key: 'order_info.time_cancel' },
        { label: '跟踪次数', key: 'order_info.tracking_num' },
      ],
    },
  ]);

  // 获取确认件信息
  const getConfirmationInfo = async (order_no: string) => {
    confirmationLoading.value = true;
    try {
      // 组装确认件信息
      const orderInfo = info.value;
      const inquiry = orderInfo.inquiry_order_info;
      const order = orderInfo.order_info;

      // 获取线路名称
      const lineInfo = inquiry.line_id
        ? dataStore.getLineItem('line_id', inquiry.line_id, 'line_name')
        : null;
      const lineName = lineInfo?.line_name || '';

      // 根据实际数据组装确认件文本
      const confirmationText = `${inquiry?.real_travel_start_date || ''} ${
        inquiry?.travel_line || lineName
      }

出行人信息：
${order.fs_doc_travel_people || ''}
联系方式：${order.customer_phone || order.phone || ''}
房间信息：${order.room_info || ''}
航班信息：
${order.fs_doc_travel_time || ''}
备注：
${inquiry.remark || ''}`;

      confirmationInfo.value = confirmationText;
    } catch (error) {
      console.error('获取确认件信息失败:', error);
      confirmationInfo.value = '获取确认件信息失败';
    } finally {
      confirmationLoading.value = false;
    }
  };

  function getInfo(order_no: string) {
    request('/api/travel/orderInfoV2', {
      order_no,
    }).then((res) => {
      info.value = res.data || {};
      // 如果需要显示确认件信息，则获取确认件数据
      nextTick(() => {
        if (shouldShowConfirmationInfo.value) {
          getConfirmationInfo(order_no);
        }
      });
    });
  }

  // 复制确认件信息
  const copyConfirmationInfo = async () => {
    if (!confirmationInfo.value) {
      Message.warning('暂无确认件信息可复制');
      return;
    }

    copyLoading.value = true;
    try {
      await copy(confirmationInfo.value);
      Message.success('确认件信息已复制到剪贴板');
    } catch (error) {
      console.error('复制失败:', error);
      Message.error('复制失败，请手动复制');
    } finally {
      copyLoading.value = false;
    }
  };

  // 监听确认件显示状态变化
  watch(
    shouldShowConfirmationInfo,
    (newValue) => {
      if (newValue && info.value.order_info?.order_no) {
        // getConfirmationInfo(info.value.order_info.order_no);
      } else {
        confirmationInfo.value = '';
      }
    },
    { immediate: false }
  );

  const show = (dinfo: any) => {
    let initForm = defaultForm();
    formModel.value = initForm;
    Object.keys(initForm).forEach((key) => {
      if (dinfo[key] && ['advance_price'].includes(key)) {
        // @ts-ignore
        formModel.value[key] = parseFloat(dinfo[key]);
      } else {
        // @ts-ignore
        formModel.value[key] =
          dinfo[key] || initForm[key as keyof typeof initForm];
      }
    });
    getInfo(dinfo.order_no);
    visible.value = true;
    loading.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  const handleBeforeOk = async () => {
    loading.value = true;
    request('/api/travel/finalOrderSave', {
      ...formModel.value,
    })
      .then(() => {
        Message.success('保存成功');
        handleCancel();
        emit('save');
      })
      .catch(() => {
        loading.value = false;
      });
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped>
  .modal-content-wrapper {
    display: flex;
    gap: 20px;
    height: 100%;

    &.with-confirmation {
      .detail-section {
        flex: 1;
        min-width: 0;
      }

      .confirmation-section {
        width: 400px;
        flex-shrink: 0;
      }
    }

    .detail-section {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
    }
  }

  .confirmation-section {
    border-left: 1px solid var(--color-border-2);
    padding-left: 20px;
    display: flex;
    flex-direction: column;
    height: 100%;

    .confirmation-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid var(--color-border-2);

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--color-text-1);
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 12px;

        .update-indicator {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: var(--color-text-3);

          .arco-icon {
            animation: spin 1s linear infinite;
          }
        }
      }
    }

    .confirmation-content {
      flex: 1;
      overflow-y: auto;
      background: var(--color-fill-1);
      border-radius: 6px;
      padding: 16px;

      .confirmation-text {
        white-space: pre-wrap;
        word-break: break-word;
        line-height: 1.6;
        font-size: 14px;
        color: var(--color-text-1);
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      }

      .no-confirmation {
        text-align: center;
        color: var(--color-text-3);
        font-size: 14px;
        padding: 40px 0;
      }
    }
  }

  // 文本自动换行样式
  .text-wrap {
    word-wrap: break-word;
    word-break: break-word;
    white-space: normal;
    overflow-wrap: break-word;
    hyphens: auto;
    line-height: 1.4;
  }

  .travel-order-detail :deep(.arco-form-item) {
    margin-bottom: 8px !important;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .modal-content-wrapper.with-confirmation {
      .confirmation-section {
        width: 350px;
      }
    }
  }

  @media (max-width: 1000px) {
    .modal-content-wrapper.with-confirmation {
      flex-direction: column;

      .detail-section {
        flex: none;
      }

      .confirmation-section {
        width: 100%;
        border-left: none;
        border-top: 1px solid var(--color-border-2);
        padding-left: 0;
        padding-top: 20px;
        max-height: 300px;
      }
    }
  }
</style>
