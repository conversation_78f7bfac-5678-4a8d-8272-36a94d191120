<template>
  <div>
    <a-card size="small" class="table-card">
      <order-type-radio
        ref="typeRadioRef"
        v-model="formModel.tab_type"
        size="medium"
        type="button"
        @change="handleSubmit()"
      />
    </a-card>
    <a-card size="small" class="table-card">
      <search-form-custom
        v-if="dataCacheStore.lineList?.length"
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        placeholder="请输入关键词"
        :search-rules="searchRules"
        :base-search-rules="baseSearchRules"
        @hand-submit="handleSubmit"
      ></search-form-custom>
      <div class="table-card-header mt-10">
        <div>
          <a-space v-if="rowSelection.selectedRowKeys.length">
            <span class="title-box">
              已选择
              <a-tag>
                {{ rowSelection.selectedRowKeys.length }}
              </a-tag>
              个订单
            </span>
            <icon-close-circle class="cur-por" @click="resetHandler" />
            <a-button
              :disabled="
                !rowSelection.selectedRows.filter(
                  (item:any) => item.order_type !== 'sub_order'
                ).length
              "
              type="outline"
              size="small"
              :loading="resending"
              @click="resend"
            >
              <template #icon>
                <icon-lark-color />
              </template>
              推送飞书消息卡
            </a-button>
            <!-- <a-button
              :disabled="!rowSelection.selectedRowKeys.length"
              :loading="mergeing"
              type="outline"
              size="small"
              @click="mergeAction"
            >
              <template #icon>
                <icon-branch />
              </template>
              合并订单
            </a-button> -->
            <!-- <a-button type="primary" @click="showOrderPriceFn()">
              <template #icon>
                <icon-plus />
              </template>
              新增订单
            </a-button> -->
          </a-space>
        </div>
        <a-space>
          <columns-select
            :the-field-map="allFieldsConfig"
            :need-format-column="false"
            :send-params="{ type: 'travel_order' }"
            :default-columns="columnsConfig.map((item:any) => item.dataIndex)"
            @select-columns-ok="setColumns"
          ></columns-select>
          <a-popover
            v-model:popup-visible="exportShow"
            class="export-popover"
            trigger="click"
            :content-style="{ padding: '0 4px' }"
          >
            <template #content>
              <div>
                <a-link @click="exportAction({})">合并订单</a-link>
                <a-divider :margin="5" direction="vertical" />
                <a-link @click="exportAction({ is_export_sub: true })">
                  子订单
                </a-link>
              </div>
            </template>
            <a-button type="primary" :loading="exporting">
              <template #icon>
                <icon-export />
              </template>
              导出
            </a-button>
          </a-popover>
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        v-model:summary-data="summaryData"
        :columns-config="columns"
        :data-config="getList"
        :send-params="tableParams"
        column-resizable
        :row-selection="rowSelection"
        :data-handle="dataHandle"
        :dpagination="pagination"
        :scroll-percent="scrollPercent"
        :auto-request="false"
        :sort-keys="sortKeys"
        size="small"
        @select-change="selectionChange"
        @column-resize="resizeAction"
      >
        <template #order_no="{ record }: TableColumnSlot">
          <!-- 汇总行显示 -->
          <div v-if="record.is_statistics" class="summary-cell">
            <span class="summary-text">{{ record.order_no || 0 }}个</span>
            <a-tooltip
              content="订单合并时，此处统计子订单的数量"
              position="top"
            >
              <icon-info-circle />
            </a-tooltip>
          </div>
          <!-- 普通行显示 -->
          <div v-else style="position: relative">
            {{ record.order_no }}
            <a-space :size="2" style="position: absolute; right: -14px">
              <a-tag
                v-if="record.order_type === 'sub_order'"
                color="arcoblue"
                size="small"
              >
                子
              </a-tag>
              <a-tag
                v-if="record.status === '已转线下'"
                color="arcoblue"
                size="small"
              >
                转
              </a-tag>
            </a-space>
          </div>
          <div>
            <a-tag
              v-if="record.status"
              :color="getOrderStatusColor(record.status)"
              size="small"
            >
              {{ record.status }}
            </a-tag>
          </div>
        </template>
        <!-- 媒体订单号 -->
        <template #media_order_no="{ record }: TableColumnSlot">
          <div v-if="record.media_order_no" class="media-order-no-cell">
            <a-tooltip :content="record.media_order_no" position="top">
              <div class="media-order-no-text">
                {{ record.media_order_no }}
              </div>
            </a-tooltip>
          </div>
          <span v-else>-</span>
        </template>

        <template #is_add_wechat="{ record }: TableColumnSlot">
          <span v-if="record.is_statistics">-</span>

          <dict-select
            v-else
            v-model="record.is_add_wechat"
            :data-list="yesOrNo"
            :allow-clear="false"
            style="width: 64px"
            :search="false"
            size="mini"
            placeholder=""
            @change="
              updateOrderInfo({
                id: record.id,
                is_add_wechat: record.is_add_wechat,
              })
            "
          />
        </template>

        <template #customer_intentionality="{ record }: TableColumnSlot">
          <span v-if="record.is_statistics">-</span>
          <dict-select
            v-else
            v-model="record.customer_intentionality"
            :data-list="orderIntentionalityM"
            style="width: 100%"
            size="mini"
            placeholder=""
            @change="
              updateOrderInfo({
                id: record.id,
                customer_intentionality: record.customer_intentionality,
              })
            "
          />
        </template>

        <template #is_call_connected="{ record }: TableColumnSlot">
          <span v-if="record.is_statistics">-</span>
          <dict-select
            v-else
            v-model="record.is_call_connected"
            :data-list="yesOrNo"
            :allow-clear="false"
            style="width: 64px"
            :search="false"
            size="mini"
            placeholder=""
            @change="
              updateOrderInfo({
                id: record.id,
                is_call_connected: record.is_call_connected,
              })
            "
          />
        </template>

        <template #remark="{ record }: TableColumnSlot">
          <template v-if="record.inquiry_order_id">
            <div class="editable-cell-wrapper">
              <table-cell-text
                :text="record.remark"
                :max-lines="2"
                :show-copy="true"
              />
              <a-popover
                v-model:popup-visible="record.showBtn"
                :default-popup-visible="false"
                trigger="click"
                class="edit-popover"
              >
                <template #content>
                  <a-input-group>
                    <a-textarea
                      v-model="record.remark_new"
                      style="width: 400px"
                      :auto-size="{ minRows: 2, maxRows: 6 }"
                      @focus="record.showBtn = true"
                      @blur="hideEditBtn(record)"
                    />
                    <a-button
                      v-if="record.showBtn"
                      type="primary"
                      @click="editOrder(record)"
                    >
                      <icon-check />
                    </a-button>
                  </a-input-group>
                </template>
                <a-button type="text" size="mini" class="edit-button">
                  <template #icon>
                    <icon-edit />
                  </template>
                </a-button>
              </a-popover>
            </div>
          </template>
          <span v-else>-</span>
        </template>

        <!-- 新备注 -->
        <template #new_remark="{ record }: TableColumnSlot">
          <span v-if="record.is_statistics">-</span>
          <table-inline-edit
            v-else
            v-model="record.new_remark"
            placeholder="请输入"
            :max-lines="2"
            :max-length="500"
            :show-copy="false"
            edit-mode="click"
            save-mode="manual"
            :on-save="(value) => saveNewRemark(record, value)"
          />
        </template>

        <template #refund_reason="{ record }: TableColumnSlot">
          <div class="editable-cell-wrapper">
            <table-cell-text
              :text="record.refund_reason"
              :max-lines="2"
              :show-copy="true"
            />
            <a-popover
              v-model:popup-visible="record.showRefundReasonBtn"
              trigger="click"
              class="edit-popover"
            >
              <template #content>
                <a-input-group>
                  <a-textarea
                    v-model="record.refund_reason_new"
                    style="width: 400px"
                    placeholder="请输入"
                    :auto-size="{ minRows: 2, maxRows: 6 }"
                    @focus="record.showRefundReasonBtn = true"
                    @blur="hideRefundReasonEditBtn(record)"
                  />
                  <a-button
                    v-if="record.showRefundReasonBtn"
                    type="primary"
                    @click="editRefundReasonOrder(record)"
                  >
                    <icon-check />
                  </a-button>
                </a-input-group>
              </template>
              <a-button type="text" size="mini" class="edit-button">
                <template #icon>
                  <icon-edit />
                </template>
              </a-button>
            </a-popover>
          </div>
        </template>

        <template #confirmation_link="{ record }: TableColumnSlot">
          <template v-if="record.confirmation_link">
            <a-link
              :href="record.confirmation_link"
              target="_blank"
              size="mini"
            >
              查看确认件
            </a-link>
          </template>
          <span v-else>-</span>
        </template>

        <template #popTxt="{ record, column }: TableColumnSlot">
          <table-cell-text
            :text="record[column.dataIndex as string]"
            :max-lines="2"
            :show-copy="true"
          />
        </template>

        <!-- 游客信息列模板 -->
        <template #fs_doc_travel_people="{ record }: TableColumnSlot">
          <div class="tourist-info-cell">
            <table-cell-text
              :text="record.fs_doc_travel_people"
              :max-lines="2"
              :show-copy="true"
            />
            <a-tooltip
              v-if="hasTouristInfoError(record.fs_doc_travel_people)"
              content="手机号/身份证格式可能有误，请检查"
              position="top"
            >
              <icon-exclamation-circle-fill class="error-tip-icon" />
            </a-tooltip>
          </div>
        </template>

        <!-- 新增：多行文本字段模板 -->
        <template #multiLineText="{ record, column }: TableColumnSlot">
          <table-cell-text
            :text="record[column.dataIndex as string]"
            :max-lines="2"
            :show-copy="true"
          />
        </template>

        <template #compensate_explain_image_1="{ record }: TableColumnSlot">
          <a-link
            v-if="record.compensate_explain_image_1"
            @click="
              previewImg([
                record.compensate_explain_image_1,
                record.compensate_explain_image_2,
              ])
            "
          >
            查看
          </a-link>
          <span v-else>-</span>
        </template>

        <template #service_other_detail="{ record }: TableColumnSlot">
          <a-link
            v-if="record.service_other_detail?.length"
            @click="showServiceOtherDetail(record)"
          >
            查看
          </a-link>
          <span v-else>-</span>
        </template>

        <template #contract_other_detail="{ record }: TableColumnSlot">
          <a-link
            v-if="record.contract_other_detail?.length"
            @click="showContractOtherDetail(record)"
          >
            查看
          </a-link>
          <span v-else>-</span>
        </template>

        <!-- 手机号 -->
        <template #phone="{ record }: TableColumnSlot">
          <div class="phone-cell">
            <span>{{ record.phone || '-' }}</span>
            <!-- 重复手机号警告提示 -->
            <a-tooltip
              v-if="record.is_exists_same_phone"
              position="top"
              :content="getDuplicatePhoneTooltip(record)"
            >
              <icon-exclamation-circle
                :style="{
                  color: '#ff7d00',
                  cursor: 'pointer',
                }"
                size="14"
              />
            </a-tooltip>
          </div>
        </template>

        <template #phone_follow_up_num="{ record, column }: TableColumnSlot">
          <!-- 汇总行显示 -->
          <div v-if="record.is_statistics" class="summary-cell">
            <span class="summary-text">{{
              record.phone_follow_up_total || 0
            }}</span>
          </div>
          <!-- 普通行显示 -->
          <template v-else>
            <a-link
              v-if="record[column.dataIndex as string]"
              @click="followLog?.show(record)"
            >
              <span>{{ record[column.dataIndex as string] }}</span>
            </a-link>
            <span v-else>0</span>
          </template>
        </template>

        <!-- 新增：订单状态列模板 -->
        <template #status="{ record }: TableColumnSlot">
          <!-- 汇总行显示 -->
          <div v-if="record.is_statistics" class="summary-cell">
            <a-tooltip
              :content="getStatusSummaryTooltip(record)"
              position="top"
            >
              <span class="summary-text"
                >{{ record.order_status_to_num.length || 0 }}种状态</span
              >
            </a-tooltip>
          </div>
          <!-- 普通行显示 -->
          <div v-else>
            <a-tag
              v-if="record.status"
              :color="getOrderStatusColor(record.status)"
              size="small"
            >
              {{ record.status }}
            </a-tag>
            <span v-else>-</span>
          </div>
        </template>

        <!-- 新增：地接社列模板 -->
        <template #local_travel_agency="{ record }: TableColumnSlot">
          <!-- 汇总行显示 -->
          <div v-if="record.is_statistics" class="summary-cell">
            <a-tooltip
              :content="getAgencySummaryTooltip(record)"
              position="top"
            >
              <span class="summary-text"
                >{{ record.local_travel_agency.length || 0 }}个地接社</span
              >
            </a-tooltip>
          </div>
          <!-- 普通行显示 -->
          <span v-else>{{ record.local_travel_agency || '-' }}</span>
        </template>

        <!-- 新增：出行人数列模板 -->
        <template #adult_num="{ record }: TableColumnSlot">
          <!-- 汇总行显示 -->
          <div v-if="record.is_statistics" class="summary-cell">
            <span class="summary-text">
              {{ record.adult_num || 0 }}大{{ record.children_num || 0 }}小
            </span>
          </div>
          <!-- 普通行显示 -->
          <span v-else>
            {{
              record.adult_num
                ? `${record.adult_num || 0}大 ${record.children_num || 0}小`
                : '-'
            }}
          </span>
        </template>

        <!-- 新增：跟踪次数列模板 -->
        <template #tracking_num="{ record }: TableColumnSlot">
          <!-- 汇总行显示 -->
          <!-- <div v-if="record.is_statistics" class="summary-cell">
            <span class="summary-text">{{ record.tracking_total || 0 }}</span>
          </div> -->
          <!-- 普通行显示 -->
          <span>{{ record.tracking_num || 0 }}</span>
        </template>

        <!-- 新增：总团款列模板 -->
        <template #total_cut_price="{ record }: TableColumnSlot">
          <!-- 汇总行显示 -->
          <div v-if="record.is_statistics" class="summary-cell">
            <span class="summary-text">{{
              formatMoney(record.total_cut_price)
            }}</span>
          </div>
          <!-- 普通行显示 -->
          <span v-else>{{
            formatMoney(record.total_cut_price || record.advance_price)
          }}</span>
        </template>

        <!-- 新增：订金列模板 -->
        <template #advance_price="{ record }: TableColumnSlot">
          <!-- 汇总行显示 -->
          <div v-if="record.is_statistics" class="summary-cell">
            <span class="summary-text">{{
              formatMoney(record.advance_price)
            }}</span>
          </div>
          <!-- 普通行显示 -->
          <span v-else>{{ formatMoney(record.advance_price) }}</span>
        </template>

        <!-- 新增：结算价列模板 -->
        <template #total_final_price="{ record }: TableColumnSlot">
          <!-- 汇总行显示 -->
          <div v-if="record.is_statistics" class="summary-cell">
            <span class="summary-text">{{
              formatMoney(record.total_final_price)
            }}</span>
          </div>
          <!-- 普通行显示 -->
          <span v-else>{{ formatMoney(record.total_final_price) }}</span>
        </template>

        <!-- 新增：利润列模板 -->
        <template #profit="{ record }: TableColumnSlot">
          <!-- 汇总行显示 -->
          <div v-if="record.is_statistics" class="summary-cell">
            <span class="summary-text">{{ formatMoney(record.profit) }}</span>
          </div>
          <!-- 普通行显示 -->
          <span v-else>{{ formatMoney(record.profit) }}</span>
        </template>

        <!-- 新增：地接代收款列模板 -->
        <template #tripartite_need_charge_amount="{ record }: TableColumnSlot">
          <!-- 汇总行显示 -->
          <div v-if="record.is_statistics" class="summary-cell">
            <span class="summary-text">{{
              formatMoney(record.tripartite_need_charge_amount)
            }}</span>
          </div>
          <!-- 普通行显示 -->
          <span v-else>{{
            formatMoney(record.tripartite_need_charge_amount)
          }}</span>
        </template>

        <!-- 新增：门市价列模板 -->
        <template #total_price="{ record }: TableColumnSlot">
          <!-- 汇总行显示 -->
          <div v-if="record.is_statistics" class="summary-cell">
            <span class="summary-text">{{
              formatMoney(record.total_price_sum)
            }}</span>
          </div>
          <!-- 普通行显示 -->
          <span v-else>{{ formatMoney(record.total_price) }}</span>
        </template>

        <!-- 新增：商品原价列模板 -->
        <template #goods_price="{ record }: TableColumnSlot">
          <!-- 汇总行显示 -->
          <div v-if="record.is_statistics" class="summary-cell">
            <span class="summary-text">{{
              formatMoney(record.goods_price_sum)
            }}</span>
          </div>
          <!-- 普通行显示 -->
          <span v-else>{{ formatMoney(record.goods_price) }}</span>
        </template>

        <!-- 新增：预估服务费列模板 -->
        <template #estimated_service_price="{ record }: TableColumnSlot">
          <!-- 汇总行显示 -->
          <div v-if="record.is_statistics" class="summary-cell">
            <span class="summary-text">{{
              formatMoney(record.estimated_service_price)
            }}</span>
          </div>
          <!-- 普通行显示 -->
          <span v-else>{{ formatMoney(record.estimated_service_price) }}</span>
        </template>

        <!-- 新增：机票支出列模板 -->
        <template #air_ticket_expend="{ record }: TableColumnSlot">
          <!-- 汇总行显示 -->
          <div v-if="record.is_statistics" class="summary-cell">
            <span class="summary-text">{{
              formatMoney(record.air_ticket_expend_sum)
            }}</span>
          </div>
          <!-- 普通行显示 -->
          <span v-else>{{ formatMoney(record.air_ticket_expend) }}</span>
        </template>

        <!-- 新增：赔付金额列模板 -->
        <template #compensate_amount="{ record }: TableColumnSlot">
          <!-- 汇总行显示 -->
          <div v-if="record.is_statistics" class="summary-cell">
            <span class="summary-text">{{
              formatMoney(record.compensate_amount_sum)
            }}</span>
          </div>
          <!-- 普通行显示 -->
          <span v-else>{{ formatMoney(record.compensate_amount) }}</span>
        </template>

        <template #is_flow_text="{ record }: TableColumnSlot">
          <span v-if="record.is_statistics">-</span>
          <span v-else>
            {{ record.is_flow_text || '-' }}
          </span>
        </template>

        <template #action="{ record }: TableColumnSlot">
          <span v-if="record.is_statistics">-</span>
          <a-spin v-else :loading="record.loading">
            <div class="action-buttons-container">
              <!-- 响应式按钮布局 -->
              <div class="action-buttons-wrapper">
                <!-- 主要操作按钮（最多显示2个直接按钮） -->
                <template
                  v-for="action in getDirectActions(record)"
                  :key="action.key"
                >
                  <a-button
                    :type="action.type || 'text'"
                    :status="action.status"
                    size="mini"
                    class="action-btn"
                    @click="action.handler"
                  >
                    <template v-if="action.icon" #icon>
                      <component :is="action.icon" />
                    </template>
                    {{ getButtonLabel(action, record) }}
                  </a-button>
                </template>

                <!-- 更多操作下拉菜单（当操作超过2个时显示） -->
                <a-dropdown
                  v-if="shouldShowMoreButton(record)"
                  v-model:popup-visible="record.dropdownVisible"
                  trigger="click"
                  position="bl"
                  :popup-max-height="300"
                >
                  <a-button type="text" class="action-btn more-btn">
                    <template #icon>
                      <icon-down />
                    </template>
                    更多
                  </a-button>
                  <template #content>
                    <template
                      v-for="action in getDropdownActions(record)"
                      :key="action.key"
                    >
                      <!-- 普通操作 -->
                      <a-doption
                        v-if="!action.isPopconfirm"
                        :class="action.className"
                        @click="handleDropdownAction(action, record)"
                      >
                        <template v-if="action.icon" #icon>
                          <component :is="action.icon" />
                        </template>
                        {{ action.label }}
                      </a-doption>

                      <!-- 需要确认的操作 -->
                      <a-popconfirm
                        v-else
                        :content="
                          action.confirmContent || `确定要${action.label}吗？`
                        "
                        :ok-text="action.okText || '确定'"
                        cancel-text="取消"
                        position="left"
                        @ok="handleConfirmAction(action, record)"
                        @cancel="handleCancelAction(record)"
                      >
                        <a-doption :class="action.className">
                          <template v-if="action.icon" #icon>
                            <component :is="action.icon" />
                          </template>
                          {{ action.label }}
                        </a-doption>
                      </a-popconfirm>
                    </template>
                  </template>
                </a-dropdown>
              </div>
            </div>
          </a-spin>
        </template>
      </base-table>
    </a-card>
    <travel-price-edit ref="priceRef" @save="refreshData()" />

    <travel-order-send ref="sendRef" @save="refreshData()" />

    <travel-order-detail ref="detailRef" @save="refreshData()" />

    <travel-order-cancel ref="cancelRef" @save="refreshData()" />

    <travel-order-invalid ref="invalidRef" @save="refreshData()" />

    <travel-order-transfer ref="transferRef" @save="refreshData()" />

    <travel-order-detainment ref="detainmentRef" @save="refreshData()" />

    <travel-order-asign ref="asignRef" @save="refreshData()" />

    <travel-order-follow-log ref="followLog" />

    <table-modal ref="tableModalRef" />
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';

  import { ref, reactive, computed, h } from 'vue';
  import { hasTouristInfoError } from '@/utils/tourist-info-validator';
  import request from '@/api/request';
  import { moneyFormatShow } from '@/utils/table-utils/columns-config';
  import {
    yesOrNo,
    orderTypeM,
    orderIntentionalityM,
    orderStatusListM,
  } from '@/components/dict-select/dict-travel';
  import { getColor } from '@/components/dict-select/dict-util';
  import { TableColumnSlot } from '@/global';
  import { Message, Modal } from '@arco-design/web-vue';
  import { useRouter } from 'vue-router';
  import TravelPriceEdit from '@/views/travel-order/travel-order-list/travel-price-edit.vue';
  import { useClipboard } from '@vueuse/core';
  import TravelOrderSend from '@/views/travel-order/travel-order-list/travel-order-send.vue';
  import ColumnsSelect from '@/components/columns-select/columns-select.vue';
  import { cloneDeep, debounce } from 'lodash';
  import { useDataCacheStore, useUserStore } from '@/store';
  import TravelOrderDetail from '@/views/travel-order/travel-order-list/travel-order-detail.vue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import TravelOrderCancel from '@/views/travel-order/travel-order-list/travel-order-cancel.vue';
  import TravelOrderDetainment from '@/views/travel-order/travel-order-list/travel-order-detainment.vue';
  import TravelOrderAsign from '@/views/travel-order/travel-order-list/travel-order-asign.vue';
  import TravelOrderFollowLog from '@/views/travel-order/travel-order-list/travel-order-follow-log.vue';
  import dictSelect from '@/components/dict-select/dict-select.vue';
  import TravelOrderInvalid from '@/views/travel-order/travel-order-list/travel-order-invalid.vue';
  import TableInlineEdit from '@/components/table-inline-edit/TableInlineEdit.vue';
  import { previewImg } from '@/utils/util';
  import TableModal from '@/components/table-modal/table-modal.vue';
  import TableCellText from '@/components/table-cell-text/table-cell-text.vue';
  import OrderTypeRadio from '@/components/order-type-radio/order-type-radio.vue';
  import {
    allFieldsConfig,
    searchRules,
  } from '@/views/travel-order/travel-order-list/travel-order-config';
  import TravelOrderTransfer from '@/views/travel-order/travel-order-list/travel-order-transfer.vue';

  const userStore = useUserStore();

  const typeRadioRef = ref();
  const refreshTypeRadio = () => {
    typeRadioRef.value?.refresh();
  };

  const generateFormModel = () => {
    return {
      order_create_time: [],
      order_no: null,
      customer_name: null,
      service_user_fs_id: null,
      department_ids: [],
      status: null,
      phoneKeyword: null as null | string,
      real_travel_start_date: [],
      is_add_wechat: null,
      is_call_connected: null,
      goods_id: null,
      time_add_wechat: null,
      time_order_confirmed: null,
      time_send_confirmation: null,
      time_travel: null,
      time_travel_back: null,
      time_cancel: null,
      time_settle: null,
      refund_time: null,
      time_accept: null,
      time_distribute: null,
      media_order_no: null,
      sale_channel: null,
      personage: null,
      redistribution: null,
      area: null,
      local_travel_agency: null,
      source: null,
      is_refund_follow_up_again: null,
      customer_intentionality: null,
      thread_source: [],
      source_detail: [],
      order_source: [],
      travel_line: [],
      payment_type: [],
      anchor_id: [],
      confirm_doc_audit_user_id: [],
      tab_type: '',
      is_exchange_user: null,

      order_new_type_custom: [], // "订单类型",
      // order_new_type: '', // "订单类型",
      // order_new_type_detail: '', // "订单细分类型",
      order_new_type_origin_customer: '', // "原客户筛选",
      keywords: null,
      pay_type: null,
      fs_doc_travel_people: null,
      is_flow: null,
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const followLog = ref();
  const tableModalRef = ref();
  // const summary = ref([]);
  const formModel = reactive(generateFormModel());
  const dataCacheStore = useDataCacheStore();

  let cancelToken: AbortController | null = null;
  const getList = async (data: any) => {
    cancelToken?.abort('重复请求取消');
    cancelToken = new AbortController();
    let order_new_type_search: any = {};
    formModel.order_new_type_custom.forEach((item: any) => {
      // 拆分订单类型和细分类型
      const [type, typeDetail] = item.split('___');
      if (!typeDetail) {
        order_new_type_search[type] = [];
      } else if (!order_new_type_search[type]) {
        order_new_type_search[type] = [typeDetail];
      } else if (
        order_new_type_search[type] &&
        order_new_type_search[type].length
      ) {
        order_new_type_search[type].push(typeDetail);
      }
    });
    return request(
      '/api/travel/finalOrderList',
      {
        ...data,
        order_new_type_search,
      },
      cancelToken.signal
    );
  };
  const updateOrderInfo = async (data: any) => {
    return request('/api/travel/finalOrderSave', {
      ...data,
    });
  };

  const pagination = {
    showTotal: false,
    // show_total_key: 'sub_order_num',
    show_total_key: 'total',
    pageSize: 20,
  };

  function dataHandle(list: any[]) {
    return list.map((item) => ({
      ...item,
      disabled: item.order_type === 'sub_order',
      showBtn: false,
      remark_new: item.remark,
      showRefundReasonBtn: false,
      refund_reason_new: item.refund_reason,
      children: item.children?.length ? item.children : null,
      dropdownVisible: false, // 新增：下拉菜单显示状态
      _actionButtonsComputed: false, // 新增：标记是否已计算按钮布局
    }));
  }

  const cancelRef = ref();
  const invalidRef = ref();
  const transferRef = ref();
  const detainmentRef = ref();
  const asignRef = ref();
  const priceRef = ref();
  const sendRef = ref();
  const detailRef = ref();

  function showOrderPriceFn() {
    priceRef.value?.show();
  }

  const baseSearchRules: any = ref([
    {
      field: 'keywords',
      label: '关键词',
      value: null,
      width: '200px',
    },
  ]);

  const columnsConfig = ref(
    cloneDeep(
      allFieldsConfig.reduce((sum: any[], item) => {
        // @ts-ignore
        sum.push(...item.dataList.filter((citem: any) => !citem.defaultHide));
        return sum;
      }, [])
    )
  );
  const columns = computed(() => [
    {
      title: '订单编号',
      dataIndex: 'order_no',
      fixed: 'left',
      align: 'center',
      width: 140,
    },
    ...columnsConfig.value.map((item) => ({
      ...item,
      title:
        item.dataIndex === 'service_user' && formModel.tab_type === 5
          ? '原客服'
          : item.title,
    })),
    {
      title: '操作',
      dataIndex: 'action',
      width: 238, // 增加操作列宽度以适应更多按钮
      align: 'center',
      fixed: 'right',
    },
  ]);

  function showServiceOtherDetail(record: any) {
    tableModalRef.value?.show({
      title: '客服其他收入和支出',
      list: [
        {
          name: '汇总',
          income: record.service_other_income_total,
          expend: record.service_other_expend_total,
        },
        ...record.service_other_detail,
      ],
      columns: [
        {
          title: '内容',
          dataIndex: 'name',
        },
        {
          title: '收入',
          dataIndex: 'income',
          render: moneyFormatShow(),
        },
        {
          title: '支出',
          dataIndex: 'expend',
          render: moneyFormatShow(),
        },
      ],
    });
  }
  function showContractOtherDetail(record: any) {
    tableModalRef.value?.show({
      title: '合同其他收入和支出',
      list: [
        {
          name: '汇总',
          income: record.contract_other_income_total,
          expend: record.contract_other_expend_total,
        },
        ...record.contract_other_detail,
      ],
      columns: [
        {
          title: '内容',
          dataIndex: 'name',
        },
        {
          title: '收入',
          dataIndex: 'income',
          render: moneyFormatShow(),
        },
        {
          title: '支出',
          dataIndex: 'expend',
          render: moneyFormatShow(),
        },
      ],
    });
  }

  const sortKeys = ['real_travel_start_date'];

  const scrollPercent = computed(() => ({
    maxHeight: '70vh',
    x: columns.value.length * 165,
  }));

  // table渲染完成回调
  const changeHandler = (tableData: any) => {
    loading.value = false;
  };

  // 数据选择相关
  let rowSelection = reactive({
    type: 'checkbox',
    fixed: true,
    width: 30,
    onlyCurrent: true,
    showCheckedAll: true,
    selectedRowKeys: [] as any[],
    selectedRows: [],
  });

  const selectionChange = (selectedRowKeys: any, selectedRows: any) => {
    rowSelection.selectedRowKeys = selectedRowKeys.filter((item: any) => item);
    rowSelection.selectedRows = selectedRows;
  };

  // 汇总行数据
  const emptyFlag = ref(true);
  const summaryData: any = ref({
    cost: '',
  });
  const summary = () => {
    console.log(summaryData, '我看到了');
    return [
      {
        ...summaryData.value,
        media_name: '汇总',
      },
    ];
  };

  let resizeAction = (dataIndex: any, width: any) => {
    let colItem: any = null;
    allFieldsConfig.forEach((item) => {
      item.dataList.some((citem: any) => {
        if (citem.dataIndex === dataIndex) {
          colItem = citem;
          return true;
        }
        return false;
      });
    });
    if (colItem) {
      colItem.width = width;
    }
  };
  resizeAction = debounce(resizeAction, 300);

  const router = useRouter();
  function toMerge() {
    router.push('travel-order-merge');
  }

  // 重置
  const resetHandler = () => {
    rowSelection.selectedRowKeys = [];
    rowSelection.selectedRows = [];
  };

  const resending = ref(false);
  function resend() {
    resending.value = true;
    request('/api/travel/pushFeishuCard', {
      ids: rowSelection.selectedRows
        .filter((item: any) => item.order_type !== 'sub_order')
        .map((item: any) => item.id),
    })
      .then(() => {
        Message.success('操作成功');
      })
      .finally(() => {
        resending.value = false;
      });
  }

  async function copyLink(record: any) {
    const { copy } = useClipboard();
    await copy(
      `${window.location.origin}/ensureLinkRedirect?order_no=${record.order_no}`
    );
    Message.success('复制成功');
  }
  function updateLink(record: any) {
    loading.value = true;
    request('/api/travel/updateEnsureDoc', {
      order_no: record.order_no,
    })
      .then((res) => {
        record.confirmation_link = res.data.confirmation_link;
        Message.success('更新成功');
      })
      .finally(() => {
        loading.value = false;
      });
  }

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    delete resData.tab_type;
    Object.assign(formModel, resData);
    theTable.value?.search();
    refreshTypeRadio();
  };

  function setColumns(val: any[]) {
    columnsConfig.value = val.filter((item) =>
      allFieldsConfig.some((citem) => citem.keys.includes(item.dataIndex))
    );
    handleSubmit();
  }

  // 点击搜索时 处理逻辑
  const refreshData = () => {
    loading.value = true;
    theTable.value?.fetchData();
  };

  // 电话跟进
  let followAction = (record: any) => {
    request('/api/travel/phoneFollowUp', {
      order_no: record.order_no,
    }).then(() => {
      refreshData();
      Message.success('操作成功');
    });
  };
  followAction = debounce(followAction, 500);

  // 订单受理
  function acceptOrderAction(record: any) {
    loading.value = true;
    request('/api/travel/acceptOrder', {
      order_no: record.order_no,
    })
      .then((res) => {
        Message.success('订单已受理');
        refreshData();
      })
      .catch((err) => {
        loading.value = false;
      });
  }
  // 订单已完成
  function ensureOrderAction(record: any) {
    loading.value = true;
    request('/api/travel/confirmOrder', {
      order_no: record.order_no,
    })
      .then((res) => {
        Message.success('操作成功');
        refreshData();
      })
      .catch((err) => {
        loading.value = false;
        // 如果报错，则打开编辑页面
        priceRef.value?.show(record);
      });
  }

  // 新增：处理受理订单（使用 Modal.confirm）
  function handleAcceptOrder(record: any) {
    Modal.confirm({
      title: '确认要受理此订单吗？',
      content: () =>
        h(
          'div',
          {
            style: {
              textAlign: 'left',
              lineHeight: '1.6',
            },
          },
          [
            h('div', { style: { marginBottom: '5px' } }, [
              h('strong', '订单编号：'),
              record.order_no,
            ]),
            h('div', { style: { marginBottom: '5px' } }, [
              h('strong', '客户昵称：'),
              record.customer_name || '-',
            ]),
            h('div', { style: { marginBottom: '5px' } }, [
              h('strong', '订单状态：'),
              record.status || '-',
            ]),
            h('div', { style: { marginBottom: '0px' } }, [
              h('strong', '产品名称：'),
              record.travel_line || '-',
            ]),
          ]
        ),
      okText: '确定受理',
      cancelText: '取消',
      width: 380,
      onOk: () => {
        acceptOrderAction(record);
      },
    });
  }

  // 新增：处理确认订单（使用 Modal.confirm）
  function handleConfirmOrder(record: any) {
    Modal.confirm({
      title: '确认将此订单提交给计调审核吗？',
      content: () =>
        h(
          'div',
          {
            style: {
              textAlign: 'left',
              lineHeight: '1.6',
            },
          },
          [
            h('div', { style: { marginBottom: '5px' } }, [
              h('strong', '订单编号：'),
              record.order_no,
            ]),
            h('div', { style: { marginBottom: '5px' } }, [
              h('strong', '客户昵称：'),
              record.customer_name || '-',
            ]),
            h('div', { style: { marginBottom: '5px' } }, [
              h('strong', '订单状态：'),
              record.status || '-',
            ]),
            h('div', { style: { marginBottom: '0px' } }, [
              h('strong', '产品名称：'),
              record.travel_line || '-',
            ]),
          ]
        ),
      okText: '确认提交',
      cancelText: '取消',
      width: 380,
      onOk: () => {
        ensureOrderAction(record);
      },
    });
  }

  // 新增：处理退款有效客户（使用 Modal.confirm）
  function handleRefundValidCustomer(record: any) {
    Modal.confirm({
      title: '标记退款有效客户',
      content: () =>
        h(
          'div',
          {
            style: {
              textAlign: 'left',
              lineHeight: '1.6',
            },
          },
          [
            h(
              'div',
              {
                style: {
                  marginBottom: '16px',
                  fontSize: '14px',
                  color: '#666',
                },
              },
              '订单信息'
            ),
            h('div', { style: { marginBottom: '8px' } }, [
              h('strong', '订单编号：'),
              record.order_no,
            ]),
            h('div', { style: { marginBottom: '8px' } }, [
              h('strong', '客户昵称：'),
              record.customer_name || '-',
            ]),
            h('div', { style: { marginBottom: '8px' } }, [
              h('strong', '订单状态：'),
              record.status || '-',
            ]),
            h('div', { style: { marginBottom: '8px' } }, [
              h('strong', '联系电话：'),
              record.customer_phone || '-',
            ]),
            h('div', { style: { marginBottom: '16px' } }, [
              h('strong', '产品名称：'),
              record.travel_line || '-',
            ]),
            h(
              'div',
              { style: { fontSize: '16px', color: '#1890ff' } },
              '确定标记为退款有效客户吗？'
            ),
            h(
              'div',
              { style: { fontSize: '12px', color: '#999', marginTop: '8px' } },
              '标记后该客户将被认定为有效退款客户'
            ),
          ]
        ),
      okText: '确定标记',
      cancelText: '取消',
      width: 380,
      onOk: () => {
        loading.value = true;
        updateOrderInfo({
          id: record.id,
          is_refund_follow_up_again: '是',
        })
          .then(() => {
            Message.success('操作成功');
            refreshData();
          })
          .finally(() => {
            loading.value = false;
          });
      },
    });
  }

  // 终止出行
  let stopTravel = (record: any) => {
    request('/api/travel/orderSuspendTravel', {
      order_no: record.order_no,
    }).then(() => {
      refreshData();
      Message.success('操作成功');
      Modal.confirm({
        title: '操作成功',
        content: '是否变更询价单（修改总团款或者结算价）？',
        okText: '是',
        cancelText: '否',
        onOk: () => {
          priceRef.value?.show({ order_no: record.order_no });
        },
      });
    });
  };
  // 取消预约
  let cancelTravel = (record: any) => {
    request('/api/travel/orderCancel', {
      id: record.id,
      order_no: record.order_no,
    }).then(() => {
      refreshData();
      Message.success('操作成功');
    });
  };

  // 确认件相关操作的确认弹窗处理方法
  function handleCancelTravelConfirm(record: any) {
    Modal.confirm({
      title: '取消预约确认',
      content: `确定对订单【${record.order_no}】进行取消预约操作吗?`,
      onOk: () => {
        cancelTravel(record);
      },
    });
  }

  function handleStopTravelConfirm(record: any) {
    Modal.confirm({
      title: '终止出行确认',
      content: `确定对订单【${record.order_no}】终止出行吗?`,
      onOk: () => {
        stopTravel(record);
      },
    });
  }

  function handleUpdateLinkConfirm(record: any) {
    // Modal.confirm({
    //   title: '生成确认件确认',
    //   content: '确定生成确认件吗?',
    //   onOk: () => {
    //     updateLink(record);
    //   },
    // });
    sendRef.value?.show(record);
  }

  const mergeing = ref(false);
  function mergeAction() {
    resending.value = true;
    request('/api/travel/orderMerge', {
      sub_order_no: rowSelection.selectedRows
        .map((item: any) => item.order_no)
        .join('\n'),
    })
      .then(() => {
        Message.success('操作成功');
        refreshData();
      })
      .finally(() => {
        resending.value = false;
      });
  }

  const tableParams = computed(() => {
    let phones: any[] = [];
    if (formModel.phoneKeyword) {
      formModel.phoneKeyword?.split(' ').forEach((item) => {
        item
          .trim()
          .split('，')
          .forEach((citem) => {
            citem
              .trim()
              .split(',')
              .forEach((ccitem) => {
                if (ccitem) {
                  phones.push(ccitem);
                }
              });
          });
      });
    }
    return {
      ...formModel,
      real_travel_start_date_begin: formModel.real_travel_start_date?.[0],
      real_travel_start_date_end: formModel.real_travel_start_date?.[1],
      phones,
    };
  });

  function hideEditBtn(record: any) {
    setTimeout(() => {
      record.showBtn = false;
      record.remark_new = record.remark;
    }, 300);
  }
  function editOrder(record: any) {
    setTimeout(() => {
      record.showBtn = false;
      record.remark = record.remark_new;
    }, 100);
    request('/api/travel/finalOrderSave', {
      id: record.id,
      remark: record.remark_new,
    });
  }

  // 退款说明
  function hideRefundReasonEditBtn(record: any) {
    setTimeout(() => {
      record.showRefundReasonBtn = false;
      record.refund_reason_new = record.refund_reason;
    }, 300);
  }
  function editRefundReasonOrder(record: any) {
    record.showRefundReasonBtn = false;
    record.refund_reason = record.refund_reason_new;
    request('/api/travel/finalOrderSave', {
      id: record.id,
      refund_reason: record.refund_reason_new,
    });
  }

  // 新备注保存方法
  const saveNewRemark = async (record: any, value: string): Promise<void> => {
    try {
      await updateOrderInfo({
        id: record.id,
        new_remark: value,
      });
      // 更新记录中的值
      record.new_remark = value;
    } catch (error) {
      console.error('备注保存失败:', error);
      throw new Error('备注保存失败');
    }
  };

  // 生成重复手机号的tooltip内容
  const getDuplicatePhoneTooltip = (record: any): string => {
    if (!record.is_exists_same_phone) {
      return '';
    }

    // 尝试从不同可能的字段获取重复订单列表
    const duplicateOrders = record.same_phone_order_nos || [];

    if (duplicateOrders && duplicateOrders.length > 0) {
      // 如果有具体的订单列表
      const orderList = Array.isArray(duplicateOrders)
        ? duplicateOrders.join(', ')
        : duplicateOrders;
      return `该手机号在以下订单中重复使用：${orderList}`;
    }
    // 如果没有具体订单列表，显示通用提示
    return `该手机号在其他订单中重复使用`;
  };

  const resetSearch = () => {
    Object.assign(formModel, generateFormModel());
    handleSubmit();
  };

  const exporting = ref(false);
  const exportShow = ref(false);
  async function exportAction(data: any) {
    exporting.value = true;
    exportShow.value = false;
    theTable.value?.exportTable(data).finally(() => {
      exporting.value = false;
    });
  }

  // 判断是否显示编辑按钮
  const shouldShowEditButton = (record: any) => {
    return (
      ([
        '已支付订金',
        '已受理',
        '待计调审核',
        '已发确认件',
        '已出行',
        '已核销',
      ].includes(record.status) &&
        userStore.hasPermission(14)) ||
      (['已支付订金', '已受理', '待计调审核'].includes(record.status) &&
        userStore.hasPermission(3))
    );
  };

  // 获取所有可用的操作按钮
  const getAllActions = (record: any) => {
    const actions = [];

    // 1. 编辑（优先级最高）
    if (shouldShowEditButton(record)) {
      actions.push({
        key: 'edit',
        label: '编辑',
        type: 'text',
        status: 'warning',
        priority: 1,
        icon: 'icon-edit',
        handler: () =>
          priceRef.value?.show(
            { order_no: record.order_no },
            {
              showConfirm: record.status === '已支付订金',
            }
          ),
      });
    }

    // 2. 受理（优先级第二）
    if (
      (record.status === '已支付订金' && userStore.hasPermission(3)) ||
      (record.status === '已退款' && !record.time_accept)
    ) {
      actions.push({
        key: 'accept',
        label: '受理',
        type: 'text',
        icon: 'icon-check',
        priority: 2,
        handler: () => handleAcceptOrder(record),
      });
    }

    // 3. 确认订单（优先级第三）
    if (record.status === '已受理' && userStore.hasPermission(3)) {
      actions.push({
        key: 'confirm-order',
        label: '提交审核',
        type: 'text',
        icon: 'icon-stamp',
        priority: 3,
        handler: () => handleConfirmOrder(record),
      });
    }

    // 4. 审核通过（优先级第四）
    if (record.status === '待计调审核' && userStore.hasPermission(14)) {
      actions.push({
        key: 'approve',
        label: '审核通过',
        type: 'text',
        icon: 'icon-check',
        priority: 4,
        handler: () => sendRef.value?.show(record),
      });
    }

    // 5. 详情（优先级第五，常用操作）
    actions.push({
      key: 'detail',
      label: '详情',
      type: 'text',
      icon: 'icon-eye',
      priority: 5,
      handler: () => detailRef.value?.show(record),
    });

    // 6. 分配
    if (
      record.order_type !== 'sub_order' &&
      record.detainment_order_flag === 1
    ) {
      actions.push({
        key: 'assign',
        label: '分配',
        type: 'text',
        icon: 'icon-user',
        priority: 6,
        handler: () => asignRef.value?.show(record),
      });
    }

    // 7. 转线下
    if (record.status === '已退款') {
      actions.push({
        key: 'transfer-offline',
        label: '转线下',
        type: 'text',
        icon: 'icon-swap',
        priority: 7,
        handler: () =>
          priceRef.value?.show(
            {
              order_no: record.order_no,
            },
            {
              transfer_offline: true,
            }
          ),
      });
    }

    // 8. 退款有效客户
    if (record.status === '已退款' && !record.is_refund_follow_up_again) {
      actions.push({
        key: 'refund-valid-customer',
        label: '退款有效客户',
        type: 'text',
        icon: 'icon-user-add',
        priority: 8,
        handler: () => handleRefundValidCustomer(record),
      });
    }

    // 9. 取消预约
    if (record.status === '已发确认件' && userStore.hasPermission(14)) {
      actions.push({
        key: 'cancel-travel',
        label: '取消预约',
        type: 'text',
        icon: 'icon-close',
        priority: 9,
        handler: () => handleCancelTravelConfirm(record),
      });
    }

    // 10. 终止出行
    if (record.status === '已出行' && userStore.hasPermission(14)) {
      actions.push({
        key: 'stop-travel',
        label: '终止出行',
        type: 'text',
        icon: 'icon-stop',
        priority: 10,
        handler: () => handleStopTravelConfirm(record),
      });
    }

    // 11. 订单已结算
    if (record.status === '已核销' && userStore.hasPermission(14)) {
      actions.push({
        key: 'order-settle',
        label: '订单已结算',
        type: 'text',
        icon: 'icon-check-square',
        priority: 11,
        handler: () =>
          priceRef.value?.show(
            {
              order_no: record.order_no,
            },
            {
              order_settle: true,
            }
          ),
      });
    }

    // 12. 电话跟进
    if (!userStore.hasRolePermission(14)) {
      actions.push({
        key: 'follow',
        label: '电话跟进',
        type: 'text',
        icon: 'icon-phone',
        priority: 12,
        handler: () => followAction(record),
      });
    }

    // 13. 流转至挽单池
    if (record.order_type !== 'sub_order' && !userStore.hasRolePermission(14)) {
      actions.push({
        key: 'detainment',
        label: '流转至挽单池',
        type: 'text',
        icon: 'icon-loop',
        priority: 13,
        handler: () => detainmentRef.value?.show(record),
      });
    }

    // 14. 作废（危险操作）
    if (record.order_type !== 'sub_order') {
      actions.push({
        key: 'invalid',
        label: '作废',
        type: 'text',
        icon: 'icon-close',
        priority: 14,
        className: 'danger-option',
        handler: () => invalidRef.value?.show(record),
      });
    }

    // 15. 转给其它客服（危险操作）
    actions.push({
      key: 'transfer',
      label: '转给其它客服',
      type: 'text',
      icon: 'icon-reply',
      priority: 15,
      className: 'danger-option',
      handler: () => transferRef.value?.show(record),
    });

    // 按优先级排序
    return actions.sort((a, b) => a.priority - b.priority);
  };

  // 计算按钮文字的实际显示宽度（考虑中英文字符差异）
  const calculateTextWidth = (text: string) => {
    let width = 0;
    for (let i = 0; i < text.length; i += 1) {
      const char = text.charAt(i);
      // 中文字符宽度约为英文字符的2倍
      if (/[\u4e00-\u9fa5]/.test(char)) {
        width += 2;
      } else {
        width += 1;
      }
    }
    return width;
  };

  // 检测是否需要紧凑模式（根据按钮文字长度和数量）
  const isCompactMode = (record: any) => {
    const allActions = getAllActions(record);
    if (allActions.length === 0) return false;

    // 计算前2个按钮的总宽度
    const directActions = allActions.slice(0, 2);
    const totalWidth = directActions.reduce((sum, action) => {
      return sum + calculateTextWidth(action.label);
    }, 0);

    // 考虑"更多"按钮的宽度（如果需要显示的话）
    const moreButtonWidth =
      allActions.length > 2 ? calculateTextWidth('更多') : 0;
    const totalWidthWithMore = totalWidth + moreButtonWidth;

    // 根据操作列宽度(160px)和按钮padding等计算可用空间
    // 每个字符大约占用6-8px，加上按钮padding和间距
    const availableWidth = 160 - 20; // 减去容器padding
    const estimatedButtonWidth =
      totalWidthWithMore * 6 +
      directActions.length * 16 +
      (allActions.length > 2 ? 16 : 0);

    return estimatedButtonWidth > availableWidth || allActions.length > 4;
  };

  // 获取按钮显示文字（紧凑模式下可能缩短）
  const getButtonLabel = (action: any, record: any) => {
    if (!isCompactMode(record)) {
      return action.label;
    }

    // 紧凑模式下的文字缩短规则
    const labelMap: Record<string, string> = {
      编辑: '编辑',
      受理: '受理',
      提交审核: '提交审核',
      审核通过: '审核通过',
      详情: '详情',
      分配: '分配',
      转线下: '转线下',
      退款有效客户: '退款客户',
      取消预约: '取消',
      终止出行: '终止',
      订单已结算: '结算',
      电话跟进: '跟进',
      流转至挽单池: '挽单',
      作废: '作废',
      转给其它客服: '转客服',
    };

    return labelMap[action.label] || action.label;
  };

  // 获取直接显示的操作按钮（最多2个）
  const getDirectActions = (record: any) => {
    const allActions = getAllActions(record);
    return allActions.slice(0, Math.min(allActions.length, 2));
  };

  // 判断是否应该显示"更多"按钮
  const shouldShowMoreButton = (record: any) => {
    const allActions = getAllActions(record);
    return allActions.length > 2;
  };

  // 获取下拉菜单中的操作按钮
  const getDropdownActions = (record: any) => {
    const allActions = getAllActions(record);
    return allActions.slice(2);
  };

  // 处理普通下拉菜单操作
  const handleDropdownAction = (action: any, record: any) => {
    // 执行操作
    action.handler();
    // 关闭下拉菜单
    record.dropdownVisible = false;
  };

  // 处理确认操作
  const handleConfirmAction = (action: any, record: any) => {
    // 执行操作
    action.handler();
    // 关闭下拉菜单
    record.dropdownVisible = false;
  };

  // 处理取消操作
  const handleCancelAction = (record: any) => {
    // 关闭下拉菜单
    record.dropdownVisible = false;
  };

  // 汇总行相关方法
  // 格式化金额显示
  const formatMoney = (amount: number | string | null | undefined): string => {
    if (!amount && amount !== 0) return '-';
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    if (Number.isNaN(num)) return '-';
    return `¥${num.toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  };

  // 获取状态汇总文本
  const getStatusSummaryText = (record: any): string => {
    if (
      !record.order_status_to_num ||
      !Array.isArray(record.order_status_to_num)
    ) {
      return '状态汇总';
    }

    // 取前2个状态显示，如果超过2个则显示"..."
    const displayStatuses = record.order_status_to_num.slice(0, 1);
    const statusTexts = displayStatuses.map(
      (item: any) => `${item.status}(${item.num}个)`
    );

    if (record.order_status_to_num.length > 1) {
      statusTexts.push('...');
    }

    return statusTexts.join('、');
  };

  // 获取状态汇总的 tooltip 内容
  const getStatusSummaryTooltip = (record: any): string => {
    if (
      !record.order_status_to_num ||
      !Array.isArray(record.order_status_to_num)
    ) {
      return '暂无状态统计信息';
    }

    const statusTexts = record.order_status_to_num.map(
      (item: any) => `${item.status}: ${item.num}个`
    );

    return `状态分布：\n${statusTexts.join('\n')}`;
  };

  // 获取订单状态颜色
  const getOrderStatusColor = (status: string) => {
    // 使用字典数据和工具函数获取颜色，保持代码一致性
    return getColor(orderStatusListM, status) || '';
  };

  // 获取地接社汇总的 tooltip 内容
  const getAgencySummaryTooltip = (record: any): string => {
    if (
      !record.local_travel_agency ||
      !Array.isArray(record.local_travel_agency)
    ) {
      return '暂无地接社信息';
    }

    const agencyTexts = record.local_travel_agency.map(
      (agency: string, index: number) => `${index + 1}. ${agency}`
    );

    return `地接社列表：\n${agencyTexts.join('\n')}`;
  };
</script>

<style lang="less" scoped>
  // 隐藏汇总行的水平滚动条，只允许通过主表格滚动
  // :deep(.arco-table-tfoot-wrapper) {
  //   overflow-x: hidden !important;
  // }
  :deep(.arco-table-tfoot) {
    overflow-x: hidden !important;
  }

  .bg-wrap {
    display: flex;
    flex-direction: column;
    width: 860px;
    height: 860px;
    border: 1px solid var(--color-border-1);
    background: url(@/assets/images/travel/travel-bg.png) no-repeat center
      center;
    background-size: cover;
    padding: 20px;
    border-radius: 10px;
    .icon-box {
      position: relative;
      z-index: 1;
      display: flex;
      justify-content: center;
      z-index: 1;
      margin-bottom: -55px;
      .title-icon {
        position: relative;
        height: 110px;
        .title-img {
          height: 100%;
          width: 100%;
          margin-top: 5px;
        }
        .top-icon {
          position: absolute;
          top: -16px;
          left: 50%;
          transform: translateX(-50%);
          width: 120px;
          .top-icon-bg {
            width: 100%;
          }
          .cat-logo-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 106px;
          }
        }
        .left-bag {
          position: absolute;
          left: -70px;
          top: -10px;
          width: 114px;
          height: 114px;
        }
        .right-cao {
          position: absolute;
          right: -105px;
          top: -10px;
          width: 165px;
          // height: 165px;
        }
      }
    }
    .form-card {
      position: relative;
      flex: 1;
      padding: 60px 40px 15px 20px;
      height: 100%;
      background-color: var(--color-bg-1);
      border-radius: 25px;
      color: #359245 !important;
      font-size: 24px;
      .travel-line-tip {
        font-size: 14px;
        margin-top: 5px;
        margin-left: 20px;
        color: rgba(213, 0, 0, 1);
        // font-weight: bold;
      }
      .gift-select {
        position: relative;
        .gift-info-box {
          position: absolute;
          top: -140%;
          right: -0px;
          display: flex;
          flex-direction: column;
          .gift-img-box {
            width: 100px;
            height: 100px;
            .gift-img {
              width: 100%;
              height: 100%;
            }
          }
          .gift-info {
            display: flex;
            font-size: 18px;
            font-weight: bold;
            margin-top: 10px;
            .price-number {
              display: flex;
              align-items: baseline;
              .flag {
                font-size: 14px;
              }
            }
          }
        }
      }

      // 提交按钮
      .form-button {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 180px;
        height: 60px;
        border-radius: 50px;
        color: rgba(67, 113, 59, 1);
        font-weight: bold;
        border: 2px solid #43713b;
        background: linear-gradient(180deg, #24d680 0%, #08a125 100%);
        cursor: pointer;
        .text {
          position: relative;
          z-index: 1;
          font-size: 32px;
        }
        &.submit {
          color: rgba(193, 46, 0, 1);
          background: linear-gradient(180deg, #ffd600 0%, #ff7a00 100%);
          border-color: #c12e00;
          margin-left: 90px;
        }
        &::before {
          content: '';
          position: absolute;
          top: 3px;
          width: 140px;
          height: 26px;
          border-radius: 25px;
          background: linear-gradient(
            180deg,
            rgba(255, 255, 255, 0.6) 0%,
            rgba(255, 255, 255, 0) 100%
          );
        }
        &::after {
          content: '';
          position: absolute;
          top: 7px;
          left: 14px;
          width: 16px;
          height: 16px;
          border-radius: 25px;
          background: url(@/assets/images/travel/ellipse.png) no-repeat center;
        }
      }
      // 表单
      .original-price {
        font-size: 36px;
        font-weight: bold;
        padding-left: 30px;
        .flag {
          font-size: 20px;
        }

        &.has-cut {
          // text-decoration: line-through;
          .price-number {
            text-decoration: line-through;
          }
        }
      }
      // 刷新价格按钮
      .refresh-price-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 160px;
        height: 50px;
        background: rgba(113, 216, 132, 1);
        border-radius: 25px;
        color: #43713b;
        font-weight: bold;
        border: 2px solid #43713b;
        margin-left: 100px;
        cursor: pointer;
        &:hover {
          background: rgba(113, 216, 132, 0.8);
        }
      }
      // 总团款输入框
      .cut-price-input {
        position: relative;
        padding-left: 40px;
        color: #d50001;
        :deep(input) {
          color: #d50001 !important;
          font-size: 42px !important;
        }
        &.show-flag {
          &::before {
            content: '￥';
            position: absolute;
            color: #d50001;
            font-size: 20px;
            font-weight: bold;
            bottom: 5px;
            left: 32px;
          }
        }
      }
      // 容器的样式类 重置
      // -----start
      :deep(.arco-input-wrapper) {
        height: 50px;
        background-color: #ebffeb;
        border-color: #88cd91 !important;
        border-width: 2px;
        border-radius: 25px;
        &.arco-input-focus {
          border-color: #88cd91 !important;
        }
        .arco-input.arco-input-size-large {
          font-size: 24px;
        }
      }
      :deep(.arco-select-view-single) {
        background-color: #ebffeb;
        border-color: #88cd91 !important;
        border-width: 2px;
        border-radius: 25px;
        font-size: 24px;
        height: 50px;
      }
      :deep(.arco-picker) {
        background-color: #ebffeb;
        border-color: #88cd91 !important;
        border-width: 2px;
        border-radius: 25px;
        height: 50px;
      }

      :deep(.arco-form-item-label) {
        color: #359245 !important;
        font-size: 24px;
        font-weight: bold;
        line-height: 50px;
      }
      :deep(.arco-radio-group) {
        line-height: 50px;
      }
      // ----end

      // 重新组件设置颜色和边框
      // 图表类颜色设置
      :deep(.arco-select-view-icon) {
        color: #359245 !important;
        svg {
          font-size: 24px !important;
          font-weight: bold;
        }
      }

      :deep(.arco-picker-suffix-icon) {
        color: #359245 !important;
        svg {
          font-size: 24px !important;
          font-weight: bold;
        }
      }
      :deep(.arco-select-view-value) {
        color: #359245;
        font-weight: bold;
        font-size: 24px;
      }
      :deep(.arco-radio-label) {
        color: #359245;
        font-size: 24px;
        font-weight: bold;
      }

      :deep(.arco-radio-icon) {
        width: 30px;
        height: 30px;
        &::after {
          width: 28px;
          height: 28px;
          background-color: transparent;
        }
      }

      :deep(.arco-radio-checked) {
        .arco-radio-icon {
          background-color: var(--color-white);
          border-color: #90d69d;
          border-width: 1px;
        }
        &:hover {
          border-color: #359245;
          border-width: 1px;
        }
        .arco-radio-icon::after {
          // background-color: var(--color-white);
          background-color: #359245;
          transform: scale(0.6);
        }
      }
    }
  }

  .arco-form-item {
    margin-bottom: 0px;
  }

  :deep(.arco-link) {
    font-size: 12px;
  }

  // 新增：可编辑单元格样式
  .editable-cell-wrapper {
    position: relative;
    display: flex;
    align-items: flex-start;
    gap: 4px;

    .edit-button {
      flex-shrink: 0;
      width: 20px;
      height: 20px;
      padding: 0;
      opacity: 0.6;
      transition: opacity 0.2s ease;

      :deep(.arco-btn-icon) {
        font-size: 12px;
        color: var(--color-text-3);
      }

      &:hover {
        opacity: 1;

        :deep(.arco-btn-icon) {
          color: rgb(var(--primary-6));
        }
      }
    }

    &:hover .edit-button {
      opacity: 1;
    }
  }

  // 新增：响应式操作按钮容器样式
  .action-buttons-container {
    width: 100%;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .action-buttons-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    flex-wrap: wrap;
    max-width: 100%;

    // 当按钮换行时的样式
    &:has(.action-btn:nth-child(n + 3)) {
      justify-content: flex-start;
      gap: 2px 4px;
    }
  }

  .action-btn {
    flex-shrink: 0;
    min-width: auto;
    padding: 0 8px;
    font-size: 12px;
    height: 24px;
    line-height: 22px;
    border-radius: 4px;
    transition: all 0.2s ease;
    white-space: nowrap;

    // 确保按钮文字不被截断
    overflow: visible;
    text-overflow: clip;

    &.arco-btn-text {
      border: none;
      background: transparent;

      &:hover {
        background: var(--color-fill-2);
      }
    }

    // 紧凑模式样式
    &.action-btn-compact {
      padding: 0 6px;
      font-size: 11px;
      min-width: 32px;

      // 紧凑模式下隐藏图标，节省空间
      :deep(.arco-btn-icon) {
        display: none;
      }
    }

    // 更多按钮特殊样式
    &.more-btn {
      color: var(--color-text-2);

      &:hover {
        color: var(--color-text-1);
        background: var(--color-fill-2);
      }

      :deep(.arco-btn-icon) {
        margin-left: 2px;
        font-size: 10px;
      }
    }
  }

  // 响应式适配
  @media (max-width: 1200px) {
    .action-buttons-wrapper {
      gap: 2px;

      .action-btn {
        padding: 0 6px;
        font-size: 11px;

        &:not(.action-btn-compact) {
          :deep(.arco-btn-icon) {
            display: none;
          }
        }
      }
    }
  }

  // 移动端适配
  @media (max-width: 768px) {
    .action-buttons-container {
      padding: 1px 2px;
    }

    .action-buttons-wrapper {
      gap: 1px 2px;

      .action-btn {
        padding: 0 4px;
        font-size: 10px;
        height: 22px;
        line-height: 20px;
        min-width: 28px;

        :deep(.arco-btn-icon) {
          display: none;
        }
      }
    }
  }

  // 新增：下拉菜单危险选项样式
  :deep(.danger-option) {
    color: rgb(var(--danger-6));

    &:hover {
      background-color: var(--color-danger-light-1);
      color: rgb(var(--danger-6));
    }

    .arco-icon {
      color: rgb(var(--danger-6));
    }
  }

  // 新增：汇总行样式
  .summary-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    // padding: 4px 8px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #cbd5e1;
    border-radius: 6px;
    min-height: 28px;

    .summary-text {
      font-weight: 600;
      color: #1e293b;
      font-size: 13px;
      letter-spacing: 0.3px;

      &.status-summary {
        color: #0f766e;
        cursor: help;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;

        &:hover {
          color: #0d9488;
        }

        // 响应式布局：在小屏幕上允许换行
        @media (max-width: 768px) {
          white-space: normal;
          line-height: 1.4;
          max-height: 2.8em; // 约2行的高度
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }
    }
  }

  // 汇总行的表格行样式
  :deep(.arco-table-tr) {
    &:has(.summary-cell) {
      background-color: #f1f5f9;
      border-top: 2px solid #e2e8f0;
      border-bottom: 2px solid #e2e8f0;

      &:hover {
        background-color: #e2e8f0;
      }

      .arco-table-td {
        border-bottom: 1px solid #cbd5e1;
        font-weight: 500;
      }
    }
  }

  .title-box {
    font-size: 12px;
  }

  // 手机号单元格样式
  .phone-cell {
    display: flex;
    align-items: center;

    .duplicate-phone-warning {
      animation: warning-pulse 2s infinite;

      &:hover {
        color: #ff4d4f !important;
        transform: scale(1.1);
        transition: all 0.2s ease;
      }
    }
  }

  // 警告图标脉冲动画
  @keyframes warning-pulse {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.6;
    }
    100% {
      opacity: 1;
    }
  }

  // 媒体订单号单元格样式
  .media-order-no-cell {
    width: 100%;
    max-width: 100%;

    .media-order-no-text {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.4;
      max-height: 2.8em; // 约2行的高度 (1.4 * 2)
      word-break: break-all;
      word-wrap: break-word;
      white-space: normal;
      font-size: 13px;
      color: #1f2937;

      // 确保在不同屏幕尺寸下都能正常显示
      @media (max-width: 1200px) {
        font-size: 12px;
        line-height: 1.3;
        max-height: 2.6em;
      }

      @media (max-width: 768px) {
        font-size: 11px;
        line-height: 1.2;
        max-height: 2.4em;
      }

      // 兼容不支持 line-clamp 的浏览器
      @supports not (-webkit-line-clamp: 2) {
        max-height: 2.8em;
        overflow: hidden;
        position: relative;

        &::after {
          content: '...';
          position: absolute;
          right: 0;
          bottom: 0;
          background: linear-gradient(to right, transparent, #fff 50%);
          padding-left: 20px;
        }
      }
    }
  }

  // 游客信息单元格样式
  .tourist-info-cell {
    position: relative;
    display: flex;
    align-items: flex-start;
    gap: 6px;

    .error-tip-icon {
      color: #ff4d4f;
      font-size: 14px;
      margin-top: 2px;
      flex-shrink: 0;
      cursor: help;
    }
  }
</style>
