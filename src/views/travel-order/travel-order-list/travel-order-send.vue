<template>
  <d-modal
    v-model:visible="visible"
    width="460px"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    :mask-closable="false"
    :esc-to-close="false"
    title="订单审核通过"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formModel" :auto-label-width="true">
      <a-form-item label="订单编号">
        <span>{{ formModel.order_no }}</span>
      </a-form-item>
      <a-form-item
        label="出行时间"
        field="real_travel_date"
        :rules="requiredRuleArr"
      >
        <a-range-picker v-model="formModel.real_travel_date" allow-clear />
      </a-form-item>
      <!-- <a-form-item label="手机号" field="phone" :rules="requiredRule">
        <a-input v-model="formModel.phone" placeholder="请输入" />
      </a-form-item> -->
      <a-divider></a-divider>
      <div class="info-text">
        <icon-info-circle class="primary_color" />
        审核通过后，订单状态将 变更为
        <span class="primary_color text_underline"> 已发确认件(3级) </span>
        ，是否确认？
      </div>
    </a-form>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { cloneDeep, toString } from 'lodash';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { requiredRule, requiredRuleArr } from '@/utils/util';
  import RequestSelect from '@/components/select/request-select.vue';
  import {
    contactWayListM,
    priceStatusListM,
    yesOrNo,
  } from '@/components/dict-select/dict-travel';
  import UploadImageFile from '@/components/upload-file/upload-image-file.vue';

  const defaultForm = () => ({
    order_no: null,
    phone: null,
    real_travel_date: [] as any[],
  });

  const emit = defineEmits(['save']);

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const formModel = ref(defaultForm());
  const info = ref<any>({});
  const validate = () => {
    return formRef.value?.validate();
  };
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  const show = (dinfo: any) => {
    info.value = dinfo;
    let initForm = defaultForm();
    formModel.value = initForm;
    Object.keys(initForm).forEach((key) => {
      // @ts-ignore
      formModel.value[key] =
        dinfo[key] || initForm[key as keyof typeof initForm];
    });
    if (dinfo.real_travel_start_date && dinfo.real_travel_end_date) {
      formModel.value.real_travel_date = [
        dinfo.real_travel_start_date,
        dinfo.real_travel_end_date,
      ];
    } else {
      formModel.value.real_travel_date = [];
    }
    clearValidate();
    visible.value = true;
    loading.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  const handleBeforeOk = async () => {
    const result = await validate();
    if (!result) {
      loading.value = true;
      request('/api/travel/sendEnsureOrder', {
        ...formModel.value,
        real_travel_start_date: formModel.value.real_travel_date[0],
        real_travel_end_date: formModel.value.real_travel_date[1],
      })
        .then(() => {
          Message.success('已成功发送确认件，请提醒客户按照预约时间出行');
          handleCancel();
          emit('save');
        })
        .catch(() => {
          loading.value = false;
        });
    }
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped>
  .info-text {
    font-size: 14px;
    font-weight: bold;
  }
  .text_underline {
  }
</style>
