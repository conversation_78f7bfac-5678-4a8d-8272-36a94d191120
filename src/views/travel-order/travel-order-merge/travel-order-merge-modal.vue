<template>
  <d-modal
    v-model:visible="visible"
    width="500px"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    :mask-closable="false"
    :esc-to-close="false"
    :title="modalTitle"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formModel" :auto-label-width="true">
      <a-form-item label="订单编号">
        <a-textarea
          v-model="formModel.sub_order_no"
          :auto-size="{ minRows: 5 }"
          placeholder="订单编号，一行一个"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';

  const defaultForm = () => ({
    sub_order_no: null,
    parent_order_no: null,
  });

  const emit = defineEmits(['save']);

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const formModel = ref(defaultForm());
  const info = ref<any>({});
  const validate = () => {
    return formRef.value?.validate();
  };
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  const modalTitle = computed(() =>
    formModel.value.parent_order_no
      ? `合并订单(${formModel.value.parent_order_no})`
      : '合并订单'
  );

  const show = (dinfo: any) => {
    info.value = dinfo;
    let initForm = defaultForm();
    formModel.value = initForm;
    if (dinfo) {
      Object.keys(initForm).forEach((key) => {
        formModel.value[key as keyof typeof initForm] =
          dinfo?.[key] || initForm[key as keyof typeof initForm];
      });
      formModel.value.sub_order_no = dinfo.sub_order_no.join('\n');
    }

    clearValidate();
    visible.value = true;
    loading.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  const handleBeforeOk = async () => {
    const result = await validate();
    if (!result) {
      loading.value = true;
      request('/api/travel/orderMerge', {
        ...formModel.value,
      })
        .then(() => {
          Message.success('保存成功');
          handleCancel();
          emit('save');
        })
        .catch(() => {
          loading.value = false;
        });
    }
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>
