<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="theTable.search()"
      >
        <template #formItemGroup>
          <a-form-item label="虚拟订单ID">
            <a-input
              v-model="formModel.parent_order_no"
              placeholder="请输入"
              allow-clear
            />
          </a-form-item>
          <a-form-item label="订单编号">
            <a-input
              v-model="formModel.order_no"
              placeholder="请输入"
              allow-clear
            />
          </a-form-item>
        </template>
      </search-form-fold>

      <div class="table-card-header">
        <div> </div>
        <a-space>
          <a-button type="primary" @click="showEditFn()">
            <template #icon>
              <icon-plus />
            </template>
            新增
          </a-button>
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :scroll-percent="{ x: 1000, maxHeight: '70vh' }"
        :send-params="formModel"
      >
        <template #action="{ record }: TableColumnSlot">
          <a-spin class="jc-cen fd-r" :loading="record.loading">
            <a-space>
              <a-link @click="showEditFn(record)"> <icon-edit />编辑 </a-link>
            </a-space>
          </a-spin>
        </template>
      </base-table>
    </a-card>
    <travel-order-merge-modal ref="editRef" @save="handleSubmit()" />
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';

  import { ref, reactive, computed } from 'vue';
  import request from '@/api/request';
  import { stringArrShow } from '@/utils/table-utils/columns-config';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import { TableColumnSlot } from '@/global';
  import TravelOrderMergeModal from '@/views/travel-order/travel-order-merge/travel-order-merge-modal.vue';

  const generateFormModel = () => {
    return {
      parent_order_no: null,
      order_no: null,
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    return request('/api/travel/orderMergeList', {
      ...data,
    });
  };

  const editRef = ref();
  function showEditFn(record?: any) {
    editRef.value?.show(record);
  }

  const columns = [
    {
      title: '虚拟订单编号',
      dataIndex: 'parent_order_no',
    },
    {
      title: '订单编号',
      dataIndex: 'sub_order_no',
      render: stringArrShow(),
    },
    {
      title: '创建时间',
      dataIndex: 'add_time',
    },
    {
      title: '更新时间',
      dataIndex: 'update_time',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ];
  // table渲染完成回调
  const changeHandler = (tableData: any) => {
    loading.value = false;
  };

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  const tableParams = computed(() => ({
    ...formModel,
  }));

  const resetSearch = () => {
    Object.assign(formModel, generateFormModel());
    handleSubmit();
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less"></style>
