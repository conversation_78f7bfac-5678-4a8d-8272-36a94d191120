<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        :loading="loading"
        @search="searchAction"
      >
        <template #formItemGroup>
          <a-form-item label="统计维度">
            <a-radio-group
              model-value="order"
              type="button"
              @change="(val:string) => router.replace({query: { type: val } })"
            >
              <a-radio value="clue">线索</a-radio>
              <a-radio value="order">订单</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="日期范围">
            <a-range-picker
              v-model="formModel.date"
              class="w100p"
              format="YYYY-MM-DD"
              :allow-clear="false"
            />
          </a-form-item>
          <a-form-item label="平台">
            <dict-select
              v-model="formModel.platform"
              :data-list="contactWayListM"
            />
          </a-form-item>
          <a-form-item label="客资类型">
            <dict-select
              v-model="formModel.customer_equity_type"
              :data-list="customerEquityTypeM"
            />
          </a-form-item>
          <a-form-item label="订单类型">
            <order-type-select v-model="formModel.order_new_type_custom" />
          </a-form-item>
          <a-form-item label="部门">
            <request-tree-select
              v-model="formModel.department_ids"
              multiple
              request-url="/api/department/list"
              label-key="department_name"
              child-key="child"
              :max-tag-count="3"
              @change="
                () => {
                  formModel.user_role_3 = [];
                }
              "
            />
          </a-form-item>
          <a-form-item label="电销">
            <request-select
              v-model="formModel.user_role_3"
              api="user"
              :max-tag-count="1"
              :send-params="{
                roles: [3],
                department_ids: formModel.department_ids,
              }"
            />
          </a-form-item>
          <a-form-item label="旅游线路">
            <dict-select
              v-model="formModel.area"
              :data-list="dataCacheStore.lineList"
              label-key="line_name"
              value-key="line_name"
            />
          </a-form-item>
          <!--<a-form-item label="订单来源类型">
            <request-select
              v-model="formModel.source_detail"
              request-url="/api/travel/getSourceDetailMap"
              label-key="value"
              value-key="value"
              :max-tag-count="1"
            />
          </a-form-item>-->
        </template>
      </search-form-fold>
    </a-card>
    <a-row class="mt-10" :gutter="[12, 12]">
      <a-col :span="12">
        <customer-service-bouns-pie
          title="订单状态分布"
          :info="info.status_count"
          @export="exportAction('status_count')"
        />
      </a-col>
      <a-col :span="12">
        <customer-service-bouns-funnel
          :info="info.conversion_funnel"
          @export="exportAction('conversion_funnel')"
        />
      </a-col>
      <a-col :span="24">
        <customer-service-bouns-line
          :info="info.conversion_trend"
          :default-field="{
            '支付-发确认件比率': true,
            '总核销率': true,
          }"
          @export="exportAction('conversion_trend')"
        />
      </a-col>
      <a-col :span="24">
        <customer-service-bouns-bar
          :info="info.conversion_service_user"
          :num-info="info.conversion_service_user_count"
          extra-show="订单数"
          @export="exportAction('conversion_service_user')"
        />
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import dayjs from 'dayjs';
  import { computed, onMounted, reactive, ref } from 'vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import CustomerServiceBounsFunnel from '@/views/travel-order/customer-service/components/customer-service-bouns-funnel.vue';
  import CustomerServiceBounsBar from '@/views/travel-order/customer-service/components/customer-service-bouns-bar.vue';
  import CustomerServiceBounsLine from '@/views/travel-order/customer-service/components/customer-service-bouns-line.vue';
  import request from '@/api/request';
  import CustomerServiceBounsPie from '@/views/travel-order/customer-service/components/customer-service-bouns-pie.vue';
  import { useDataCacheStore } from '@/store';
  import {
    contactWayListM,
    customerEquityTypeM,
  } from '@/components/dict-select/dict-travel';
  import OrderTypeSelect from '@/views/travel-order/travel-order-list/order-type-select.vue';
  import RequestTreeSelect from '@/components/select/request-tree-select.vue';
  import { useRouter } from 'vue-router';

  const generateFormModel = () => {
    return {
      service_user_fs_ids: [],
      date: [
        dayjs().add(-6, 'week').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      area: '',
      source_detail: [],

      platform: '',
      customer_equity_type: '', // 客资类型
      order_new_type_custom: [], // "订单类型",
      department_ids: [], // 部门
      user_role_3: [], // 电销
    };
  };
  const router = useRouter();

  const loading = ref(false);
  const formModel = ref(generateFormModel());
  const info = ref({
    conversion_funnel: [],
    conversion_trend: [],
    conversion_service_user: [],
    conversion_service_user_count: [],
    status_count: [],
  });
  const dataCacheStore = useDataCacheStore();
  function searchAction() {
    loading.value = true;
    let order_new_type_search: any = {};
    formModel.value.order_new_type_custom.forEach((item: any) => {
      // 拆分订单类型和细分类型
      const [type, typeDetail] = item.split('___');
      if (!typeDetail) {
        order_new_type_search[type] = [];
      } else if (!order_new_type_search[type]) {
        order_new_type_search[type] = [typeDetail];
      } else if (
        order_new_type_search[type] &&
        order_new_type_search[type].length
      ) {
        order_new_type_search[type].push(typeDetail);
      }
    });
    request('/api/report/serviceUserEfficiencyReport', {
      ...formModel.value,
      order_new_type_search,
      date_begin: formModel.value.date[0],
      date_end: formModel.value.date[1],
    })
      .then((res: any) => {
        info.value = res.data;
        info.value.conversion_service_user?.forEach((item: any) => {
          let extra = info.value.conversion_service_user_count.find(
            (citem: any) => citem.service_user === item.service_user
          );
          Object.assign(item, extra);
        });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function exportAction(export_type: string) {
    request('/api/report/serviceUserEfficiencyReport', {
      ...formModel.value,
      date_begin: formModel.value.date[0],
      date_end: formModel.value.date[1],
      export_type,
      export: true,
      export_now: true,
    });
  }
  onMounted(() => {
    searchAction();
  });
</script>

<style scoped lang="less"></style>
