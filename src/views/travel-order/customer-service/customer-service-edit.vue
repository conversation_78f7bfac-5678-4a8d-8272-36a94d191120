<template>
  <d-modal
    v-model:visible="visible"
    width="500px"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    :mask-closable="false"
    :esc-to-close="false"
    :title="`${formModel.id ? '编辑' : '添加'}客服`"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formModel" :auto-label-width="true">
      <a-form-item label="姓名" field="user_name" :rules="requiredRule">
        <a-input v-model="formModel.user_name" :disabled="!!formModel.id" />
      </a-form-item>
      <a-form-item label="手机号" field="mobile" :rules="requiredRule">
        <a-input v-model="formModel.mobile" :disabled="!!formModel.id" />
      </a-form-item>
      <a-form-item label="每日最多单数">
        <a-input-number
          v-model="formModel.day_max_order"
          :min="0"
          :precision="0"
        />
      </a-form-item>
      <a-form-item v-if="!!formModel.id" label="状态">
        <dict-select
          v-model="formModel.status"
          :allow-clear="false"
          :data-list="customerServiceStatusListM"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { requiredRule } from '@/utils/util';
  import { customerServiceStatusListM } from '@/components/dict-select/dict-travel';

  const defaultForm = () => ({
    mobile: null,
    user_name: null,
    id: null,
    day_max_order: null,
    status: '离线',
  });

  const emit = defineEmits(['save']);

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const formModel = ref(defaultForm());
  const validate = () => {
    return formRef.value?.validate();
  };
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  const show = (dinfo: any) => {
    let initForm = defaultForm();
    formModel.value = initForm;
    if (dinfo) {
      Object.keys(initForm).forEach((key) => {
        // @ts-ignore
        formModel.value[key] =
          dinfo[key] || initForm[key as keyof typeof initForm];
      });
    }

    clearValidate();
    visible.value = true;
    loading.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  const handleBeforeOk = async () => {
    const result = await validate();
    if (!result) {
      loading.value = true;
      request(
        formModel.value.id
          ? '/api/travel/serviceUserEdit'
          : '/api/travel/serviceUserAdd',
        {
          ...formModel.value,
          phone: formModel.value.mobile,
        }
      )
        .then(() => {
          Message.success('保存成功');
          handleCancel();
          emit('save');
        })
        .catch(() => {
          loading.value = false;
        });
    }
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>
