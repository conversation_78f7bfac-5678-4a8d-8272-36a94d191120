<template>
  <div>
    <customer-service-bouns-report-order
      v-if="reportType === 'order'"
    ></customer-service-bouns-report-order>
    <customer-service-bouns-report-clue
      v-else
    ></customer-service-bouns-report-clue>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { useRoute } from 'vue-router';
  import CustomerServiceBounsReportOrder from './customer-service-bouns-report-order.vue';
  import CustomerServiceBounsReportClue from './customer-service-bouns-report-clue.vue';

  const route = useRoute();
  const reportType = computed(() => route.query.type || 'clue');
</script>

<style scoped lang="less"></style>
