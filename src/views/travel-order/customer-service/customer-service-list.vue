<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="handleSubmit()"
      >
        <template #formItemGroup>
          <a-form-item label="客服">
            <a-input v-model="formModel.user_name" allow-clear />
          </a-form-item>
          <a-form-item field="status" label="状态">
            <dict-select
              v-model="formModel.status"
              :data-list="customerServiceStatusListM"
            />
          </a-form-item>
        </template>
      </search-form-fold>

      <!--<div class="table-card-header mt-10">
        <div> </div>
        <a-space>
          <a-button type="primary" @click="showEditFn()">
            <template #icon>
              <icon-plus />
            </template>
            新增
          </a-button>
        </a-space>
      </div>-->
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        class="mt-10"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formModel"
        :scroll-percent="{ x: 1000, maxHeight: '70vh' }"
      >
        <template #action="{ record }: TableColumnSlot">
          <a-space>
            <a-link @click="showEditFn(record)"> <icon-edit />编辑 </a-link>
          </a-space>
        </template>
      </base-table>
    </a-card>
    <customer-service-edit ref="editRef" @save="handleSubmit()" />
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';

  import { ref, reactive } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import { customerServiceStatusListM } from '@/components/dict-select/dict-travel';
  import CustomerServiceEdit from '@/views/travel-order/customer-service/customer-service-edit.vue';

  const generateFormModel = () => {
    return {
      status: null,
      user_name: null,
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    return request('/api/travel/allServiceUser', {
      ...data,
    });
  };

  const editRef = ref();
  function showEditFn(record?: any) {
    editRef.value?.show(record);
  }

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
    },
    {
      title: '客服名称',
      dataIndex: 'user_name',
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
    },
    {
      title: '状态',
      dataIndex: 'status',
    },
    // {
    //   title: '每日最多单数',
    //   dataIndex: 'day_max_order',
    // },
    {
      title: '创建时间',
      dataIndex: 'add_time',
    },
    {
      title: '更新时间',
      dataIndex: 'update_time',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less"></style>
