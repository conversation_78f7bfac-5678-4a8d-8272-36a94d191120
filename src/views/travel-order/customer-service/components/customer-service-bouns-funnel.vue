<template>
  <a-card title="转化漏斗">
    <template #extra>
      <a-space>
        <a-radio-group v-model="type" type="button">
          <a-radio value="chart">图表</a-radio>
          <a-radio value="table">表格</a-radio>
        </a-radio-group>
        <a-button type="primary" @click="emits('export')">
          <template #icon>
            <icon-export />
          </template>
          导出
        </a-button>
      </a-space>
    </template>
    <a-spin class="w100p" :loading="loading">
      <div v-if="type === 'table'" style="height: 403px">
        <a-table
          :columns="columns"
          :scroll="{ y: '100%' }"
          :data="tableData"
          :pagination="false"
        />
      </div>
      <chart v-else class="chart" :options="option" height="400px" autoresize />
    </a-spin>
  </a-card>
</template>

<script setup lang="ts">
  import { computed, PropType, ref, watchEffect } from 'vue';
  import { moneyFormat } from '@/utils/table-utils/table-util';
  import { useAppStore } from '@/store';

  const appStore = useAppStore();
  const props = defineProps({
    info: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  });
  const theme = computed(() => {
    if (appStore.theme === 'dark') return 'dark';
    return '';
  });
  const type = ref('chart');
  const columns = [
    {
      title: '指标',
      dataIndex: 'title',
    },
    {
      title: '数值',
      dataIndex: 'value',
    },
  ];
  const emits = defineEmits(['export']);
  const tableData = ref([
    {
      title: '已支付订金订单数',
      value: '',
      dataKey: '已支付订金订单数',
      valueKey: 'value',
    },
    {
      title: '已支付订金人数',
      value: '',
      dataKey: '已支付订金订单数',
      valueKey: 'count',
    },
    {
      title: '已添加微信订单数',
      value: '',
      dataKey: '已添加微信订单数',
      valueKey: 'value',
    },
    {
      title: '已添加微信人数',
      value: '',
      dataKey: '已添加微信订单数',
      valueKey: 'count',
    },
    {
      title: '已确认订单数',
      value: '',
      dataKey: '已确认订单数',
      valueKey: 'value',
    },
    {
      title: '已确认订单人数',
      value: '',
      dataKey: '已确认订单数',
      valueKey: 'count',
    },
    {
      title: '已发确认件订单数',
      value: '',
      dataKey: '已发确认件订单数',
      valueKey: 'value',
    },
    {
      title: '已发确认件人数',
      value: '',
      dataKey: '已发确认件订单数',
      valueKey: 'count',
    },
    { title: '已出行数', value: '', dataKey: '已出行数', valueKey: 'value' },
    {
      title: '已出行订单人数',
      value: '',
      dataKey: '已出行数',
      valueKey: 'count',
    },
    { title: '已核销数', value: '', dataKey: '已核销数', valueKey: 'value' },
    { title: '已核销人数', value: '', dataKey: '已核销数', valueKey: 'count' },
    { title: '加微率', value: '', dataKey: '加微率', valueKey: 'value' },
    { title: '确认率', value: '', dataKey: '确认率', valueKey: 'value' },
    {
      title: '发确认件比率',
      value: '',
      dataKey: '发确认件比率',
      valueKey: 'value',
    },
    { title: '出行率', value: '', dataKey: '出行率', valueKey: 'value' },
    {
      title: '出行核销率',
      value: '',
      dataKey: '出行核销率',
      valueKey: 'value',
    },
    {
      title: '支付-发确认件比率',
      value: '',
      dataKey: '支付-发确认件比率',
      valueKey: 'value',
    },
    { title: '总核销率', value: '', dataKey: '总核销率', valueKey: 'value' },
  ]);
  const option = ref({
    tooltip: {
      show: false,
    },
    legend: {
      show: false,
    },
    series: [
      {
        name: 'Funnel',
        type: 'funnel',
        left: '0%',
        top: '10%',
        bottom: '10%',
        width: '80%',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '80%',
        sort: 'none',
        gap: 2,
        label: {
          show: true,
          position: 'inside',
          // formatter: '{b}: {c} 人数:  {@count}',
          color: '#000',
          textBorderColor: '#000',
          formatter: ({ data }: any) =>
            `${data.name}: ${data.value} {count|人数: ${data.count}}`,
          rich: {
            count: {
              // color: '#666',
            },
          },
        },
        data: [] as any[],
        markLine: {
          lineStyle: {
            width: 1,
            type: 'solid',
            color: '#cccccc',
          },
          symbol: [],
          data: [],
        },
      },
    ],
  });
  const transformTitle = [
    '',
    '加微率',
    '确认率',
    '发确认件比率',
    '出行率',
    '出行核销率',
  ];
  const loading = ref(false);

  watchEffect(() => {
    option.value.series[0].label.color =
      theme.value === 'dark' ? '#fff' : '#000';
    option.value.series[0].label.textBorderColor =
      theme.value === 'dark' ? '#fff' : '#000';

    option.value.series[0].data = props.info || [];
    option.value.series[0].max = props.info[0]?.value || 100;
    tableData.value.forEach((item) => {
      item.value =
        props.info.find((citem) => citem.name === item.dataKey)?.[
          item.valueKey
        ] || 0;
    });

    // @ts-ignore 循环计算每个区间的转化率  计算方式为 区间值 / 上一个区间值
    option.value.series[0].markLine.data = props.info.map((item, index) => {
      let rate = moneyFormat(
        (item.value / (props.info[index - 1]?.value || 1)) * 100
      );
      let titem = tableData.value.find(
        (citem) => citem.title === transformTitle[index]
      );
      if (titem) {
        titem.value = `${rate}%`;
      }
      // 每个区间的转化率
      return [
        {
          name: `${transformTitle[index]} ${rate}%`,
          emphasis: {
            disabled: true,
          },
          x: `${
            (item.value / (props.info[index - 1]?.value || 1)) * 0.5 * 0.7 + 40
          }%`,
          y: `${10 + (index / props.info.length) * 100 * 0.8}%`,
        },
        {
          x: `70%`,
          y: `${10 + (index / props.info.length) * 100 * 0.8}%`,
          symbol: 'arrow',
        },
      ];
    });
    // @ts-ignore 删除第一个没用的数据
    option.value.series[0].markLine.data.splice(0, 1);

    // 总核销率
    const total_rate = props.info.length
      ? (
          ((props.info.find((item) => item.name === '已核销数')?.value || 0) /
            (props.info.find((item) => item.name === '已支付订金订单数')
              ?.value || 1)) *
          100
        ).toFixed(2)
      : 0;
    let titem = tableData.value.find((citem) => citem.title === '总核销率');
    if (titem) {
      titem.value = `${total_rate}%`;
    }
    // 总核销率
    // @ts-ignore
    option.value.series[0].markLine.data.push([
      {
        name: `总核销率 ${total_rate}%`,
        emphasis: {
          disabled: true,
        },
        label: {
          position: 'middle',
          color: theme.value === 'dark' ? '#fff' : '#000',
        },
        lineStyle: {
          width: 2,
          type: 'solid',
          color: theme.value === 'dark' ? '#fff' : '#000',
        },
        x: `95%`,
        y: `10%`,
      },
      {
        x: `95%`,
        y: `90%`,
        symbol: 'arrow',
      },
    ]);
    // 横线-上
    // @ts-ignore
    option.value.series[0].markLine.data.push([
      {
        lineStyle: {
          width: 1,
          type: 'solid',
          color: theme.value === 'dark' ? '#fff' : '#000',
        },
        emphasis: {
          disabled: true,
        },
        x: `50%`,
        y: `10%`,
      },
      {
        x: `95%`,
        y: `10%`,
      },
    ]);
    // 横线-下
    // @ts-ignore
    option.value.series[0].markLine.data.push([
      {
        lineStyle: {
          width: 1,
          type: 'solid',
          color: theme.value === 'dark' ? '#fff' : '#000',
        },
        emphasis: {
          disabled: true,
        },
        x: `50%`,
        y: `90%`,
      },
      {
        x: `95%`,
        y: `90%`,
      },
    ]);

    // 支付-发确认件比率
    const sure_rate = props.info.length
      ? (
          ((props.info.find((item) => item.name === '已发确认件订单数')
            ?.value || 0) /
            (props.info.find((item) => item.name === '已支付订金订单数')
              ?.value || 1)) *
          100
        ).toFixed(2)
      : 0;
    let sitem = tableData.value.find(
      (citem) => citem.title === '支付-发确认件比率'
    );
    if (sitem) {
      sitem.value = `${sure_rate}%`;
    }
    // @ts-ignore
    option.value.series[0].markLine.data.push([
      {
        name: `支付-发确认件比率 ${sure_rate}%`,
        label: {
          position: 'insideMiddleBottom',
          color: theme.value === 'dark' ? '#fff' : '#000',
        },
        emphasis: {
          disabled: true,
        },
        lineStyle: {
          width: 2,
          type: 'solid',
          color: theme.value === 'dark' ? '#fff' : '#000',
        },
        x: `5%`,
        y: `10%`,
      },
      {
        x: `5%`,
        y: `${10 + (3.5 / props.info.length) * 100 * 0.8}%`,
        symbol: 'arrow',
      },
    ]);
    // 横线-上
    // @ts-ignore
    option.value.series[0].markLine.data.push([
      {
        lineStyle: {
          width: 1,
          type: 'solid',
          color: theme.value === 'dark' ? '#fff' : '#000',
        },
        emphasis: {
          disabled: true,
        },
        x: `5%`,
        y: `10%`,
      },
      {
        x: `30%`,
        y: `10%`,
      },
    ]);
    // 横线-下
    // @ts-ignore
    option.value.series[0].markLine.data.push([
      {
        lineStyle: {
          width: 1,
          type: 'solid',
          color: theme.value === 'dark' ? '#fff' : '#000',
        },
        emphasis: {
          disabled: true,
        },
        x: `5%`,
        y: `${10 + (3.5 / props.info.length) * 100 * 0.8}%`,
      },
      {
        x: `30%`,
        y: `${10 + (3.5 / props.info.length) * 100 * 0.8}%`,
      },
    ]);
  });
</script>

<style scoped></style>
