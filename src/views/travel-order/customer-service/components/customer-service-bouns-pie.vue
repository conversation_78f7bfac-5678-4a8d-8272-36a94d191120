<template>
  <a-card :title="title">
    <template #extra>
      <a-space>
        <a-radio-group v-model="type" type="button">
          <a-radio value="chart">图表</a-radio>
          <a-radio value="table">表格</a-radio>
        </a-radio-group>
        <a-button type="primary" @click="emits('export')">
          <template #icon>
            <icon-export />
          </template>
          导出
        </a-button>
      </a-space>
    </template>
    <a-spin class="w100p" :loading="loading">
      <div v-if="type === 'table'" style="height: 403px">
        <a-table
          :columns="columns"
          :scroll="{ y: '100%' }"
          :data="tableData"
          :pagination="false"
        />
      </div>
      <chart v-else class="chart" :options="option" height="400px" autoresize />
    </a-spin>
  </a-card>
</template>

<script setup lang="ts">
  import { ref, watch, PropType, watchEffect, computed } from 'vue';
  import { moneyFormat } from '@/utils/table-utils/table-util';
  import { useAppStore } from '@/store';
  import { rateFormatShow } from '@/utils/table-utils/columns-config';

  const appStore = useAppStore();
  const theme = computed(() => {
    if (appStore.theme === 'dark') return 'dark';
    return '';
  });
  const type = ref('chart');
  const columns = [
    {
      title: '状态',
      dataIndex: 'status',
    },
    {
      title: '订单数',
      dataIndex: 'total',
    },
    {
      title: '占比',
      dataIndex: 'proportion',
    },
  ];
  const emits = defineEmits(['export']);
  const tableData = ref<any[]>([]);
  const option = ref({
    tooltip: {
      show: true,
      formatter: '{b}:{c}单({d}%)',
    },
    legend: {
      show: true,
    },
    series: [
      {
        type: 'pie',
        radius: ['0%', '40%'],
        label: {
          show: true,
          formatter: '{b}:{c}单({d}%)',
        },
        data: [] as any[],
      },
    ],
  });
  const props = defineProps({
    info: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    title: {
      type: String,
      default: '',
    },
  });
  const loading = ref(false);

  watchEffect(() => {
    option.value.series[0].data = props.info?.map((item) => ({
      name: item.status,
      value: item.total,
    }));
    let orderNum = props.info.reduce((prev, cur) => prev + cur.total, 0);
    tableData.value = props.info.map((item) => ({
      status: item.status,
      total: item.total,
      proportion: `${item.proportion}%`,
    }));
  });
</script>

<style scoped></style>
