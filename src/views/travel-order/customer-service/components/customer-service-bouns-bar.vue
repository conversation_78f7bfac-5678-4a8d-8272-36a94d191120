<template>
  <a-card title="客服转化率对比">
    <template #extra>
      <a-space>
        <a-radio-group v-model="type" type="button">
          <a-radio value="chart">图表</a-radio>
          <a-radio value="table">表格</a-radio>
        </a-radio-group>
        <a-button type="primary" @click="emits('export')">
          <template #icon>
            <icon-export />
          </template>
          导出
        </a-button>
      </a-space>
    </template>
    <a-spin class="w100p" :loading="loading">
      <a-table
        v-if="type === 'table'"
        :columns="showColumns"
        :data="tableData"
        :pagination="false"
      />
      <chart
        v-else
        class="chart"
        :options="option"
        :height="chartHeight"
        autoresize
      />
    </a-spin>
  </a-card>
</template>

<script setup lang="ts">
  import { computed, PropType, ref, watch, watchEffect } from 'vue';
  import { rateFormatShow } from '@/utils/table-utils/columns-config';
  import { useAppStore } from '@/store';

  const appStore = useAppStore();
  const theme = computed(() => {
    if (appStore.theme === 'dark') return 'dark';
    return '';
  });
  const props = defineProps({
    info: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    numInfo: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    extraShow: {
      type: String,
      default: '',
    },
  });
  const emits = defineEmits(['export']);
  const type = ref('table');
  const columns = ref<any[]>([]);
  const showColumns = computed(() => [
    {
      title: '客服',
      dataIndex: 'service_user',
    },
    {
      title: props.extraShow,
      dataIndex: props.extraShow,
      align: 'right',
    },
    ...columns.value,
  ]);
  const tableData = ref<any[]>([]);
  const option = ref({
    tooltip: {
      show: true,
      trigger: 'axis',
      valueFormatter: (value: number) => `${value}%`,
    },
    legend: {
      show: true,
    },
    xAxis: {
      type: 'value',
    },
    yAxis: {
      type: 'category',
      data: [] as any[],
      axisTick: { show: false },
      axisLabel: {
        show: true,
        color: '#000',
        formatter(value: string) {
          let num =
            props.numInfo?.find((item) => item.service_user === value)?.[
              props.extraShow
            ] || '0';
          return `${value}\n\n{num|${props.extraShow}: ${num}}`;
        },
        rich: {
          num: {
            // margin: 5,
          },
        },
      },
    },
    series: [] as any[],
  });
  const loading = ref(false);
  const chartHeight = computed(
    () => `${+option.value.yAxis.data.length * 250}px`
  );
  const labelOption = computed(() => ({
    show: true,
    // distance: 30,
    align: 'left',
    verticalAlign: 'middle',
    /// / rotate: 90,
    formatter: '{a} {c}% ',
    color: theme.value === 'dark' ? '#fff' : '#000',
    textBorderColor: theme.value === 'dark' ? '#000' : '#fff',
  }));
  watchEffect((val) => {
    tableData.value = props.info;
    columns.value = Object.keys(props.info[0] || {})
      .filter(
        (key) =>
          !['service_user', 'service_user_fs_id', props.extraShow].includes(key)
      )
      .map((key) => ({
        title: key,
        dataIndex: key,
        render: key.includes('率') ? rateFormatShow() : undefined,
        align: 'right',
      }));
    let data = Object.keys(props.info[0] || {})
      .filter(
        (key) =>
          !['service_user', 'service_user_fs_id', props.extraShow].includes(key)
      )
      .map((key) => ({
        name: key,
        type: 'bar',
        label: labelOption,
        data: [] as any[],
      }));
    data.forEach((item) => {
      item.data = props.info.map((citem) => citem[item.name]);
    });
    option.value.series = data;
    option.value.yAxis.data = props.info.map((item) => item.service_user);
  });
</script>

<style scoped></style>
