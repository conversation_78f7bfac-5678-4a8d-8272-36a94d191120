<template>
  <a-card title="转化趋势">
    <template #extra>
      <a-space>
        <a-radio-group v-model="type" type="button">
          <a-radio value="chart">图表</a-radio>
          <a-radio value="table">表格</a-radio>
        </a-radio-group>
        <a-button type="primary" @click="emits('export')">
          <template #icon>
            <icon-export />
          </template>
          导出
        </a-button>
      </a-space>
    </template>
    <a-spin class="w100p" :loading="loading">
      <div v-if="type === 'table'" style="height: 403px">
        <a-table
          :columns="columns"
          :scroll="{ x: 'max-content' }"
          :data="tableData"
          :pagination="false"
        />
      </div>
      <chart v-else class="chart" :options="option" height="400px" autoresize />
    </a-spin>
  </a-card>
</template>

<script setup lang="ts">
  import { computed, PropType, ref, watch, watchEffect } from 'vue';
  import { rateFormatShow } from '@/utils/table-utils/columns-config';

  const emits = defineEmits(['export']);
  const props = defineProps({
    info: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    defaultField: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
  });
  const type = ref('chart');

  const tableData = ref<any[]>([]);
  const option = ref({
    tooltip: {
      trigger: 'axis',
      valueFormatter: (value: number) => `${value}%`,
    },
    legend: {
      data: [] as string[],
      selected: props.defaultField,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [] as string[],
    },
    yAxis: {
      type: 'value',
      max: 100,
    },
    series: [] as any[],
  });
  const loading = ref(false);
  const columns = computed(() => [
    {
      title: '时间',
      dataIndex: 'date',
    },
    ...option.value.series.map((item) => ({
      title: item.name,
      dataIndex: item.name,
      render: rateFormatShow(),
      align: 'right',
    })),
  ]);

  watchEffect(() => {
    tableData.value = props.info;
    let data = Object.keys(props.info[0] || {})
      .filter((key) => key !== 'date')
      .map((key) => ({
        name: key,
        type: 'line',
        data: [] as any[],
      }));
    data.forEach((item) => {
      item.data = props.info.map((citem) => parseFloat(citem[item.name] || 0));
    });
    option.value.series = data;
    option.value.legend.data = data.map((item) => item.name);
    option.value.legend.data.forEach((item) => {
      if (!option.value.legend.selected[item]) {
        option.value.legend.selected[item] = false;
      }
    });

    option.value.xAxis.data = props.info.map((item) => item.date);
  });
</script>

<style scoped></style>
