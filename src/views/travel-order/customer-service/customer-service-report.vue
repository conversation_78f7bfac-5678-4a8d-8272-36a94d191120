<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="handleSubmit()"
      >
        <template #formItemGroup>
          <a-form-item label="客服">
            <request-select
              v-model="formModel.service_user_fs_ids"
              request-url="/api/travel/allServiceUser"
              label-key="user_name"
              value-key="fs_user_id"
              :send-params="{
                no_has_dimission: true,
              }"
              @blur="handleSubmit()"
            />
          </a-form-item>
          <a-form-item label="旅游线路">
            <dict-select
              v-model="formModel.area"
              :data-list="dataCacheStore.lineList"
              label-key="line_name"
              value-key="line_name"
            />
          </a-form-item>
          <!-- <a-form-item label="日期">
            <c-range-picker v-model="formModel.date" class="w100p" />
          </a-form-item>-->
        </template>
      </search-form-fold>

      <div class="table-card-header">
        <div> </div>
        <a-space>
          <!--<a-button type="primary" @click="exportAction">
            <template #icon>
              <icon-export />
            </template>
            导出
          </a-button>-->
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        bordered
        :data-config="getList"
        no-pagination
        :send-params="tableParmas"
        :span-method="dataSpanMethod"
      >
      </base-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';

  import { ref, reactive, computed } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import dayjs from 'dayjs';
  import { cloneDeep } from 'lodash';
  import RequestSelect from '@/components/select/request-select.vue';
  import { useDataCacheStore } from '@/store';

  const generateFormModel = () => {
    return {
      date: [
        // dayjs().add(-6, 'days').format('YYYY-MM-DD'),
        // dayjs().format('YYYY-MM-DD'),
      ],
      area: '',
      service_user_fs_ids: [],
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const tableParmas = computed(() => ({
    ...formModel,
    date_begin: formModel.date[0],
    date_end: formModel.date[1],
  }));
  const dataCacheStore = useDataCacheStore();

  const fields = [
    { parent: '待处理', title: '待确认订单数', key: 'need_confirmed' },
    {
      parent: '待处理',
      title: '待发确认件订单数',
      key: 'need_send_confirmation',
    },
    { parent: '当日', title: '新增订单数', key: 'day_add_order' },
    { parent: '当日', title: '确认订单数', key: 'day_ensure_order' },
    {
      parent: '当日',
      title: '发确认件订单数',
      key: 'day_send_ensure_order',
    },
    { parent: '累计', title: '已支付订金订单数', key: 'history_add_order' },
    { parent: '累计', title: '已确认订单数', key: 'history_ensure_order' },

    {
      parent: '累计',
      title: '已发确认件订单数',
      key: 'history_send_ensure_order',
    },
    {
      parent: '累计',
      title: '已出行订单数',
      key: 'history_travel_order',
    },
    {
      parent: '累计',
      title: '已核销订单数',
      key: 'history_travel_back_order',
    },
    {
      parent: '目标',
      title: '每日需核销单数',
      key: 'avg_day_need_hand_order',
    },
  ];

  const columns = ref<any[]>([]);
  const listData = ref<any[]>([]);

  function dataSpanMethod({ record, rowIndex, columnIndex }: any) {
    if (columnIndex === 0) {
      if (
        listData.value.findIndex((item) => item.parent === record.parent) ===
        rowIndex
      ) {
        return {
          rowspan: listData.value.filter(
            (item) => item.parent === record.parent
          ).length,
        };
      }
      return {
        rowspan: 0,
      };
    }
  }
  const getList = async (data: any) => {
    return new Promise((resolve, reject) => {
      request('/api/report/serviceUserDayReport', {
        ...data,
      }).then((res) => {
        let list: any = cloneDeep(fields);
        let userList: any = [
          { title: '类型', dataIndex: 'parent' },
          { title: '指标', dataIndex: 'title' },
        ];
        res.data.forEach((item: any) => {
          userList.push({
            title: item.service_user,
            dataIndex: item.service_user_fs_id,
          });
          list.forEach((fitem: any, index: number) => {
            list[index][item.service_user_fs_id] = item[fitem.key];
          });
        });

        columns.value = userList;
        listData.value = list;
        resolve({
          data: list,
        });
      });
    });
  };

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less"></style>
