<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="handleSubmit()"
      >
        <template #formItemGroup>
          <a-form-item label="日期">
            <a-input-group class="w100p">
              <c-range-picker v-model="formModel.date" :allow-clear="false" />
              <a-checkbox
                :model-value="formModel.group_by.includes('accept_date')"
                @change="changeGroupFields('accept_date')"
              />
            </a-input-group>
          </a-form-item>

          <a-form-item label="旅游线路">
            <a-input-group class="w100p">
              <dict-select
                v-model="formModel.area"
                :max-tag-count="4"
                :data-list="dataCacheStore.lineList"
                label-key="line_name"
                value-key="line_name"
              />
              <a-checkbox
                :model-value="formModel.group_by.includes('area')"
                @change="changeGroupFields('area')"
              />
            </a-input-group>
          </a-form-item>

          <a-form-item label="订单来源">
            <a-input-group class="w100p">
              <request-select
                v-model="formModel.order_source"
                request-url="/api/report/liveRoomList"
                :max-tag-count="3"
                :get-data-list="
                  (data:any[]) => [
                    { live_room_name: '内容私域' },
                    { live_room_name: '直播间私域' },
                    ...data,
                  ]
                "
                label-key="live_room_name"
                value-key="live_room_name"
                allow-clear
              />
              <a-checkbox
                :model-value="formModel.group_by.includes('order_source')"
                @change="changeGroupFields('order_source')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="直播间类型">
            <a-input-group class="w100p">
              <dict-select
                v-model="formModel.live_room_type"
                :max-tag-count="4"
                :data-list="liveRoomTypeM"
              />
              <a-checkbox
                :model-value="formModel.group_by.includes('live_room_type')"
                @change="changeGroupFields('live_room_type')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="直播间">
            <a-input-group class="w100p">
              <request-select
                v-model="formModel.live_room_id"
                api="live_room"
                :max-tag-count="4"
                :send-params="{ type: formModel.live_room_type }"
              />
              <a-checkbox
                :model-value="formModel.group_by.includes('live_room_id')"
                @change="changeGroupFields('live_room_id')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="直播运营">
            <a-input-group class="w100p">
              <request-select
                v-model="formModel.user_role_10"
                api="operate"
                :max-tag-count="4"
              />
              <a-checkbox
                :model-value="formModel.group_by.includes('user_role_10')"
                @change="changeGroupFields('user_role_10')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="电销">
            <a-input-group class="w100p">
              <request-select
                v-model="formModel.user_role_3"
                api="user"
                :send-params="{ roles: [3] }"
                :max-tag-count="4"
              />
              <a-checkbox
                :model-value="formModel.group_by.includes('user_role_3')"
                @change="changeGroupFields('user_role_3')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="全职主播">
            <a-input-group class="w100p">
              <request-select
                v-model="formModel.user_role_7"
                api="user"
                :send-params="{ roles: [7] }"
                :max-tag-count="4"
              />
              <a-checkbox
                :model-value="formModel.group_by.includes('user_role_7')"
                @change="changeGroupFields('user_role_7')"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="内部KOC主播">
            <a-input-group class="w100p">
              <request-select
                v-model="formModel.user_role_8"
                api="user"
                :send-params="{ roles: [8] }"
                :max-tag-count="4"
              />
              <a-checkbox
                :model-value="formModel.group_by.includes('user_role_8')"
                @change="changeGroupFields('user_role_8')"
              />
            </a-input-group>
          </a-form-item>
        </template>
      </search-form-fold>
      <div class="table-card-header mt-20">
        <div>
          <a-space> </a-space>
        </div>
        <a-space>
          <columns-select
            :the-field-map="allFieldsConfig"
            :need-format-column="false"
            :send-params="{ type: 'order_refund_report' }"
            :default-columns="columnsConfig.map((item:any) => item.dataIndex)"
            @select-columns-ok="setColumns"
          ></columns-select>
          <a-button type="primary" @click="exportAction">
            <template #icon>
              <icon-export />
            </template>
            导出
          </a-button>
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :auto-request="false"
        :send-params="tableParams"
        :scroll-percent="scrollPercent"
      >
      </base-table>
    </a-card>
    <live-room-cost-modal ref="editRef" @save="handleSubmit()" />
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive, computed } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import ColumnsSelect from '@/components/columns-select/columns-select.vue';
  import { cloneDeep } from 'lodash';
  import {
    moneyFormatShow,
    rateFormatShow,
  } from '@/utils/table-utils/columns-config';
  import dayjs from 'dayjs';
  import LiveRoomCostModal from '@/views/travel-order/live-room-report/live-room-cost-modal.vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import { useDataCacheStore } from '@/store';
  import { liveRoomTypeM } from '@/components/dict-select/dict-common';

  const dataCacheStore = useDataCacheStore();
  const generateFormModel = () => {
    return {
      date: [
        dayjs().add(-6, 'day').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      group_by: ['accept_date'] as string[],
      area: [],
      order_source: [],
      live_room_id: [],
      live_room_type: '',
      user_role_3: [],
      user_role_10: [],
      user_role_7: [],
      user_role_8: [],
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const editRef = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    return request('/api/report/refundReport', {
      ...data,
    });
  };

  const allFieldsConfig = [
    {
      title: '全部字段',
      dataIndex: '全部字段',
      keys: [] as string[],
      dataList: [
        {
          title: '成交订单数',
          dataIndex: 'order_num',
        },
        {
          title: '退款数',
          dataIndex: 'refund_num',
        },
        {
          title: '退单率',
          dataIndex: 'refund_rate',
          description: '退款数/成交订单数*100%',
          render: rateFormatShow(),
        },
        {
          title: '秒拍秒退数',
          dataIndex: 'quick_refund',
          description:
            '秒拍秒退的订单为抖音来客【酒旅销售明细】存在且已退款而【预约管理】中不存在的订单',
        },
        {
          title: '秒拍秒退比率',
          dataIndex: 'quick_refund_rate',
          description: '秒拍秒退数/成交订单数*100%',
          render: rateFormatShow(),
        },
        {
          title: '1小时内退款数',
          dataIndex: 'hour_0_1_refund',
          description: '退款时间与订单创建时间间隔在1小时以内',
        },
        {
          title: '1小时内退款率',
          dataIndex: 'hour_0_1_refund_rate',
          description: '1小时内退款数/成交订单数',
          render: rateFormatShow(),
        },
        {
          title: '1-24小时退款数',
          dataIndex: 'hour_1_24_refund',
          description: '退款时间与订单创建时间间隔在1小时至24小时之间',
        },
        {
          title: '1-24小时退款率',
          dataIndex: 'hour_1_24_refund_rate',
          description: '1至24小时退款数/成交订单数*100%',
          render: rateFormatShow(),
        },
        {
          title: '1-3天退款数',
          dataIndex: 'hour_24_72_refund',
          description: '退款时间与订单创建时间间隔在1天至3天之间',
        },
        {
          title: '1-3天退款率',
          dataIndex: 'hour_24_72_refund_rate',
          description: '1至3天退款数/成交订单数*100%',
          render: rateFormatShow(),
        },
        {
          title: '3-7天退款数',
          dataIndex: 'hour_72_168_refund',
          description: '退款时间与订单创建时间间隔在3天至7天之间',
        },
        {
          title: '3-7天退款率',
          dataIndex: 'hour_72_168_refund_rate',
          description: '3至7天退款数/成交订单数*100%',
          render: rateFormatShow(),
        },
        {
          title: '超7天退款数',
          dataIndex: 'hour_168_order',
          description: '退款时间与订单创建时间间隔超过7*24h',
        },
        {
          title: '超7天退款率',
          dataIndex: 'hour_168_order_rate',
          description: '超7天退款数/成交订单数*100%',
          render: rateFormatShow(),
        },
        {
          title: '超7天未退款未确认订单数',
          dataIndex: 'hour_168_order',
          description: '订单创建超过7天，订单状态为’已支付订金’',
        },
        {
          title: '超7天未退款未确认订单率',
          dataIndex: 'hour_168_order_rate',
          description: '超7天未退款未确认订单数/成交订单数*100%',
          render: rateFormatShow(),
        },
      ],
    },
  ];
  allFieldsConfig.forEach((item) => {
    item.keys = item.dataList.map((citem) => {
      citem.key = citem.dataIndex;
      return citem.dataIndex;
    });
  });
  const columnsConfig = ref(cloneDeep(allFieldsConfig[0].dataList));

  function getGroupCols() {
    let arr: any[] = [];

    if (formModel.group_by.includes('accept_date')) {
      arr.push({
        title: '日期',
        dataIndex: 'accept_date',
        width: 110,
      });
    }
    if (formModel.group_by.includes('area')) {
      arr.push({
        title: '旅游线路',
        dataIndex: 'area',
        width: 200,
      });
    }
    if (formModel.group_by.includes('order_source')) {
      arr.push({
        title: '订单来源',
        dataIndex: 'order_source',
        width: 110,
      });
    }
    if (formModel.group_by.includes('live_room_type')) {
      arr.push({
        title: '直播间类型',
        dataIndex: 'live_room_type',
        width: 110,
      });
    }
    if (formModel.group_by.includes('live_room_id')) {
      arr.push({
        title: '直播间',
        dataIndex: 'live_name',
        width: 200,
      });
    }
    if (formModel.group_by.includes('user_role_10')) {
      arr.push({
        title: '直播运营',
        dataIndex: 'user_role_10',
        width: 110,
      });
    }
    if (formModel.group_by.includes('user_role_3')) {
      arr.push({
        title: '电销',
        dataIndex: 'user_role_3',
        width: 110,
      });
    }
    if (formModel.group_by.includes('user_role_7')) {
      arr.push({
        title: '全职主播',
        dataIndex: 'user_role_7',
        width: 110,
      });
    }
    if (formModel.group_by.includes('user_role_8')) {
      arr.push({
        title: '内部KOC主播',
        dataIndex: 'user_role_8',
        width: 110,
      });
    }
    if (arr[0]) {
      arr[0].fixed = 'left';
    }
    return arr;
  }

  const columns = computed(() => [...getGroupCols(), ...columnsConfig.value]);

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function changeGroupFields(key: string) {
    if (!formModel.group_by.includes(key)) {
      formModel.group_by.push(key);
    } else {
      formModel.group_by = formModel.group_by.filter((item) => item !== key);
    }
    if (!formModel.group_by.length) {
      formModel.group_by.push('accept_date');
    }
    handleSubmit();
  }

  function setColumns(val: any[]) {
    columnsConfig.value = val.filter((item) =>
      allFieldsConfig.some((citem) => citem.keys.includes(item.dataIndex))
    );
    handleSubmit();
  }

  const tableParams = computed(() => {
    return {
      ...formModel,
      start_date: formModel.date?.[0],
      end_date: formModel.date?.[1],
      group_by: formModel.group_by.length
        ? formModel.group_by
        : ['accept_date'],
    };
  });
  const scrollPercent = computed(() => ({
    maxHeight: '70vh',
    x: columns.value.length * 150,
  }));

  function exportAction() {
    theTable.value?.exportTable({ export_now: true });
  }
</script>

<style scoped lang="less"></style>
