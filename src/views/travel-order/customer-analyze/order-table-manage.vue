<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="handleSubmit()"
      >
        <template #formItemGroup>
          <a-form-item label="创建时间">
            <c-range-picker
              v-model="formModel.order_create_time"
              class="w100p"
              :allow-clear="false"
              @change="handleSubmit"
            />
          </a-form-item>
          <a-form-item label="分配时间">
            <c-range-picker
              v-model="formModel.time_distribute"
              class="w100p"
              :allow-clear="true"
              @change="handleSubmit"
            />
          </a-form-item>
          <!-- 旅游线路 -->
          <a-form-item label="旅游线路">
            <a-input-group class="w100p">
              <dict-select
                v-model="formModel.area"
                :data-list="dataCacheStore.lineList"
                label-key="line_name"
                value-key="line_name"
                @change="handleSubmit"
              />
              <a-checkbox
                :model-value="formModel.checkConfig.includes('area')"
                @change="changeGroupFields('area')"
              />
            </a-input-group>
          </a-form-item>

          <a-form-item label="来源媒体">
            <a-input-group class="w100p">
              <dict-select
                v-model="formModel.source"
                :data-list="contactWayListM"
                @change="handleSubmit"
              />
              <a-checkbox
                :model-value="formModel.checkConfig.includes('source')"
                @change="changeGroupFields('source')"
              />
            </a-input-group>
          </a-form-item>

          <a-form-item label="客服所属部门">
            <a-input-group class="w100p">
              <request-tree-select
                v-model="formModel.department_id"
                request-url="/api/department/list"
                label-key="department_name"
                child-key="child"
                multiple
                :max-tag-count="2"
                @change="handleSubmit"
              />
              <a-checkbox
                :model-value="formModel.checkConfig.includes('department_id')"
                @change="changeGroupFields('department_id')"
              />
            </a-input-group>
          </a-form-item>

          <a-form-item label="客服">
            <a-input-group class="w100p">
              <request-select
                v-model="formModel.service_user_fs_id"
                request-url="/api/travel/allServiceUser"
                label-key="user_name"
                value-key="fs_user_id"
                :send-params="{
                  no_has_dimission: true,
                }"
                @blur="handleSubmit"
              />
              <a-checkbox
                :model-value="
                  formModel.checkConfig.includes('service_user_fs_id')
                "
                @change="changeGroupFields('service_user_fs_id')"
              />
            </a-input-group>
          </a-form-item>

          <a-form-item label="订单类型">
            <a-input-group class="w100p">
              <request-select
                v-model="formModel.order_new_type"
                request-url="/api/travel/orderMap"
                label-key="name"
                value-key="id"
                :send-params="{
                  module: 'order_new_type',
                }"
                @change="changeOrderType"
              />
              <a-checkbox
                :model-value="formModel.checkConfig.includes('order_new_type')"
                @change="changeGroupFields('order_new_type')"
              />
            </a-input-group>
          </a-form-item>

          <a-form-item label="订单细分类型">
            <a-input-group class="w100p">
              <request-select
                v-model="formModel.order_new_type_detail"
                request-url="/api/travel/orderMap"
                label-key="combine"
                value-key="combine"
                :send-params="{
                  module: 'order_new_type_collect',
                }"
                @change="changeOrderTypeDetail"
              />
              <a-checkbox
                :model-value="
                  formModel.checkConfig.includes('order_new_type_detail')
                "
                @change="changeGroupFields('order_new_type_detail')"
              />
            </a-input-group>
          </a-form-item>
        </template>
      </search-form-fold>
      <div class="table-card-header mt-20">
        <div>
          <a-space> </a-space>
        </div>
        <a-space>
          <columns-select
            :the-field-map="allFieldsConfig"
            :need-format-column="false"
            :send-params="{ type: 'travel_customer_analyze_data' }"
            :default-columns="columnsConfig.map((item) => item.dataIndex)"
            @select-columns-ok="setColumns"
          ></columns-select>
          <!-- <a-button type="primary" @click="exportAction">
            <template #icon>
              <icon-export />
            </template>
            导出
          </a-button> -->
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :auto-request="false"
        :send-params="tableParams"
        :scroll-percent="scrollPercent"
        :sort-keys="sortKeys"
      >
      </base-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive, computed } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import { TableColumnSlot } from '@/global';
  import ColumnsSelect from '@/components/columns-select/columns-select.vue';
  import { cloneDeep } from 'lodash';
  import {
    moneyFormatShow,
    rateFormatShow,
  } from '@/utils/table-utils/columns-config';
  import dayjs from 'dayjs';
  import LiveRoomCostModal from '@/views/travel-order/live-room-report/live-room-cost-modal.vue';
  import { moneyFormat } from '@/utils/table-utils/table-util';
  import RequestSelect from '@/components/select/request-select.vue';
  import { useDataCacheStore } from '@/store';
  import {
    contactWayListM,
    payTypeM,
  } from '@/components/dict-select/dict-travel';
  import { isFlowM } from '@/components/dict-select/dict-clue';
  import requestTreeSelect from '@/components/select/request-tree-select.vue';

  const dataCacheStore = useDataCacheStore();
  const generateFormModel = () => {
    return {
      checkConfig: ['area', 'source', 'department_id'],
      area: [],
      source: [],
      department_id: [],
      service_user_fs_id: [],
      order_new_type: [],
      order_new_type_detail: [],
      // 默认本月
      order_create_time: [
        dayjs().startOf('month').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      time_distribute: [],
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    return request('/api/report/customerServiceEfficiencyOrder', {
      ...data,
    });
  };

  const allFieldsConfig = [
    {
      title: '全部字段',
      dataIndex: '全部字段',
      keys: [] as string[],
      dataList: [
        // 客资数
        {
          title: '客资数',
          dataIndex: 'customer_num',
        },
        // 有效建联数
        {
          title: '有效建联数',
          dataIndex: 'effective_contact_num',
        },
        // 有效建联率
        {
          title: '有效建联率',
          dataIndex: 'effective_contact_rate',
        },
        // 确认件数
        {
          title: '确认件数',
          dataIndex: 'confirm_num',
        },
        // 确认件人数
        {
          title: '确认件人数',
          dataIndex: 'confirm_people_num',
        },
        // 确认件率
        {
          title: '确认件率',
          dataIndex: 'confirm_rate',
        },
        // 总团款
        {
          title: '总团款',
          dataIndex: 'total_cut_price',
          render: moneyFormatShow(),
        },
        // 结算价
        {
          title: '结算价',
          dataIndex: 'total_final_price',
          render: moneyFormatShow(),
        },
        // 毛利
        {
          title: '毛利',
          dataIndex: 'profit',
          render: moneyFormatShow(),
        },
        // 均单毛利
        {
          title: '均单毛利',
          dataIndex: 'avg_order_profit',
          render: moneyFormatShow(),
        },
        // 人均毛利
        {
          title: '人均毛利',
          dataIndex: 'avg_people_profit',
          render: moneyFormatShow(),
        },
        // 退确认件数
        {
          title: '退确认件数',
          dataIndex: 'refund_confirm_num',
        },
        // 退确认件率
        {
          title: '退确认件率',
          dataIndex: 'refund_confirm_rate',
        },
      ],
    },
  ];
  allFieldsConfig.forEach((item) => {
    item.keys = item.dataList.map((citem) => {
      citem.key = citem.dataIndex;
      return citem.dataIndex;
    });
  });

  const sortKeys = allFieldsConfig[0].dataList.map((item) => item.dataIndex);

  const columnsConfig = ref(cloneDeep(allFieldsConfig[0].dataList));

  // 列配置映射
  const columnConfigMap = {
    area: {
      title: '旅游线路',
      dataIndex: 'area',
    },
    source: {
      title: '来源媒体',
      dataIndex: 'source',
    },
    // 客服所属部门
    department_id: {
      title: '客服所属部门',
      dataIndex: 'department_name',
    },
    service_user_fs_id: {
      title: '客服',
      dataIndex: 'service_user_name',
    },
    order_new_type: {
      title: '订单类型',
      dataIndex: 'order_new_type',
    },
    // 订单细分类型
    order_new_type_detail: {
      title: '订单细分类型',
      dataIndex: 'order_new_type_detail',
    },
  };

  // 构建动态列
  const buildDynamicColumns = () => {
    const dynamicColumns = [];

    // 根据 checkConfig 添加对应的列
    formModel.checkConfig.forEach((configKey) => {
      if (columnConfigMap[configKey]) {
        dynamicColumns.push(columnConfigMap[configKey]);
      }
    });

    return dynamicColumns;
  };

  const columns = computed(() => {
    // 动态列
    const dynamicColumns = buildDynamicColumns();

    // 配置列
    const configColumns = columnsConfig.value;

    // 合并所有列
    const allColumns = [...dynamicColumns, ...configColumns];

    // 应用过滤
    return allColumns;
  });

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  const changeOrderType = (val: any, recordArr: any) => {
    formModel.order_new_type_detail = [];
    handleSubmit();
  };

  const changeOrderTypeDetail = (val: any, recordArr: any) => {
    if (val) {
      recordArr.forEach((record) => {
        if (!formModel.order_new_type.includes(record.order_new_type)) {
          formModel.order_new_type.push(record.order_new_type);
        }
      });
    }
    handleSubmit();
  };

  function changeGroupFields(key: string) {
    if (!formModel.checkConfig.includes(key)) {
      formModel.checkConfig.push(key);
    } else {
      formModel.checkConfig = formModel.checkConfig.filter(
        (item) => item !== key
      );
    }
    // 如果勾选了线索细分类型，则需要展示线索类型的列
    handleSubmit();
  }

  function setColumns(val: any[]) {
    columnsConfig.value = val.filter((item) =>
      allFieldsConfig.some((citem) => citem.keys.includes(item.dataIndex))
    );
    handleSubmit();
  }

  const tableParams = computed(() => {
    return {
      ...formModel,
    };
  });
  const scrollPercent = computed(() => ({
    maxHeight: '70vh',
    x: columns.value.length * 150,
  }));

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less"></style>
