<template>
  <div>
    <a-radio-group
      v-model="formModel.type"
      type="button"
      @change="handleSubmit()"
    >
      <a-radio value="1">线索</a-radio>
      <a-radio value="2">订单</a-radio>
    </a-radio-group>

    <clue-table-manage v-if="formModel.type === '1'" />
    <order-table-manage v-if="formModel.type === '2'" />
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import ClueTableManage from './clue-table-manage.vue';
  import OrderTableManage from './order-table-manage.vue';

  const formModel = ref({
    type: '1',
  });
</script>

<style scoped lang="less"></style>
