<template>
  <d-modal
    v-model:visible="visible"
    width="500px"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    :mask-closable="false"
    :esc-to-close="false"
    :title="`${formModel.id ? '编辑' : '添加'}直播间`"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formModel" :auto-label-width="true">
      <a-form-item label="平台" field="platform" :rules="requiredRule">
        <dict-select
          v-model="formModel.platform"
          :data-list="contactWayListM"
        />
      </a-form-item>
      <a-form-item label="直播间名称" field="live_name" :rules="requiredRule">
        <a-input v-model="formModel.live_name" />
      </a-form-item>
      <a-form-item
        label="直播账号"
        field="live_account_id"
        :rules="requiredRule"
      >
        <a-input v-model="formModel.live_account_id" />
      </a-form-item>
      <a-form-item label="负责人" field="manager_user_id" :rules="requiredRule">
        <request-select
          v-model="formModel.manager_user_id"
          request-url="/api/user/userList"
          label-key="user_name"
          :send-params="{ pageSize: 999999 }"
        />
      </a-form-item>
      <a-form-item label="直播间类型" field="type" :rules="requiredRule">
        <dict-select v-model="formModel.type" :data-list="liveRoomTypeM" />
      </a-form-item>
      <a-form-item
        v-if="formModel.type === 'without'"
        label="经纪人"
        field="broker_user_id"
        :rules="requiredRule"
      >
        <request-select
          v-model="formModel.broker_user_id"
          request-url="/api/user/userList"
          label-key="user_name"
          :send-params="{ pageSize: 999999, roles: [9] }"
        />
      </a-form-item>
      <a-form-item
        v-else
        label="直播运营"
        field="operate_user_id"
        :rules="requiredRule"
      >
        <request-select v-model="formModel.operate_user_id" api="operate" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { requiredRule, requiredRuleArr } from '@/utils/util';
  import {
    contactWayListM,
    customerServiceStatusListM,
  } from '@/components/dict-select/dict-travel';
  import RequestSelect from '@/components/select/request-select.vue';
  import { liveRoomTypeM } from '@/components/dict-select/dict-common';

  const defaultForm = () => ({
    id: null,
    platform: contactWayListM[0].value,
    live_name: '',
    live_account_id: '',
    manager_user_id: '',
    type: liveRoomTypeM[0].value,
    operate_user_id: '',
    broker_user_id: '',
    state: 1,
  });

  const emit = defineEmits(['save']);

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const formModel = ref(defaultForm());
  const validate = () => {
    return formRef.value?.validate();
  };
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  const show = (dinfo: any) => {
    let initForm = defaultForm();
    formModel.value = initForm;
    if (dinfo) {
      Object.keys(initForm).forEach((key) => {
        // @ts-ignore
        formModel.value[key] =
          dinfo[key] || initForm[key as keyof typeof initForm];
      });
    }

    clearValidate();
    visible.value = true;
    loading.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  const handleBeforeOk = async () => {
    const result = await validate();
    if (!result) {
      loading.value = true;
      request('/api/liveRoom/save', {
        ...formModel.value,
      })
        .then(() => {
          Message.success('保存成功');
          handleCancel();
          emit('save');
        })
        .finally(() => {
          loading.value = false;
        });
    }
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>
