<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        :continue-key="['live_room_id']"
        @search="handleSubmit()"
      >
        <template #formItemGroup>
          <a-form-item label="日期">
            <a-range-picker
              v-model="formModel.date"
              class="w100p"
              allow-clear
            />
          </a-form-item>
          <a-form-item label="时间">
            <dict-select
              v-model="formModel.start_hour"
              class="table-search-input-mini"
              :data-list="timeList"
              @change="
                formModel.end_hour =
                  formModel.end_hour < formModel.start_hour
                    ? ''
                    : formModel.end_hour
              "
            ></dict-select>
            <span> ~ </span>
            <dict-select
              v-model="formModel.end_hour"
              :data-list="timeEndList"
              class="table-search-input-mini"
            ></dict-select>
          </a-form-item>
          <a-form-item label="直播间">
            <request-select
              v-model="formModel.live_room_id"
              request-url="/api/liveRoom/map"
              select-first
              label-key="live_name"
              :allow-clear="false"
              @change="handleSubmit()"
            />
          </a-form-item>
          <a-form-item label="主播">
            <request-select
              v-model="formModel.anchor_user"
              request-url="/api/user/userList"
              label-key="user_name"
              :send-params="{ pageSize: 999999, show_empty_flag: true }"
            />
          </a-form-item>
          <a-form-item label="助播">
            <request-select
              v-model="formModel.assistant_user"
              request-url="/api/user/userList"
              label-key="user_name"
              :send-params="{ pageSize: 999999 }"
            />
          </a-form-item>
          <a-form-item label="直播运营">
            <request-select
              v-model="formModel.operate_user"
              request-url="/api/user/userList"
              label-key="user_name"
              :send-params="{ pageSize: 999999 }"
            />
          </a-form-item>
        </template>
      </search-form-fold>

      <div class="table-card-header mt-10">
        <div>
          <!-- 批量操作按钮 -->
          <a-space v-if="selectedRows.length > 0">
            <a-button type="outline" @click="showBatchAnchorEdit">
              <template #icon>
                <icon-edit />
              </template>
              批量修改主播 ({{ selectedRows.length }})
            </a-button>
            <a-button type="text" @click="clearAllSelection">
              <template #icon>
                <icon-close />
              </template>
              取消选择
            </a-button>
          </a-space>
        </div>
        <a-space>
          <a-button type="primary" @click="showEditFn()">
            <template #icon>
              <icon-upload />
            </template>
            导入
          </a-button>
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formParams"
        :dpagination="{
          showPageSize: false,
          pageSize: 24,
          showPageSize: true,
          pageSizeOptions: [24, 48, 240, 480],
        }"
        :row-selection="rowSelection"
        @select-change="selectionChange"
      >
        <template #anchor_user_name="{ record }: TableColumnSlot">
          <anchor-edit-cell
            :record="record"
            :anchor-list="anchorList"
            @save="handleAnchorSave"
          />
        </template>
      </base-table>
    </a-card>
    <liveroom-schedule-import ref="importRef" @refresh="handleSubmit()" />

    <!-- 批量修改主播弹窗 -->
    <a-modal
      v-model:visible="batchEditVisible"
      title="批量修改主播"
      width="400px"
      @ok="handleBatchSave"
      @cancel="handleBatchCancel"
    >
      <a-form layout="vertical" :model="{ batchAnchorId }">
        <a-form-item label="选择主播">
          <a-select
            v-model="batchAnchorId"
            placeholder="请选择主播"
            :loading="anchorListLoading"
            allow-clear
            allow-search
          >
            <a-option
              v-for="anchor in anchorList"
              :key="anchor.id"
              :value="anchor.id"
            >
              {{ anchor.user_name }}
            </a-option>
          </a-select>
        </a-form-item>
        <!-- <a-form-item>
          <div class="batch-info">
            <p>将为以下 {{ selectedRows.length }} 个直播间排班修改主播：</p>
            <div class="selected-items">
              <a-tag
                v-for="row in selectedRows.slice(0, 30)"
                :key="row.id"
                size="small"
              >
                {{ row.live_name }} - {{ row.accept_date }}
                {{ row.accept_hour }}
              </a-tag>
              <span v-if="selectedRows.length > 30">
                等 {{ selectedRows.length }} 项
              </span>
            </div>
          </div>
        </a-form-item> -->
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive, computed, onMounted } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import dayjs from 'dayjs';
  import LiveroomScheduleImport from '@/views/manage/liveroom-manage/liveroom-schedule-import.vue';
  import { stringArrShow } from '@/utils/table-utils/columns-config';
  import { useRoute } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { TableColumnSlot } from '@/global';
  import AnchorEditCell from './components/anchor-edit-cell.vue';

  const timeList = ref(
    Array.from({ length: 24 }, (item, index) => {
      return {
        label: index > 9 ? `${index}:00` : `0${index}:00`,
        value: index,
      };
    })
  );

  const generateFormModel = () => {
    return {
      live_room_id: '' as string | number,
      anchor_user: [],
      assistant_user: [],
      operate_user: [],
      date: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      start_hour: null,
      end_hour: null,
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const timeEndList = computed(() => {
    return [
      ...timeList.value.filter(
        (item) => item.value > (formModel.start_hour || 0)
      ),
      {
        label: `24:00`,
        value: 24,
      },
    ];
  });
  const route = useRoute();
  formModel.live_room_id = route.query.live_room_id
    ? parseInt(route.query.live_room_id, 10)
    : '';

  const formParams = computed(() => {
    return {
      ...formModel,
      add_time_begin: formModel.date?.[0],
      add_time_end: formModel.date?.[1],
    };
  });

  const getList = async (data: any) => {
    return request('/api/liveRoom/schedule', {
      ...data,
    });
  };

  const importRef = ref();
  function showEditFn() {
    importRef.value?.show();
  }

  // 主播列表相关
  const anchorList = ref<any[]>([]);
  const anchorListLoading = ref(false);

  // 获取主播列表
  const fetchAnchorList = async () => {
    try {
      anchorListLoading.value = true;
      const response = await request('/api/user/userList', {
        pageSize: 999999,
        roles: [7, 8],
      });
      anchorList.value = response.data?.data || [];
    } catch (error) {
      console.error('获取主播列表失败:', error);
      Message.error('获取主播列表失败');
    } finally {
      anchorListLoading.value = false;
    }
  };

  // 行选择相关
  const selectedRows = ref<any[]>([]);
  const rowSelection = reactive({
    type: 'checkbox',
    fixed: true,
    width: 30,
    onlyCurrent: true,
    showCheckedAll: true,
    selectedRowKeys: [] as any[],
    selectedRows: [] as any[],
  });

  const selectionChange = (selectedRowKeys: any[], selectedRowsData: any[]) => {
    rowSelection.selectedRowKeys = selectedRowKeys.filter((item: any) => item);
    rowSelection.selectedRows = selectedRowsData;
    selectedRows.value = selectedRowsData;
  };

  // 清空所有选择
  const clearAllSelection = () => {
    selectedRows.value = [];
    rowSelection.selectedRowKeys = [];
    rowSelection.selectedRows = [];
  };

  // 批量修改相关
  const batchEditVisible = ref(false);
  const batchAnchorId = ref<number | null>(null);

  const showBatchAnchorEdit = () => {
    if (selectedRows.value.length === 0) {
      Message.warning('请先选择要修改的排班记录');
      return;
    }
    batchEditVisible.value = true;
    batchAnchorId.value = null;
  };

  const handleBatchCancel = () => {
    batchEditVisible.value = false;
    batchAnchorId.value = null;
  };

  const columns = [
    {
      title: '直播间',
      dataIndex: 'live_name',
    },
    {
      title: '日期',
      dataIndex: 'accept_date',
    },
    {
      title: '时间',
      dataIndex: 'accept_hour',
    },
    {
      title: '主播',
      dataIndex: 'anchor_user_name',
      slotName: 'anchor_user_name',
      width: 220,
    },
    {
      title: '助播',
      dataIndex: 'assistant_user_names',
      render: stringArrShow(),
    },
    {
      title: '直播运营',
      dataIndex: 'operate_user_names',
      render: stringArrShow(),
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
    // 清空选择
    selectedRows.value = [];
    rowSelection.selectedRowKeys = [];
    rowSelection.selectedRows = [];
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }

  // 单个主播保存
  const handleAnchorSave = async (record: any, anchorUserId: number) => {
    try {
      const params = {
        ids: [record.id],
        live_room_id: record.live_room_id,
        anchor_user: anchorUserId,
      };

      await request('/api/liveRoom/setAnchor', params);
      Message.success('主播修改成功');

      // 刷新表格数据
      theTable.value?.search();
    } catch (error) {
      console.error('修改主播失败:', error);
      Message.error('修改主播失败');
      throw error;
    }
  };

  // 批量保存主播
  const handleBatchSave = async () => {
    // if (!batchAnchorId.value) {
    //   Message.warning('请选择主播');
    //   return;
    // }

    try {
      loading.value = true;

      const params = {
        ids: selectedRows.value.map((row) => row.id),
        live_room_id: selectedRows.value[0]?.live_room_id, // 假设同一批次的直播间ID相同
        anchor_user: batchAnchorId.value,
      };

      await request('/api/liveRoom/setAnchor', params);
      Message.success(`成功修改 ${selectedRows.value.length} 个排班的主播`);

      // 关闭弹窗
      handleBatchCancel();

      // 刷新表格数据
      theTable.value?.search();

      // 清空选择
      selectedRows.value = [];
      rowSelection.selectedRowKeys = [];
      rowSelection.selectedRows = [];
    } catch (error) {
      console.error('批量修改主播失败:', error);
      Message.error('批量修改主播失败');
    } finally {
      loading.value = false;
    }
  };

  // 组件挂载时获取主播列表
  onMounted(() => {
    fetchAnchorList();
  });
</script>

<style scoped lang="less">
  .batch-info {
    p {
      margin-bottom: 8px;
      color: var(--color-text-2);
    }

    .selected-items {
      max-height: 200px;
      overflow-y: auto;
      padding: 8px;
      background-color: var(--color-fill-1);
      border-radius: 4px;

      .arco-tag {
        margin: 2px 4px 2px 0;
      }
    }
  }
</style>
