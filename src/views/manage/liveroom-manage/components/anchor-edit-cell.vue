<template>
  <div class="anchor-edit-cell">
    <!-- 显示模式 -->
    <div v-if="!isEditing" class="display-mode">
      <span class="anchor-name">{{ displayName }}</span>
      <a-button type="text" size="mini" class="edit-btn" @click="startEdit">
        <template #icon>
          <icon-edit />
        </template>
      </a-button>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="edit-mode">
      <a-select
        ref="selectRef"
        v-model="selectedAnchorId"
        placeholder="请选择主播"
        size="small"
        class="anchor-select"
        :loading="loading"
        allow-clear
        allow-search
        @change="handleSelectChange"
      >
        <a-option
          v-for="anchor in anchorList"
          :key="anchor.id"
          :value="anchor.id"
        >
          {{ anchor.user_name }}
        </a-option>
      </a-select>

      <div class="edit-actions">
        <a-button
          type="primary"
          size="mini"
          :loading="saving"
          @click="handleSave"
        >
          <template #icon>
            <icon-check />
          </template>
        </a-button>
        <a-button size="mini" @click="handleCancel">
          <template #icon>
            <icon-close />
          </template>
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, nextTick } from 'vue';
  import { Message } from '@arco-design/web-vue';

  interface Props {
    record: any;
    anchorList: any[];
  }

  interface Emits {
    (e: 'save', record: any, anchorUserId: number): Promise<void>;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  // 状态管理
  const isEditing = ref(false);
  const selectedAnchorId = ref<number | null>(null);
  const saving = ref(false);
  const loading = ref(false);
  const selectRef = ref();

  // 计算属性
  const displayName = computed(() => {
    return props.record.anchor_user_name || '-';
  });

  // 开始编辑
  const startEdit = async () => {
    if (isEditing.value) return;

    isEditing.value = true;
    // 设置当前主播ID
    const currentAnchor = props.anchorList.find(
      (anchor) => anchor.user_name === props.record.anchor_user_name
    );
    selectedAnchorId.value = currentAnchor?.id || null;

    await nextTick();
    // 尝试聚焦到选择框
    try {
      if (selectRef.value && typeof selectRef.value.focus === 'function') {
        selectRef.value.focus();
      }
    } catch (error) {
      console.warn('无法聚焦到选择框:', error);
    }
  };

  // 处理选择变化
  const handleSelectChange = (value: number) => {
    selectedAnchorId.value = value;
  };

  // 保存
  const handleSave = async () => {
    // if (!selectedAnchorId.value) {
    //   Message.warning('请选择主播');
    //   return;
    // }

    if (saving.value) return;

    try {
      saving.value = true;

      // 调用父组件的保存方法，父组件负责更新数据
      await emit('save', props.record, selectedAnchorId.value);

      // 结束编辑
      isEditing.value = false;
    } catch (error) {
      console.error('保存失败:', error);
      // 错误处理已在父组件中完成
    } finally {
      saving.value = false;
    }
  };

  // 取消编辑
  const handleCancel = () => {
    isEditing.value = false;
    selectedAnchorId.value = null;
  };
</script>

<style scoped lang="less">
  .anchor-edit-cell {
    width: 100%;

    .display-mode {
      display: flex;
      align-items: center;
      gap: 8px;

      .anchor-name {
        flex: 1;
        min-width: 0;
        word-break: break-all;
      }

      .edit-btn {
        flex-shrink: 0;
        opacity: 0.6;
        transition: opacity 0.2s ease;

        &:hover {
          opacity: 1;
        }
      }
    }

    &:hover .edit-btn {
      opacity: 1;
    }

    .edit-mode {
      display: flex;
      align-items: center;
      gap: 8px;

      .anchor-select {
        flex: 1;
        min-width: 120px;
      }

      .edit-actions {
        display: flex;
        gap: 4px;
        flex-shrink: 0;
      }
    }
  }
</style>
