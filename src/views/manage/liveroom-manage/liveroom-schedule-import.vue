<template>
  <d-drawer
    :ok-loading="loading"
    title="批量导入"
    :visible="visible"
    width="800px"
    @ok="sendInfo"
    @cancel="onClose"
  >
    <a-form ref="formRef" :model="formModel" layout="vertical">
      <a-spin :loading="loading">
        <a-form-item>
          <template #label>
            <span class="mr-10">文件</span>
            <a-button size="mini" type="primary" @click="downloadTmp()">
              <template #icon>
                <icon-download />
              </template>
              <span>下载模板文件</span>
            </a-button>
          </template>
          <a-upload
            v-model:file-list="formModel.file"
            :multiple="false"
            draggable
            accept=".xlsx,.xls,.csv"
            :directory="false"
            name="file"
            @before-upload="beforeUpload"
          >
          </a-upload>
        </a-form-item>
      </a-spin>
    </a-form>
  </d-drawer>
</template>

<script lang="ts" setup>
  import DDrawer from '@/components/d-modal/d-drawer.vue';
  import { ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import request from '@/api/request';
  import { downloadLinkFile } from '@/utils/table-utils/table-util';

  const getDefaultFormModel = () => {
    return {
      file: [],
    };
  };
  const formModel = ref<any>(getDefaultFormModel());
  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();

  const emits = defineEmits(['refresh']);

  function downloadTmp() {
    downloadLinkFile('/直播间排班导入模板.xlsx', '直播间排班导入模板.xlsx');
  }
  function beforeUpload(file: File) {
    formModel.value.file = [
      {
        file,
        uid: '1',
        status: 'done',
        name: file.name,
      },
    ] as any;
    return false;
  }

  function show() {
    visible.value = true;
  }

  function resetThisForm() {
    formModel.value = getDefaultFormModel();
    // 移除表单项的校验结果
    formRef.value.clearValidate();
  }
  function onClose() {
    visible.value = false;
    loading.value = false;
    resetThisForm();
  }

  function sendInfo() {
    formRef.value.validate((err: any) => {
      if (!err) {
        if (!formModel.value.file?.length) {
          Message.error('请选择文件');
          return null;
        }
        loading.value = true;
        const formData = new FormData();
        formData.append('file', formModel.value.file[0]?.file);
        request('/api/liveRoom/schedulingImport', formData)
          .then(() => {
            onClose();
            emits('refresh');
          })
          .finally(() => {
            loading.value = false;
          });
      }
    });
  }

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>
