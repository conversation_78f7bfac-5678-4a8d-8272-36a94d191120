<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="handleSubmit()"
      >
        <template #formItemGroup>
          <a-form-item label="添加时间">
            <a-range-picker v-model="formModel.date" allow-clear />
          </a-form-item>
          <a-form-item label="旅游线路">
            <dict-select
              v-model="formModel.line_id"
              :data-list="dataStore.lineList"
              label-key="line_name"
              value-key="line_id"
            />
          </a-form-item>
          <a-form-item label="添加人">
            <request-select
              v-model="formModel.user_id"
              request-url="/api/user/userList"
              label-key="user_name"
              :send-params="{ pageSize: 999999 }"
            />
          </a-form-item>
        </template>
      </search-form-fold>

      <div class="table-card-header mt-10">
        <div> </div>
        <a-space>
          <a-button type="primary" @click="showEditFn()">
            <template #icon>
              <icon-plus />
            </template>
            新增
          </a-button>
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formParams"
        :scroll-percent="{ x: 1000, maxHeight: '70vh' }"
      >
        <template #action="{ record }: TableColumnSlot">
          <a-space>
            <a-link @click="showEditFn(record)"> 编辑 </a-link>
            <a-popconfirm
              position="left"
              :content="`此操作不可逆，确定要删除【${record.line_name}】吗?`"
              @ok="updateState(record, { state: -1 })"
            >
              <a-link :loading="record.loading" status="danger"> 删除 </a-link>
            </a-popconfirm>
          </a-space>
        </template>
        <template #state="{ record }: TableColumnSlot">
          <a-switch
            :loading="record.loading"
            :model-value="record.state === 1"
            @change="
              updateState(record, { state: record.state === 1 ? -1 : 1 })
            "
          />
        </template>
      </base-table>
    </a-card>
    <line-edit
      ref="editRef"
      @save="
        dataStore.getLineConfig();
        handleSubmit();
      "
    />
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive, computed } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import LineEdit from '@/views/manage/line-manage/line-edit.vue';
  import { useDataCacheStore } from '@/store';

  const dataStore = useDataCacheStore();
  const generateFormModel = () => {
    return {
      line_id: '',
      user_id: '',
      date: [],
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const num = ref(1);
  const formModel = reactive(generateFormModel());

  const formParams = computed(() => {
    return {
      ...formModel,
      add_time_begin: formModel.date?.[0],
      add_time_end: formModel.date?.[1],
    };
  });

  const getList = async (data: any) => {
    return request('/api/travelLine/list', {
      ...data,
    });
  };

  const editRef = ref();
  function showEditFn(record?: any) {
    editRef.value?.show(record);
  }

  const columns = [
    {
      title: 'ID',
      dataIndex: 'line_id',
    },
    {
      title: '旅游线路',
      dataIndex: 'line_name',
    },
    {
      title: '流量投放',
      dataIndex: 'user_pitcher_name',
    },
    {
      title: '产品规划师',
      dataIndex: 'user_planner_name',
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
    {
      title: '添加人',
      dataIndex: 'user_name',
    },
    {
      title: '添加时间',
      dataIndex: 'created_at',
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 180,
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function updateState(record: any, params: any) {
    record.loading = true;
    request('/api/travelLine/save', {
      line_id: record.line_id,
      ...params,
    })
      .then(() => {
        handleSubmit();
      })
      .finally(() => {
        record.loading = false;
      });
  }

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less"></style>
