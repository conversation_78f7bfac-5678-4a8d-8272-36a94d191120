<template>
  <d-modal
    v-model:visible="visible"
    width="500px"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    :mask-closable="false"
    :esc-to-close="false"
    :title="`${formModel.line_id ? '编辑' : '添加'}旅游线路`"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formModel" :auto-label-width="true">
      <a-form-item label="旅游线路" field="line_name" :rules="requiredRule">
        <a-input v-model="formModel.line_name" />
      </a-form-item>
      <a-form-item
        label="流量投放"
        field="user_pitcher_id"
        :rules="requiredRule"
      >
        <request-select
          v-model="formModel.user_pitcher_id"
          request-url="/api/user/userList"
          label-key="user_name"
          :send-params="{ pageSize: 999999, roles: [11] }"
        />
      </a-form-item>
      <a-form-item
        label="产品规划师"
        field="user_planner_id"
        :rules="requiredRule"
      >
        <request-select
          v-model="formModel.user_planner_id"
          request-url="/api/user/userList"
          label-key="user_name"
          :send-params="{ pageSize: 999999, roles: [13] }"
        />
      </a-form-item>
      <a-form-item label="备注">
        <a-textarea
          v-model="formModel.remark"
          :auto-size="{ minRows: 3, maxRows: 5 }"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { requiredRule } from '@/utils/util';
  import { contactWayListM } from '@/components/dict-select/dict-travel';
  import RequestSelect from '@/components/select/request-select.vue';

  const defaultForm = () => ({
    line_id: null,
    line_name: '',
    user_pitcher_id: '',
    user_planner_id: '',
    remark: '',
    state: 1,
  });

  const emit = defineEmits(['save']);

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const formModel = ref(defaultForm());
  const validate = () => {
    return formRef.value?.validate();
  };
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  const show = (dinfo: any) => {
    let initForm = defaultForm();
    formModel.value = initForm;
    if (dinfo) {
      Object.keys(initForm).forEach((key) => {
        // @ts-ignore
        formModel.value[key] =
          dinfo[key] || initForm[key as keyof typeof initForm];
      });
    }

    clearValidate();
    visible.value = true;
    loading.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  const handleBeforeOk = async () => {
    const result = await validate();
    if (!result) {
      loading.value = true;
      request('/api/travelLine/save', {
        ...formModel.value,
      })
        .then(() => {
          Message.success('保存成功');
          handleCancel();
          emit('save');
        })
        .finally(() => {
          loading.value = false;
        });
    }
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>
