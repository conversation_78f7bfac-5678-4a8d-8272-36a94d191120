<template>
  <div>
    <a-card>
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="handleSubmit()"
      >
        <template #formItemGroup>
          <a-form-item label="操作人">
            <request-select v-model="formModel.user_id" api="user" />
          </a-form-item>
          <a-form-item field="status" label="操作动作">
            <dict-select v-model="formModel.action" :data-list="actionType" />
          </a-form-item>
          <a-form-item label="操作时间">
            <a-range-picker v-model="formModel.created_at" />
          </a-form-item>
          <a-form-item label="操作对象">
            <a-input v-model="formModel.object" allow-clear />
          </a-form-item>
          <a-form-item label="操作内容">
            <a-input v-model="formModel.content" allow-clear />
          </a-form-item>
        </template>
      </search-form-fold>
    </a-card>
    <a-card size="small" class="table-card">
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formModel"
        :scroll-percent="{ x: 1000, maxHeight: '70vh' }"
      >
        <template #content="{ record }: TableColumnSlot">
          <template v-if="record.action === '新增'">
            {{ record.object }}
          </template>
          <template v-else>
            <div v-for="(item, index) in record.content" :key="index">
              【{{ item.field_name }}】从‘{{ item.old_value || '-' }}’改为‘{{
                item.new_value
              }}’
            </div>
          </template>
        </template>
      </base-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import RequestSelect from '@/components/select/request-select.vue';

  const generateFormModel = () => {
    return {
      user_id: '',
      action: '',
      created_at: [],
      object: '',
      content: '',
    };
  };
  const actionType = [
    {
      label: '新增',
      value: '新增',
    },
    {
      label: '修改',
      value: '修改',
    },
  ];
  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    return request('/api/operationLogs/list', {
      ...data,
    });
  };

  const columns = [
    {
      title: '操作人',
      dataIndex: 'user_name',
      width: 90,
    },
    {
      title: '操作模块',
      dataIndex: 'module',
      width: 90,
    },
    {
      title: '操作对象',
      dataIndex: 'object',
      width: 200,
    },
    {
      title: '操作动作',
      dataIndex: 'action',
      width: 200,
    },
    {
      title: '操作内容',
      dataIndex: 'content',
      align: 'left',
    },
    {
      title: '操作IP',
      dataIndex: 'ip',
      width: 200,
    },
    {
      title: '操作时间',
      dataIndex: 'created_at',
      width: 200,
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less"></style>
