<template>
  <div class="content">
    <div class="schedule">
      <div>
        <div class="df">
          <div
            ref="wrapDom"
            class="s-table"
            @mousedown="mousedownHandle"
            @mouseleave="mouseupHandle"
            @mousemove="mousemoveHandle"
            @mouseup="mouseupHandle"
          >
            <a-table
              size="small"
              bordered
              :data="list"
              row-key="value"
              :pagination="false"
            >
              <template #columns>
                <a-table-column align="center">
                  <template #title>
                    <div style="font-size: 12px">星期/时间</div>
                  </template>

                  <template #cell="{ record }">
                    <div class="s-cell-title">
                      {{ record.value }}
                    </div>
                  </template>
                </a-table-column>
                <template v-for="(val, i) in tableCols" :key="i">
                  <a-table-column>
                    <template #title>
                      <span style="color: #1890ff">
                        {{ 12 > val[0] ? '0' + val[0] : val[0] }}:00 -
                        {{ val[11] }}:00
                      </span>
                    </template>
                    <template v-for="(item, j) in val" :key="j">
                      <a-table-column>
                        <template #title>
                          <span style="color: #1890ff">{{
                            item > 9 ? item : '0' + item
                          }}</span>
                        </template>
                        <a-table-column :col-span="0">
                          <template #cell="{ rowIndex }">
                            <div
                              ref="cell"
                              :class="{
                                's-cell': true,
                                'checked':
                                  modelValue[rowIndex * 24 + i * 12 + j] ===
                                  '1',
                              }"
                              @click="
                                submitChange([rowIndex * 24 + i * 12 + j])
                              "
                            ></div>
                          </template>
                        </a-table-column>
                      </a-table-column>
                    </template>
                  </a-table-column>
                </template>
              </template>
            </a-table>
            <div
              v-if="hasDown"
              class="mask"
              :style="{
                right: domWidth - right + 'px',
                bottom: domHeight - bottom + 'px',
                top: top + 'px',
                left: left + 'px',
              }"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'TimeTableSelectKs',
    props: {
      modelValue: {
        type: Array,
        default: () => [],
      },
    },
    emits: ['update:modelValue', 'change'],
    data() {
      return {
        list: [
          { value: '一' },
          { value: '二' },
          { value: '三' },
          { value: '四' },
          { value: '五' },
          { value: '六' },
          { value: '日' },
        ],
        tableCols: [
          Array.from({ length: 12 }, (v, k) => k),
          Array.from({ length: 12 }, (v, k) => k + 12),
        ],
        hasDown: false,
        startX: 0,
        startY: 0,
        layerX: 0,
        layerY: 0,
        lastTime: 0,
        timer: null,
        tablePosition: [],
        domWidth: 0,
        domHeight: 0,
      };
    },
    computed: {
      left() {
        return this.layerX > this.startX ? this.startX : this.layerX;
      },
      right() {
        return this.layerX < this.startX ? this.startX : this.layerX;
      },
      top() {
        return this.layerY > this.startY ? this.startY : this.layerY;
      },
      bottom() {
        return this.layerY < this.startY ? this.startY : this.layerY;
      },
    },
    mounted() {
      document.ondragstart = () => false;
      setTimeout(() => {
        this.tablePosition = this.$refs.cell.map((item) => {
          return {
            x: item.offsetParent.offsetLeft,
            y: item.offsetParent.offsetTop,
          };
        });
        this.domWidth = this.$refs.wrapDom && this.$refs.wrapDom.offsetWidth;
        this.domHeight = this.$refs.wrapDom && this.$refs.wrapDom.offsetHeight;
      }, 10);
    },
    beforeUnmount() {
      document.ondragstart = () => true;
    },
    methods: {
      mousedownHandle(e) {
        this.hasDown = true;
        this.layerX = e.layerX;
        this.startX = e.layerX;
        this.layerY = e.layerY;
        this.startY = e.layerY;
      },
      mousemoveHandle(e) {
        if (this.hasDown) {
          let now = Date.now();
          if (now - this.lastTime - 100 > 0) {
            this.lastTime = now;
            if (this.timer) {
              this.layerX = e.layerX;
              this.layerY = e.layerY;
              clearTimeout(this.timer);
            } else {
              this.timer = setTimeout(() => {
                this.layerX = e.layerX;
                this.layerY = e.layerY;
              }, 100);
            }
          }
        }
      },
      mouseupHandle(e) {
        if (
          (this.hasDown && Math.abs(this.layerX - this.startX) > 10) ||
          Math.abs(this.layerY - this.startY) > 10
        ) {
          this.layerX = e.layerX;
          this.layerY = e.layerY;
          clearTimeout(this.timer);
          let arr = [];
          let value = 0;
          this.tablePosition.forEach((item, index) => {
            if (
              // 左上角
              (item.x >= this.left &&
                item.x <= this.right &&
                item.y >= this.top &&
                item.y <= this.bottom) ||
              // 右下角
              (item.x + 16 >= this.left &&
                item.x + 16 <= this.right &&
                item.y + 20 >= this.top &&
                item.y + 20 <= this.bottom) ||
              // 右上角
              (item.x + 16 >= this.left &&
                item.x + 16 <= this.right &&
                item.y >= this.top &&
                item.y <= this.bottom) ||
              // 左下角
              (item.x >= this.left &&
                item.x <= this.right &&
                item.y + 20 >= this.top &&
                item.y + 20 <= this.bottom) ||
              (item.x < this.left &&
                item.x + 16 > this.left &&
                item.y < this.top &&
                item.y + 20 > this.top) ||
              (item.x < this.left &&
                item.x + 16 > this.left &&
                ((item.y > this.top && item.y + 20 < this.bottom) ||
                  (item.y < this.top && item.y + 20 > this.top) ||
                  (item.y < this.bottom && item.y + 20 > this.bottom))) ||
              (((item.x > this.left && item.x + 16 < this.right) ||
                (item.x < this.left && item.x + 16 > this.left) ||
                (item.x < this.right && item.x + 16 > this.right)) &&
                item.y < this.top &&
                item.y + 20 > this.top)
            ) {
              if (value === '0') {
                if (this.modelValue[index] === '0') {
                  value = 1;
                }
              }
              arr.push(index);
            }
          });
          this.submitChange(arr, value);
        }
        this.hasDown = false;
        this.layerY = 0;
        this.layerX = 0;
        this.startY = 0;
        this.startX = 0;
      },
      submitChange(arr, value) {
        if (value !== '0' && value !== '1') {
          arr.forEach((index) => {
            value = this.modelValue[index] === '1' ? '0' : '1';
          });
        }
        let source = this.modelValue.slice();
        arr.forEach((index) => {
          source[index] = value;
        });
        this.$emit('update:modelValue', source);
        this.$emit('change', arr, value);
      },
    },
  };
</script>

<style lang="less" scoped>
  .content {
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    justify-content: space-between;
  }
  .color-block {
    width: 16px;
    height: 16px;
    border: 1px solid #333;
    border-radius: 3px;
  }
  .color-wrap {
    display: flex;
  }
  .color-box {
    display: flex;
    align-items: center;
    margin-right: 10px;
    p {
      margin: 0;
    }
  }
  .schedule {
    display: flex;
    .s-table {
      position: relative;
      padding-bottom: 20px;
      .mask {
        position: absolute;
        background: rgba(24, 144, 255, 0.6);
        border: 1px solid rgb(24, 144, 255);
        pointer-events: none;
      }
      .s-tr {
        &:hover {
          background: rgb(var(--arcoblue-1));
        }
      }
      .s-cell-title {
        width: 60px;
        padding: 0 6px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 12px;
      }
      .s-cell {
        height: 20px;
        width: 30px;
      }
      .checked {
        background-color: rgba(24, 144, 255, 0.4);
      }
    }
  }

  .s-table
    :deep(.arco-table-container .arco-table-content .arco-table-element) {
    margin: 0;
    user-select: none;
  }
  .s-table :deep(.arco-table-element .arco-table-cell) {
    padding: 0px !important;
    &:last-child {
      border-right: 1px solid #f0f0f0 !important;
    }
  }
  .s-table :deep(.arco-table-element tr th) {
    padding: 0px !important;
    border-right: 1px solid #f0f0f0 !important;
  }
  .s-table :deep(.arco-table-container .arco-table-content) {
    border-right: 0;
  }

  .s-table :deep(tbody tr:hover td) {
    background: none;
  }

  .s-table :deep(tbody tr td:hover) {
    background: rgb(var(--arcoblue-1));
  }
</style>
