<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="handleSubmit()"
      >
        <template #formItemGroup>
          <!-- 时间范围 -->
          <a-form-item label="时间范围">
            <c-range-picker
              v-model="formModel.dateArr"
              allow-clear
              @change="changeDateArr()"
            />
          </a-form-item>
          <!-- 部门 -->
          <a-form-item label="部门">
            <request-tree-select
              v-model="formModel.department_ids"
              request-url="/api/department/list"
              label-key="department_name"
              child-key="child"
              multiple
              placeholder="请选择部门"
              @change="handleSubmit({})"
            />
          </a-form-item>
          <!-- 人员姓名搜索 -->
          <a-form-item label="人员姓名">
            <a-input
              v-model="formModel.keyword"
              allow-clear
              placeholder="输入账号、姓名、手机号"
            />
          </a-form-item>
          <!-- 在线状态 -->
          <a-form-item field="status" label="是否在线">
            <dict-select
              v-model="formModel.status"
              :data-list="onlineStatusDict"
              placeholder="请选择状态"
              @change="handleSubmit({})"
            />
          </a-form-item>
        </template>
      </search-form-fold>
    </a-card>

    <a-card size="small" class="table-card">
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        class="mt-10"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formModel"
      >
        <template #daily_max_clue="{ record }: TableColumnSlot">
          <template v-if="record.id !== '汇总'">
            <a-input-number
              v-model="record.daily_max_clue"
              :min="0"
              :max="99"
              :precision="0"
              size="small"
              style="width: 100px"
              placeholder="请输入"
              @change="handleFieldChange(record, 'daily_max_clue', $event)"
            />
          </template>
          <template v-else>
            <span class="summary-text">{{ record.daily_max_clue || 0 }}</span>
          </template>
        </template>
        <template #daily_max_order="{ record }: TableColumnSlot">
          <template v-if="record.id !== '汇总'">
            <a-input-number
              v-model="record.daily_max_order"
              :min="0"
              :max="99"
              :precision="0"
              size="small"
              style="width: 100px"
              placeholder="请输入"
              @change="handleFieldChange(record, 'daily_max_order', $event)"
            />
          </template>
          <template v-else>
            <span class="summary-text">{{ record.daily_max_order || 0 }}</span>
          </template>
        </template>
        <template #daily_max_total="{ record }: TableColumnSlot">
          <template v-if="record.id !== '汇总'">
            <a-input-number
              v-model="record.daily_max_total"
              :min="0"
              :max="99"
              :precision="0"
              size="small"
              style="width: 100px"
              placeholder="请输入"
              @change="handleFieldChange(record, 'daily_max_total', $event)"
            />
          </template>
          <template v-else>
            <span class="summary-text">{{ record.daily_max_total || 0 }}</span>
          </template>
        </template>
        <template #status="{ record }: TableColumnSlot">
          <!-- 如果是汇总行 -->
          <div v-if="record.status.includes('/')" class="summary-cell">
            <a-tag class="summary-text">{{ record.status }}</a-tag>
          </div>
          <!-- 如果是普通行 -->
          <template v-else>
            <a-tag v-if="record.status === '在线'" color="green" size="small">
              在线
            </a-tag>
            <a-tag v-else color="red" size="small"> 离线 </a-tag>
          </template>
        </template>
        <template #distribution_clue_num="{ record }: TableColumnSlot">
          <template v-if="record.id !== '汇总'">
            <span>{{ record.distribution_clue_num || 0 }}</span>
            <a-tag
              v-if="getClueStatusTag(record)"
              :color="getClueStatusTag(record).color"
              size="mini"
              style="margin-left: 4px"
            >
              {{ getClueStatusTag(record).text }}
            </a-tag>
          </template>
          <template v-else>
            <span class="summary-text">{{
              record.distribution_clue_num || 0
            }}</span>
            <a-tag
              v-if="getSummaryStatusTag(record)"
              :color="getSummaryStatusTag(record).color"
              size="mini"
              style="margin-left: 4px"
            >
              {{ getSummaryStatusTag(record).text }}
            </a-tag>
          </template>
        </template>
        <template #distribution_order_num="{ record }: TableColumnSlot">
          <template v-if="record.id !== '汇总'">
            <span>{{ record.distribution_order_num || 0 }}</span>
            <a-tag
              v-if="getOrderStatusTag(record)"
              :color="getOrderStatusTag(record).color"
              size="mini"
              style="margin-left: 4px"
            >
              {{ getOrderStatusTag(record).text }}
            </a-tag>
          </template>
          <template v-else>
            <span class="summary-text">{{
              record.distribution_order_num || 0
            }}</span>
            <a-tag
              v-if="getSummaryStatusTag(record)"
              :color="getSummaryStatusTag(record).color"
              size="mini"
              style="margin-left: 4px"
            >
              {{ getSummaryStatusTag(record).text }}
            </a-tag>
          </template>
        </template>
        <template #action="{ record }: TableColumnSlot">
          <a-space>
            <a-link @click="handleDelete(record)"> <icon-delete />删除 </a-link>
          </a-space>
        </template>
      </base-table>
    </a-card>
    <!-- <assign-user-edit ref="editRef" @save="handleSubmit()" /> -->
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';

  import { ref, reactive, computed } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import { customerServiceStatusListM } from '@/components/dict-select/dict-travel';
  import RequestTreeSelect from '@/components/select/request-tree-select.vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import { TableColumnSlot } from '@/global';
  // import assignUserEdit from '@/views/manage/assign-user-manage/assign-user-edit.vue';
  import { requiredRule, requiredRuleArr } from '@/utils/util';
  import { stringArrShow } from '@/utils/table-utils/columns-config';
  import { Message } from '@arco-design/web-vue';
  import dayjs from 'dayjs';

  const onlineStatusDict = [
    {
      label: '在线',
      value: '在线',
    },
    {
      label: '离线',
      value: '离线',
    },
  ];

  const generateFormModel = () => {
    return {
      // 搜索表单字段
      dateArr: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')], // 日期范围选择器绑定字段
      date_begin: dayjs().format('YYYY-MM-DD'),
      date_end: dayjs().format('YYYY-MM-DD'),
      user_name: null, // 人员姓名搜索字段
      status: null, // 在线状态筛选字段

      // API参数字段（由搜索表单字段转换而来）

      department_ids: [], // API需要的部门ID数组
      user_ids: [], // 预留的用户ID筛选字段
      keyword: '',
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  // 计算API参数，处理字段映射和转换

  const getList = async (data: any) => {
    // 合并表单参数和额外参数
    const requestParams = {
      is_about_me: true,
      ...data,
      ...formModel,
    };

    return request('/api/travel/allServiceUser', requestParams);
  };

  // 处理删除操作
  const handleDelete = (record: any) => {
    console.log('删除操作:', record);
    // 这里可以添加删除逻辑
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
    },
    // {
    //   title: '账号',
    //   dataIndex: 'account_name',
    //   width: 120,
    //   fixed: 'left',
    // },
    {
      title: '姓名',
      dataIndex: 'user_name',
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
    },
    // 角色
    {
      title: '角色',
      dataIndex: 'role_names',
      render: stringArrShow(),
    },
    {
      title: '部门',
      dataIndex: 'department_name',
    },
    // 单日线索上限
    {
      title: '单日线索上限',
      dataIndex: 'daily_max_clue',
      slotName: 'daily_max_clue',
    },
    // 单日订单上限
    {
      title: '单日订单上限',
      dataIndex: 'daily_max_order',
      slotName: 'daily_max_order',
    },
    // 线索+订单上限
    {
      title: '线索+订单上限',
      dataIndex: 'daily_max_total',
      slotName: 'daily_max_total',
    },
    // 是否在线
    {
      title: '是否在线',
      dataIndex: 'status',
      fixed: 'right',
      slotName: 'status',
      width: 88,
    },
    {
      title: '已分配线索数',
      dataIndex: 'distribution_clue_num',
      slotName: 'distribution_clue_num',
      fixed: 'right',
      width: 120,
    },
    {
      title: '已分配订单数',
      dataIndex: 'distribution_order_num',
      slotName: 'distribution_order_num',
      fixed: 'right',
      width: 120,
    },
    // {
    //   title: '操作',
    //   dataIndex: 'action',
    // },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    // 更新表单数据
    Object.assign(formModel, resData);
    // 触发表格搜索，会自动使用 apiParams 计算属性
    theTable.value?.search();
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }

  const updateRuleInfo = async (data: any) => {
    return request('/api/travel/setServiceUserDescribe', {
      ...data,
    });
  };

  // 处理字段变更的互斥逻辑
  const handleFieldChange = async (
    record: any,
    fieldName: string,
    value: number | null
  ) => {
    // 创建更新数据对象
    const updateData = {
      ...record,
      user_id: record.id,
      [fieldName]: value,
    };

    // 实现互斥逻辑
    if (fieldName === 'daily_max_clue' || fieldName === 'daily_max_order') {
      // 当修改 daily_max_clue 或 daily_max_order 时，清空 daily_max_total
      updateData.daily_max_total = null;
      record.daily_max_total = null;
    } else if (fieldName === 'daily_max_total') {
      // 当修改 daily_max_total 时，清空 daily_max_clue 和 daily_max_order
      updateData.daily_max_clue = null;
      updateData.daily_max_order = null;
      record.daily_max_clue = null;
      record.daily_max_order = null;
    }

    // 调用API更新数据
    try {
      await updateRuleInfo(updateData);
      Message.success('更新成功');
    } catch (error) {
      console.error('更新失败:', error);
      // 如果更新失败，可以在这里添加错误处理逻辑
    }
  };

  const changeDateArr = () => {
    if (formModel.dateArr && formModel.dateArr.length === 2) {
      [formModel.date_begin, formModel.date_end] = formModel.dateArr;
    } else {
      formModel.date_begin = null;
      formModel.date_end = null;
    }
    handleSubmit();
  };

  // 计算时间范围天数
  const getDateRangeDays = () => {
    if (!formModel.date_begin || !formModel.date_end) {
      return 1; // 默认为1天
    }

    const startDate = dayjs(formModel.date_begin);
    const endDate = dayjs(formModel.date_end);
    const days = endDate.diff(startDate, 'day') + 1; // 包含开始和结束日期

    return Math.max(1, days); // 确保至少为1天
  };

  // 获取线索数状态标签
  const getClueStatusTag = (record: any) => {
    const clueNum = record.distribution_clue_num || 0;
    const orderNum = record.distribution_order_num || 0;
    const days = getDateRangeDays();

    // 场景2：总量上限配置优先
    if (record.daily_max_total && record.daily_max_total > 0) {
      const total = clueNum + orderNum;
      const totalLimit = record.daily_max_total * days; // 单日上限 × 天数
      if (total === totalLimit) {
        return { text: 'max', color: 'orange' };
      }
      if (total > totalLimit) {
        return { text: '超出', color: 'red' };
      }
    }
    // 场景1：单独线索上限配置
    else if (record.daily_max_clue && record.daily_max_clue > 0) {
      const clueLimit = record.daily_max_clue * days; // 单日上限 × 天数
      if (clueNum === clueLimit) {
        return { text: 'max', color: 'orange' };
      }
      if (clueNum > clueLimit) {
        return { text: '超出', color: 'red' };
      }
    }

    return null;
  };

  // 获取订单数状态标签
  const getOrderStatusTag = (record: any) => {
    const clueNum = record.distribution_clue_num || 0;
    const orderNum = record.distribution_order_num || 0;
    const days = getDateRangeDays();

    // 场景2：总量上限配置优先
    if (record.daily_max_total && record.daily_max_total > 0) {
      const total = clueNum + orderNum;
      const totalLimit = record.daily_max_total * days; // 单日上限 × 天数
      if (total === totalLimit) {
        return { text: 'max', color: 'orange' };
      }
      if (total > totalLimit) {
        return { text: '超出', color: 'red' };
      }
    }
    // 场景1：单独订单上限配置
    else if (record.daily_max_order && record.daily_max_order > 0) {
      const orderLimit = record.daily_max_order * days; // 单日上限 × 天数
      if (orderNum === orderLimit) {
        return { text: 'max', color: 'orange' };
      }
      if (orderNum > orderLimit) {
        return { text: '超出', color: 'red' };
      }
    }

    return null;
  };

  // 获取汇总行状态标签（用于线索数和订单数列）
  const getSummaryStatusTag = (record: any) => {
    if (!record.is_statistics) return null;

    const clueNum = record.distribution_clue_num || 0;
    const orderNum = record.distribution_order_num || 0;
    const totalDistributed = clueNum + orderNum; // 已分配总数
    const days = getDateRangeDays();

    // 计算总的配额限制
    let totalLimit = 0;

    // 单日线索上限
    if (record.daily_max_clue && record.daily_max_clue > 0) {
      totalLimit += record.daily_max_clue * days;
    }

    // 单日订单上限
    if (record.daily_max_order && record.daily_max_order > 0) {
      totalLimit += record.daily_max_order * days;
    }

    // 线索+订单上限
    if (record.daily_max_total && record.daily_max_total > 0) {
      totalLimit += record.daily_max_total * days;
    }

    // 判断是否超出或达到上限
    if (totalLimit > 0) {
      if (totalDistributed === totalLimit) {
        return { text: 'MAX', color: 'orange' };
      }
      if (totalDistributed > totalLimit) {
        return { text: '超出', color: 'red' };
      }
    }

    return null;
  };
</script>

<style scoped lang="less"></style>
