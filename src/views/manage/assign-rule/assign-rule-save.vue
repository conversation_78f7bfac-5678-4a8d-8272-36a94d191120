<template>
  <d-modal
    v-model:visible="visible"
    width="1000px"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    :mask-closable="false"
    :esc-to-close="false"
    :title="`${formModel.id ? '编辑' : '添加'}${keyword}分配规则`"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formModel" :auto-label-width="true">
      <a-form-item label="规则名称" field="rule_name" :rules="requiredRule">
        <a-input v-model="formModel.rule_name" />
      </a-form-item>
      <a-divider>{{ keyword }}范围</a-divider>
      <a-form-item
        v-if="sendParams?.type === 'order'"
        label="订单类型"
        :rules="requiredRuleArr"
        field="select_type"
      >
        <dict-select
          v-model="formModel.select_type"
          placeholder="请选择"
          :data-list="orderTypeListM"
        />
      </a-form-item>
      <a-form-item
        v-else
        label="线索类型"
        field="select_type"
        :rules="requiredRuleArr"
      >
        <dict-select
          v-model="formModel.select_type"
          :data-list="clueTypeListM"
          placeholder="请选择"
        />
      </a-form-item>
      <a-form-item
        field="select_detparment"
        label="来源部门"
        :rules="requiredRule"
      >
        <!-- <template #label>
          <span style="width: 140px; display: inline-block; text-align: right">
            来源部门
          </span>
        </template> -->
        <request-tree-select
          v-model="formModel.select_detparment"
          request-url="/api/department/list"
          label-key="department_name"
          child-key="child"
          placeholder="请选择"
          multiple
        />
      </a-form-item>
      <a-form-item label="旅游线路" field="select_line" :rules="requiredRule">
        <dict-select
          v-model="formModel.select_line"
          :data-list="dataStore.lineList"
          label-key="line_name"
          value-key="line_id"
          placeholder="请选择"
        />
      </a-form-item>
      <a-divider>分配规则</a-divider>

      <a-form-item
        label="分配给"
        field="distribute_user_ids"
        :rules="requiredRule"
      >
        <request-select
          v-model="formModel.distribute_user_ids"
          api="assignRuleUser"
          :send-params="{ is_about_me: true }"
          multiple
          @change="handleUserSelectionChange"
        />
      </a-form-item>

      <!-- 人员配额信息展示 -->
      <a-form-item v-if="userQuotaList.length > 0" label="">
        <a-spin :loading="quotaLoading" style="width: 100%">
          <a-table
            :data="userQuotaList"
            :pagination="false"
            size="small"
            class="user-quota-table"
            :scroll="{
              x: 600,
              y: userQuotaList.length > 10 ? '300px' : 'auto',
            }"
          >
            <template #columns>
              <a-table-column
                title="人员姓名"
                data-index="user_name"
                :width="120"
              >
                <template #cell="{ record }">
                  <div class="user-info">
                    <icon-user class="user-icon" />
                    <span class="user-name">{{ record.user_name }}</span>
                  </div>
                </template>
              </a-table-column>
              <a-table-column
                title="单日线索上限"
                data-index="daily_max_clue"
                :width="150"
              >
                <template #cell="{ record }">
                  <a-input-number
                    v-model="record.daily_max_clue"
                    :min="0"
                    :max="99"
                    :precision="0"
                    size="mini"
                    style="width: 120px"
                    placeholder="请输入"
                    @change="
                      handleFieldChange(record, 'daily_max_clue', $event)
                    "
                  />
                </template>
              </a-table-column>
              <a-table-column
                title="单日订单上限"
                data-index="daily_max_order"
                :width="150"
              >
                <template #cell="{ record }">
                  <a-input-number
                    v-model="record.daily_max_order"
                    :min="0"
                    :max="99"
                    :precision="0"
                    size="mini"
                    style="width: 120px"
                    placeholder="请输入"
                    @change="
                      handleFieldChange(record, 'daily_max_order', $event)
                    "
                  />
                </template>
              </a-table-column>
              <a-table-column
                title="线索+订单上限"
                data-index="daily_max_total"
                :width="150"
              >
                <template #cell="{ record }">
                  <a-input-number
                    v-model="record.daily_max_total"
                    :min="0"
                    :max="99"
                    :precision="0"
                    size="mini"
                    style="width: 120px"
                    placeholder="请输入"
                    @change="
                      handleFieldChange(record, 'daily_max_total', $event)
                    "
                  />
                </template>
              </a-table-column>
            </template>
          </a-table>
        </a-spin>
      </a-form-item>
      <a-form-item
        label="是否在线"
        field="distribute_is_online"
        :rules="requiredRule"
      >
        <dict-radio
          v-model="formModel.distribute_is_online"
          :data-list="ruleTypeOnline"
        />
      </a-form-item>
      <!-- 分配时段 -->
      <a-form-item
        label="分配时段"
        field="distribute_schedule"
        :rules="requiredRule"
      >
        <template #label>
          分配时段
          <a-tooltip
            content="选择分配时段后，系统将在已选的时段内进行分配，未选时段不分配"
            position="top"
          >
            <icon-question-circle />
          </a-tooltip>
        </template>
        <div>
          <time-table-select
            v-model:model-value="formModel.distribute_schedule"
          ></time-table-select>
          <!-- <a-card v-if="showtimes" size="small">
            <template #title>
              <div class="df jc-sb" style="font-size: 14px">
                <span>已选择时间段</span>
                <a-button type="text" @click="clearScheduleTime">清空</a-button>
              </div>
            </template>
            <template v-for="(item, index) in showtimes">
              <p v-if="item" :key="index">
                <span class="mr-5">{{ item.week }} </span>
                <span v-for="(val, i) in item.list" :key="i">
                  {{ val[0] }}-{{ val[1] }}
                  {{ i === item.list.length - 1 ? '' : '、' }}
                </span>
              </p>
            </template>
          </a-card> -->
        </div>
      </a-form-item>
      <a-form-item
        label="分配时间间隔"
        field="distribute_interval"
        :rules="[requiredRule, { validator: distributeIntervalValidator }]"
      >
        <template #label>
          分配时间间隔
          <a-tooltip
            content="表示每个人在该规则下，连续两次分配的最短时间间隔"
            position="top"
          >
            <icon-question-circle />
          </a-tooltip>
        </template>
        <a-input-number
          v-model="formModel.distribute_interval"
          :precision="0"
          size="small"
          hide-button
          style="width: 200px"
          placeholder="请输入"
        >
          <template #suffix>分钟</template>
        </a-input-number>
      </a-form-item>

      <a-form-item label="补充规则">
        <a-tag color="arcoblue"> 相同手机号优先分配相同的电销 </a-tag>
      </a-form-item>
    </a-form>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { requiredRule, requiredRuleArr } from '@/utils/util';
  import RequestTreeSelect from '@/components/select/request-tree-select.vue';
  import { useDataCacheStore } from '@/store';
  import { orderTypeListM } from '@/components/dict-select/dict-travel';
  import { clueTypeListM } from '@/components/dict-select/dict-clue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import TimeTableSelect from '@/views/manage/assign-rule/time-table-select.vue';
  import RequestSelect from '@/components/select/request-select.vue';

  const dataStore = useDataCacheStore();
  const defaultForm = () => ({
    id: null,
    rule_name: '',
    select_type: [],
    select_detparment: [],
    select_line: [],
    distribute_department_ids: [],
    rule_type: 'least',
    last7_day_confirmation_rate: null,
    last7_day_profit_rate: null,
    distribute_user_ids: [],
    distribute_is_online: 1,
    distribute_interval: null,
    distribute_schedule: Array.from({ length: 24 * 7 }, () => '0'),
  });
  // const info = ref({
  //   distribute_schedule_type: 'default',
  //   distribute_schedule: Array.from({ length: 24 * 7 }, () => '0'),
  // });

  const ruleTypeOnline = [
    { label: '在线时分配', value: 1 },
    { label: '随时分配', value: 2 },
  ];

  const props = defineProps({
    sendParams: {
      type: Object,
      default: () => ({}),
    },
  });

  const emit = defineEmits(['save']);

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const formModel = ref(defaultForm());
  const keyword = computed(() =>
    props.sendParams?.type === 'order' ? '订单' : '线索'
  );

  // 人员配额相关数据
  const userQuotaList = ref<any[]>([]);
  const quotaLoading = ref(false);

  // 处理人员选择变化
  const handleUserSelectionChange = async (userIds: string[]) => {
    if (!userIds || userIds.length === 0) {
      userQuotaList.value = [];
      return;
    }

    quotaLoading.value = true;
    try {
      // 调用API获取所有服务用户信息
      const response = await request('/api/travel/allServiceUser', {
        user_ids: userIds,
      });

      if (response.data && Array.isArray(response.data.data)) {
        // 过滤出选中的用户信息
        userQuotaList.value = response.data.data.filter((user: any) =>
          userIds.includes(user.id)
        );
      } else {
        userQuotaList.value = [];
      }
    } catch (error) {
      console.error('获取人员配额信息失败:', error);
      Message.error('获取人员配额信息失败');
      userQuotaList.value = [];
    } finally {
      quotaLoading.value = false;
    }
  };

  // 监听人员选择变化
  watch(
    () => formModel.value.distribute_user_ids,
    (newUserIds) => {
      if (Array.isArray(newUserIds)) {
        handleUserSelectionChange(newUserIds);
      }
    },
    { immediate: false }
  );

  // API调用：更新人员配额信息
  const updateRuleInfo = async (data: any) => {
    return request('/api/travel/setServiceUserDescribe', {
      ...data,
    });
  };

  // 处理字段变更的互斥逻辑
  const handleFieldChange = async (
    record: any,
    fieldName: string,
    value: number | null
  ) => {
    // 创建更新数据对象
    const updateData = {
      ...record,
      user_id: record.id,
      [fieldName]: value,
    };

    // 实现互斥逻辑
    if (fieldName === 'daily_max_clue' || fieldName === 'daily_max_order') {
      // 当修改 daily_max_clue 或 daily_max_order 时，清空 daily_max_total
      updateData.daily_max_total = null;
      record.daily_max_total = null;
    } else if (fieldName === 'daily_max_total') {
      // 当修改 daily_max_total 时，清空 daily_max_clue 和 daily_max_order
      updateData.daily_max_clue = null;
      updateData.daily_max_order = null;
      record.daily_max_clue = null;
      record.daily_max_order = null;
    }

    // 调用API更新数据
    try {
      await updateRuleInfo(updateData);
      Message.success('更新成功');
    } catch (error) {
      console.error('更新失败:', error);
      Message.error('更新失败，请重试');
    }
  };

  const clearScheduleTime = () => {
    formModel.value.distribute_schedule = Array.from(
      { length: 24 * 7 },
      () => '0'
    );
  };
  const weeks = ['一', '二', '三', '四', '五', '六', '日'];
  const showtimes = computed(() => {
    if (
      formModel.value.distribute_schedule.join('') !==
        Array.from({ length: 24 * 7 }, () => '0').join('') &&
      formModel.value.distribute_schedule.join('') !==
        Array.from({ length: 24 * 7 }, () => '1').join('')
    ) {
      let arr = [];
      let result = [];
      let data = [...formModel.value.distribute_schedule];
      Array.from({ length: 7 }, (item, index) => index).forEach(
        (item, index) => {
          arr[index] = data.slice(index * 24, (index + 1) * 24);
        }
      );
      arr.forEach((item, index) => {
        result[index] = {
          week: `周${weeks[index]}`,
          list: [],
        };
        let times = '';
        item.forEach((val, i) => {
          // eslint-disable-next-line
          if (val == 1) {
            if (!times) {
              let hour =
                Math.floor(i) < 10 ? `0${Math.floor(i)}` : Math.floor(i);
              times = `${hour}:00`;
            }
            // eslint-disable-next-line
            if (item[i + 1] == 0 || i === 23) {
              let hour =
                Math.floor(i + 1) < 10
                  ? `0${Math.floor(i + 1)}`
                  : Math.floor(i + 1);
              result[index].list.push([times, `${hour}:00`]);
              times = '';
            }
          }
        });
        if (result[index].list.length === 0) {
          result[index] = null;
        }
      });
      return result;
    }
    return null;
  });

  const validate = () => {
    return formRef.value?.validate();
  };
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  const show = (dinfo: any) => {
    let initForm = defaultForm();
    formModel.value = initForm;
    userQuotaList.value = []; // 清空人员配额列表

    if (dinfo) {
      Object.keys(initForm).forEach((key) => {
        // @ts-ignore
        formModel.value[key] =
          dinfo[key] || initForm[key as keyof typeof initForm];
      });

      // 如果编辑模式且有选中的人员，获取配额信息
      if (
        dinfo.distribute_user_ids &&
        Array.isArray(dinfo.distribute_user_ids) &&
        dinfo.distribute_user_ids.length > 0
      ) {
        handleUserSelectionChange(dinfo.distribute_user_ids);
      }
    }

    clearValidate();
    // 如果clearScheduleTime是个字符串，需要转换成数组
    if (
      formModel.value.distribute_schedule &&
      typeof formModel.value.distribute_schedule === 'string'
    ) {
      formModel.value.distribute_schedule =
        formModel.value.distribute_schedule.split('');
    }
    visible.value = true;
    loading.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  const distributeIntervalValidator = (
    value: number,
    callback: (error?: string) => void
  ) => {
    if (value && (value < 5 || value > 30)) {
      callback('时间间隔允许范围为5-30');
    }
  };

  // 校验人员配额
  const validateUserQuota = () => {
    if (userQuotaList.value.length === 0) {
      return true; // 没有选择人员时不需要校验
    }

    const invalidUsers: string[] = [];

    userQuotaList.value.forEach((user) => {
      const hasClueLimit = user.daily_max_clue && user.daily_max_clue > 0;
      const hasOrderLimit = user.daily_max_order && user.daily_max_order > 0;
      const hasTotalLimit = user.daily_max_total && user.daily_max_total > 0;

      // 如果三个字段都没有填写或都为0，则校验失败
      if (!hasClueLimit && !hasOrderLimit && !hasTotalLimit) {
        invalidUsers.push(user.user_name || `用户ID: ${user.id}`);
      }
    });

    if (invalidUsers.length > 0) {
      const errorMessage = `${invalidUsers.join(
        '、'
      )} 需要至少填写一个配额限制（单日线索上限、单日订单上限或线索+订单上限）`;
      Message.error(errorMessage);
      return false;
    }

    return true;
  };

  const handleBeforeOk = async () => {
    if (
      !formModel.value.select_type.length &&
      !formModel.value.select_line.length &&
      !formModel.value.select_detparment.length
    ) {
      return Message.error('请选择线索范围');
    }

    // 校验人员配额
    if (!validateUserQuota()) {
      return; // 校验失败，阻止提交
    }

    const result = await validate();
    if (!result) {
      loading.value = true;
      request('/api/distributeRules/save', {
        ...formModel.value,
        ...props.sendParams,
        distribute_schedule: formModel.value.distribute_schedule.join(''),
      })
        .then(() => {
          Message.success('保存成功');
          handleCancel();
          emit('save');
        })
        .finally(() => {
          loading.value = false;
        });
    }
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped>
  .user-quota-table {
    border: 1px solid #e8e8e8;
    border-radius: 6px;

    :deep(.arco-table) {
      border-radius: 6px;
    }

    :deep(.arco-table-th) {
      background-color: #fafafa;
      font-weight: 500;
      color: #262626;
    }

    // :deep(.arco-table-td) {
    //   padding: 12px 16px;
    // }

    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .user-icon {
        color: #1890ff;
        font-size: 14px;
      }

      .user-name {
        font-weight: 500;
        color: #262626;
        font-size: 14px;
      }
    }

    // 输入框样式优化
    :deep(.arco-input-number) {
      .arco-input-inner {
        padding: 4px 8px;
        font-size: 13px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .user-quota-table {
      // :deep(.arco-table-td) {
      //   padding: 8px 12px;
      // }

      .user-info {
        .user-name {
          font-size: 13px;
        }
      }

      :deep(.arco-input-number) {
        width: 100px !important;

        .arco-input-inner {
          font-size: 12px;
        }
      }
    }
  }
</style>
