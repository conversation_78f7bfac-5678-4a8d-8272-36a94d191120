<template>
  <div>
    <dict-radio
      v-model="formModel.type"
      :data-list="tableType"
      @change="handleSubmit()"
    />

    <user-assign-table v-if="formModel.type === '人员配置'" />
    <clue-pool-rule v-else-if="formModel.type === 'clue_pool'" />

    <div v-else>
      <a-card size="small" class="mt-10">
        <search-form-fold
          :form-data="formModel"
          :get-default-form-data="generateFormModel"
          :continue-key="['type']"
          @search="handleSubmit()"
        >
          <template #formItemGroup>
            <a-form-item label="规则ID或名称">
              <a-input
                v-model="formModel.search"
                allow-clear
                placeholder="请输入"
              />
            </a-form-item>
          </template>
        </search-form-fold>
      </a-card>

      <a-card size="small" class="table-card">
        <div class="table-card-header">
          <div> </div>
          <a-space>
            <a-button type="primary" @click="saveRef?.show()">
              <template #icon>
                <icon-plus />
              </template>
              新增
            </a-button>
          </a-space>
        </div>
        <!--table 区域-->
        <base-table
          ref="theTable"
          v-model:loading="loading"
          :columns-config="columns"
          :data-config="getList"
          :send-params="formModel"
        >
          <template #select_type="{ record }: TableColumnSlot">
            <div v-if="record.select_type.length">
              {{ record.type === 'order' ? '订单' : '线索' }}类型为：{{
                record.select_type.join(',')
              }}
            </div>
            <div v-if="record.select_detparment_show.length">
              来源部门为：{{ record.select_detparment_show.join(',') }}
            </div>
            <div v-if="record.select_line_show.length">
              旅游线路为：{{ record.select_line_show.join(',') }}
            </div>
          </template>
          <template #assign_rule_show="{ record }: TableColumnSlot">
            <div v-if="record.distribute_user_detail.length" class="rule-item">
              <span class="rule-label">分配给：</span>
              <a-tooltip
                :content="getDistributeUserText(record.distribute_user_detail)"
                position="top"
                :content-style="{ maxWidth: '400px' }"
              >
                <span class="rule-value distribute-users">
                  {{ getDistributeUserText(record.distribute_user_detail) }}
                </span>
              </a-tooltip>
            </div>
            <div v-if="record.distribute_is_online" class="rule-item">
              <span class="rule-label">是否在线：</span>
              <a-tag
                :color="getOnlineStatusColor(record.distribute_is_online)"
                size="small"
              >
                {{ getOnlineStatusText(record.distribute_is_online) }}
              </a-tag>
            </div>
            <div v-if="record.distribute_interval" class="rule-item">
              <span class="rule-label">分配时间间隔：</span>
              <span class="rule-value"
                >{{ record.distribute_interval }}分钟</span
              >
            </div>
            <span
              v-if="
                !record.distribute_user_detail.length &&
                !record.distribute_is_online &&
                !record.distribute_interval
              "
            >
              -
            </span>
          </template>
          <template #action="{ record }: TableColumnSlot">
            <a-space>
              <a-link @click="saveRef?.show(record)">编辑</a-link>
            </a-space>
          </template>
        </base-table>
      </a-card>
      <assign-rule-save
        ref="saveRef"
        :send-params="{ type: formModel.type }"
        @save="handleSubmit()"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import { stringArrShow } from '@/utils/table-utils/columns-config';
  import AssignRuleSave from '@/views/manage/assign-rule/assign-rule-save.vue';
  import UserAssignTable from '@/views/manage/assign-rule/user-assign-table.vue';
  import CluePoolRule from '@/views/manage/assign-rule/clue-pool/index.vue';

  const generateFormModel = () => {
    return {
      type: 'clue_pool',
      search: '',
    };
  };
  const tableType = [
    {
      label: '线索池分配规则',
      value: 'clue_pool',
    },
    {
      label: '人员配置',
      value: '人员配置',
    },
    {
      label: '订单',
      value: 'order',
    },
    {
      label: '线索',
      value: 'clue',
    },
  ];
  const ruleTypeOnline = [
    { label: '在线时分配', value: 1 },
    { label: '随时分配', value: 2 },
  ];
  const loading = ref(false);
  const saveRef = ref();
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  // 获取分配人员文本
  const getDistributeUserText = (userDetail: any[]) => {
    if (!userDetail || !userDetail.length) return '';
    return userDetail
      .map((item) => `${item.user_name}(${item.distribute_num}个)`)
      .join('、');
  };

  // 获取在线状态文本
  const getOnlineStatusText = (value: number) => {
    const item = ruleTypeOnline.find((rule) => rule.value === value);
    return item?.label || value;
  };

  // 获取在线状态颜色
  const getOnlineStatusColor = (value: number) => {
    switch (value) {
      case 1:
        return 'green'; // 在线时分配
      case 2:
        return 'blue'; // 随时分配
      default:
        return 'gray';
    }
  };

  const getList = async (data: any) => {
    return request('/api/distributeRules/list', {
      ...data,
    });
  };

  const columns = [
    {
      title: '规则ID',
      dataIndex: 'id',
      width: 120,
      fixed: 'left',
    },
    {
      title: '规则名称',
      dataIndex: 'rule_name',
      fixed: 'left',
    },
    {
      title: '线索范围',
      dataIndex: 'select_type',
      align: 'left',
    },
    {
      title: '分配规则',
      dataIndex: 'assign_rule_show',
      slotName: 'assign_rule_show',
      align: 'left',
    },
    {
      title: '创建人',
      dataIndex: 'create_user_name',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 80,
      fixed: 'right',
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };
</script>

<style scoped lang="less">
  .rule-item {
    margin-bottom: 8px;
    display: flex;
    align-items: center;

    &:last-child {
      margin-bottom: 0;
    }

    .rule-label {
      font-size: 13px;
      flex-shrink: 0;
      margin-right: 4px;
    }

    .rule-value {
      color: #262626;
      font-size: 13px;
      flex: 1;

      &.distribute-users {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
        cursor: pointer;

        &:hover {
          color: #1890ff;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .rule-item {
      margin-bottom: 6px;

      .rule-label,
      .rule-value {
        font-size: 12px;
      }

      .rule-value.distribute-users {
        max-width: 150px;
      }
    }
  }

  @media (max-width: 480px) {
    .rule-item {
      .rule-value.distribute-users {
        max-width: 100px;
      }
    }
  }
</style>
