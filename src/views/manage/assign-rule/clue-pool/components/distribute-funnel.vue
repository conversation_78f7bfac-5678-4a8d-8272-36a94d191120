<template>
  <a-spin :loading="loading" class="w100p">
    <!-- 流水漏斗图 -->
    <div class="water-funnel-container">
      <!-- 顶部总线索池 -->
      <div class="total-pool">
        <div class="pool-container" :style="{ width: `${totalPoolWidth}px` }">
          <div class="pool-water" :style="{ height: poolWaterHeight }">
            <div class="water-surface">
              <div class="wave wave1"></div>
              <div class="wave wave2"></div>
              <div class="wave wave3"></div>
            </div>
            <!-- 水流粒子效果 -->
            <div class="water-particles">
              <div
                v-for="i in particleCount"
                :key="i"
                class="particle"
                :style="{
                  left: `${Math.random() * 80 + 10}%`,
                  animationDelay: `${Math.random() * 4}s`,
                  animationDuration: `${3 + Math.random() * 2}s`,
                }"
              ></div>
            </div>
          </div>
          <div class="pool-label">
            <div class="pool-title">总线索池</div>
            <div class="pool-count">{{ statisticsData.total }}个</div>
            <!-- 已分配和待分配数量 - 紧凑布局 -->
            <div class="pool-stats">
              <div class="stat-item allocated">
                <span class="stat-label">已分配</span>
                <span class="stat-value">{{
                  statisticsData.already_distribute
                }}</span>
              </div>
              <div class="stat-divider">|</div>
              <div class="stat-item waiting">
                <span class="stat-label">待分配</span>
                <span class="stat-value">{{
                  statisticsData.waiting_distribute
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部部门容器 -->
      <div ref="departmentContainerRef" class="department-containers">
        <div
          v-for="(dept, index) in departmentData"
          :key="index"
          class="dept-container"
        >
          <!-- 流水管道 - 绝对定位在容器内部上方 -->
          <div class="pipe">
            <div class="pipe-flow">
              <div class="flow-stream"></div>
            </div>
          </div>

          <!-- 部门名称 -->
          <div class="dept-header">
            <div class="dept-name">{{ dept.department_name }}</div>
          </div>

          <!-- 水位容器 -->
          <div class="container-tank">
            <!-- 水位线 -->
            <div
              class="water-level"
              :style="{
                height: `${getWaterLevel(dept)}%`,
                background: getWaterGradient(dept),
              }"
            >
              <div class="level-surface">
                <div class="mini-wave mini-wave1"></div>
                <div class="mini-wave mini-wave2"></div>
              </div>
            </div>

            <!-- 容器刻度 -->
            <div class="tank-scale">
              <div class="scale-line scale-100">
                <div class="scale-label">100%</div>
              </div>
              <div class="scale-line scale-75">
                <div class="scale-label">75%</div>
              </div>
              <div class="scale-line scale-50">
                <div class="scale-label">50%</div>
              </div>
              <div class="scale-line scale-25">
                <div class="scale-label">25%</div>
              </div>
            </div>
          </div>

          <!-- 数据统计 -->
          <div class="dept-stats">
            <div class="stat-column">
              <div class="stat-value">{{
                dept.period_department_limit_upper
              }}</div>
              <div class="stat-label">上限</div>
            </div>
            <div class="stat-column">
              <div class="stat-value allocated">{{
                dept.department_already_distribute
              }}</div>
              <div class="stat-label">已分配</div>
            </div>
            <div class="stat-column">
              <div class="stat-value waiting">{{
                dept.department_waiting_distribute
              }}</div>
              <div class="stat-label">差额</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-spin>
</template>

<script setup lang="ts">
  import { ref, computed, watchEffect, onMounted, nextTick } from 'vue';
  import { useAppStore } from '@/store';

  // 部门数据类型定义
  interface DepartmentData {
    department_name: string;
    department_limit_upper: string;
    period_department_limit_upper: string;
    department_already_distribute: number;
    department_waiting_distribute: number;
  }

  // Props定义
  interface StatisticsData {
    total: number;
    already_distribute: number;
    waiting_distribute: number;
    departmentData?: DepartmentData[];
  }

  interface Props {
    statisticsData?: StatisticsData;
    loading?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    statisticsData: () => ({
      total: 0,
      already_distribute: 0,
      waiting_distribute: 0,
      departmentData: [],
    }),
    loading: false,
  });

  const appStore = useAppStore();

  // 响应式引用
  const departmentContainerRef = ref<HTMLElement>();
  const totalPoolWidth = ref<number>(850); // 默认宽度

  // 根据设备性能调整粒子数量
  const particleCount = computed(() => {
    // 检测设备性能和屏幕尺寸
    const isMobile = window.innerWidth <= 768;
    const isLowPerformance = navigator.hardwareConcurrency <= 4;

    if (isMobile || isLowPerformance) {
      return 6; // 移动设备或低性能设备减少粒子
    }
    return 8; // 桌面设备适中数量
  });

  // 部门数据
  const departmentData = computed(() => {
    return props.statisticsData.departmentData || [];
  });

  // 总线索池水位高度（基于总线索数量）
  const poolWaterHeight = computed(() => {
    const { total, already_distribute, waiting_distribute } =
      props.statisticsData;
    if (total === 0) return '15%';
    // 根据线索数量动态调整水位，最小20%，最大90%

    const percentage = ((waiting_distribute / total) * 100).toFixed(2);
    return `${percentage}%`;
  });

  // 计算容器宽度（根据部门数量自适应）
  const containerWidth = computed(() => {
    const count = departmentData.value.length;
    if (count === 0) return 100;
    if (count <= 3) return Math.floor(100 / count) - 2;
    if (count <= 6) return Math.floor(100 / Math.min(count, 4)) - 2;
    return Math.floor(100 / 6) - 2;
  });

  // 计算使用率
  const getUsageRate = (dept: DepartmentData) => {
    const limit = parseInt(dept.period_department_limit_upper, 10) || 0;
    if (limit === 0) return 0;

    const total = dept.department_already_distribute;
    return Math.min(Math.round((total / limit) * 100), 100);
  };

  // 计算水位高度
  const getWaterLevel = (dept: DepartmentData) => {
    const limit = parseInt(dept.period_department_limit_upper, 10) || 0;
    if (limit === 0) return 0;

    const total = dept.department_already_distribute;
    const percentage = Math.min((total / limit) * 100, 100);
    return Math.max(percentage, 5); // 最小显示5%，避免完全看不见
  };

  // 获取水位渐变色
  const getWaterGradient = (dept: DepartmentData) => {
    const usageRate = getUsageRate(dept);

    if (usageRate >= 90) {
      // 超负荷：红色渐变
      return 'linear-gradient(180deg, #ff7875 0%, #ff4d4f 50%, #f5222d 100%)';
    }
    if (usageRate >= 70) {
      // 高负荷：橙色渐变
      return 'linear-gradient(180deg, #ffa940 0%, #fa8c16 50%, #d46b08 100%)';
    }
    if (usageRate >= 40) {
      // 中等负荷：蓝色渐变
      return 'linear-gradient(180deg, #69c0ff 0%, #1890ff 50%, #096dd9 100%)';
    }
    // 低负荷：绿色渐变
    return 'linear-gradient(180deg, #95de64 0%, #52c41a 50%, #389e0d 100%)';
  };

  // 计算总线索池宽度以匹配底部部门容器总宽度
  const calculateTotalPoolWidth = async () => {
    await nextTick();
    if (!departmentContainerRef.value) return;

    const containerElement = departmentContainerRef.value;
    const deptContainers = containerElement.querySelectorAll('.dept-container');

    if (deptContainers.length === 0) {
      totalPoolWidth.value = 850; // 默认宽度
      return;
    }

    // 计算所有部门容器的总宽度
    let totalWidth = 0;
    let lastRight = 0;
    let firstLeft = Number.MAX_SAFE_INTEGER;

    deptContainers.forEach((container) => {
      const rect = container.getBoundingClientRect();
      const containerRect = containerElement.getBoundingClientRect();

      // 计算相对于父容器的位置
      const relativeLeft = rect.left - containerRect.left;
      const relativeRight = rect.right - containerRect.left;

      firstLeft = Math.min(firstLeft, relativeLeft);
      lastRight = Math.max(lastRight, relativeRight);
    });

    // 总宽度 = 最右边位置 - 最左边位置
    totalWidth = lastRight - firstLeft;

    // 设置最小和最大宽度限制
    totalPoolWidth.value = Math.max(300, Math.min(totalWidth, 1200));
  };

  // 监听部门数据变化，重新计算宽度
  watchEffect(() => {
    if (departmentData.value.length > 0) {
      calculateTotalPoolWidth();
    }
  });

  // 组件挂载后计算宽度
  onMounted(() => {
    calculateTotalPoolWidth();

    // 监听窗口大小变化
    const handleResize = () => {
      calculateTotalPoolWidth();
    };

    window.addEventListener('resize', handleResize);

    // 组件卸载时清理事件监听
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  });
</script>

<style scoped lang="less">
  .w100p {
    width: 100%;
  }

  .water-funnel-container {
    width: 100%;
    min-height: 380px;
    padding: 16px;
    position: relative;
    background: var(--color-bg-1);
    border-radius: 8px;
    overflow: hidden;
  }

  // 顶部总线索池
  .total-pool {
    display: flex;
    justify-content: center;
    margin-bottom: 80px; // 增加底部间距，为管道连接留出空间

    .pool-container {
      position: relative;
      min-width: 300px;
      max-width: 1200px;
      height: 100px;
      background: linear-gradient(145deg, #e6f7ff, #bae7ff);
      // border: 2px solid #1890ff;
      border-top: none;
      box-shadow: 0 6px 24px rgba(24, 144, 255, 0.15),
        inset 0 2px 6px rgba(255, 255, 255, 0.3);
      overflow: hidden;
      transition: width 0.3s ease-in-out;

      // 底部连接点，表示水流出口
      &::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 8px;
        background: linear-gradient(180deg, #1890ff, #096dd9);
        border-radius: 0 0 10px 10px;
        box-shadow: 0 2px 6px rgba(24, 144, 255, 0.4);
      }

      .pool-water {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(
          180deg,
          #69c0ff 0%,
          #1890ff 50%,
          #096dd9 100%
        );
        transition: height 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        // border-radius: 0 0 12px 12px;
        will-change: height;
        transform: translateZ(0); /* 启用硬件加速 */

        .water-surface {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 12px;
          overflow: hidden;
          will-change: transform;

          .wave {
            position: absolute;
            top: 0;
            left: -100%;
            width: 200%;
            height: 12px;
            background: linear-gradient(
              90deg,
              transparent 0%,
              rgba(255, 255, 255, 0.3) 25%,
              rgba(255, 255, 255, 0.5) 50%,
              rgba(255, 255, 255, 0.3) 75%,
              transparent 100%
            );
            border-radius: 50%;
            animation: wave-flow 4s linear infinite;
            will-change: transform;
            transform: translateZ(0);

            &.wave1 {
              animation-delay: 0s;
            }

            &.wave2 {
              animation-delay: 1.3s;
              opacity: 0.7;
            }

            &.wave3 {
              animation-delay: 2.6s;
              opacity: 0.5;
            }
          }
        }

        .water-particles {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          will-change: transform;

          .particle {
            position: absolute;
            width: 3px;
            height: 3px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: particle-float 4s ease-in-out infinite;
            will-change: transform, opacity;
            transform: translateZ(0);
          }
        }
      }

      .pool-label {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        color: white;
        font-weight: bold;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        z-index: 10;

        .pool-title {
          font-size: 14px;
          margin-bottom: 2px;
        }

        .pool-count {
          font-size: 18px;
          color: #fff;
          margin-bottom: 4px;
        }

        .pool-stats {
          display: inline-flex;
          align-items: center;
          gap: 6px;
          font-size: 12px;
          margin-top: 2px;

          .stat-item {
            display: inline-flex;
            align-items: center;
            gap: 2px;

            .stat-value {
              font-size: 12px;
              // font-weight: 700;
              line-height: 1;

              // &.allocated {
              //   color: #95de64; // 浅绿色，在深色背景上更清晰
              // }

              // &.waiting {
              //   color: #ffd666; // 浅黄色，在深色背景上更清晰
              // }
            }

            .stat-label {
              font-size: 11px;
              font-weight: 500;
              color: rgba(255, 255, 255, 0.9);
              line-height: 1;
            }

            // &.allocated {
            //   .stat-value {
            //     color: #95de64;
            //   }
            // }

            // &.waiting {
            //   .stat-value {
            //     color: #ffd666;
            //   }
            // }
          }

          .stat-divider {
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
            font-weight: 300;
            margin: 0 2px;
          }
        }
      }
    }
  }

  // 底部部门容器
  .department-containers {
    position: relative; // 为绝对定位的管道提供定位上下文
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: center;
    margin-top: 20px; // 为管道留出空间

    .dept-container {
      position: relative; // 为内部管道提供定位上下文
      width: 160px;
      background: var(--color-bg-1);
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      overflow: visible; // 允许管道超出容器边界
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      // 流水管道 - 绝对定位在容器上方中心
      .pipe {
        position: absolute;
        top: -75px; // 位于容器上方80px
        left: 50%;
        transform: translateX(-50%);
        width: 8px;
        height: 70px;
        background: linear-gradient(180deg, #1890ff, #096dd9);
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        z-index: 10;
        pointer-events: none; // 不阻挡鼠标事件

        // 管道底部连接效果
        &::after {
          content: '';
          position: absolute;
          bottom: -2px;
          left: 50%;
          transform: translateX(-50%);
          width: 12px;
          height: 4px;
          background: linear-gradient(90deg, transparent, #096dd9, transparent);
          border-radius: 0 0 6px 6px;
        }

        .pipe-flow {
          position: absolute;
          top: 0;
          left: 50%;
          transform: translateX(-50%) translateZ(0);
          width: 4px;
          height: 100%;
          overflow: hidden;
          will-change: transform;

          .flow-stream {
            position: absolute;
            top: -20px;
            left: 0;
            width: 100%;
            height: 20px;
            background: linear-gradient(
              180deg,
              rgba(255, 255, 255, 0.9) 0%,
              rgba(255, 255, 255, 0.6) 50%,
              rgba(255, 255, 255, 0.3) 100%
            );
            border-radius: 2px;
            animation: stream-flow 1.2s linear infinite;
            will-change: transform;
            transform: translateZ(0);
          }
        }
      }

      .dept-header {
        flex: 1;
        padding: 8px;
        display: flex;
        align-items: center;
        justify-content: center;

        .dept-name {
          font-size: 14px;
          font-weight: 600;
          color: var(--color-text-1);
          text-align: center;
          margin: 0;
        }
      }

      .container-tank {
        position: relative;
        width: 100%;
        height: 120px;
        background: linear-gradient(145deg, #e6f7ff, #bae7ff);
        // background: var(--color-fill-1);
        overflow: hidden;

        .water-level {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          transition: height 0.8s cubic-bezier(0.4, 0, 0.2, 1);
          // border-radius: 0 0 16px 16px;
          will-change: height;
          transform: translateZ(0);

          .level-surface {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            overflow: hidden;
            will-change: transform;

            .mini-wave {
              position: absolute;
              top: 0;
              left: -50%;
              width: 150%;
              height: 6px;
              background: linear-gradient(
                90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.4) 50%,
                transparent 100%
              );
              border-radius: 50%;
              animation: mini-wave-flow 3s linear infinite;
              will-change: transform;
              transform: translateZ(0);

              &.mini-wave1 {
                animation-delay: 0s;
              }

              &.mini-wave2 {
                animation-delay: 1.5s;
                opacity: 0.6;
              }
            }
          }
        }

        .tank-scale {
          position: absolute;
          right: 2px;
          top: 0px;
          bottom: 0px;
          width: 2px;

          .scale-line {
            position: absolute;
            right: 0;
            width: 12px;
            height: 1px;
            background: var(--color-fill-3);
            .scale-label {
              position: absolute;
              right: 16px;
              top: -4px;
              font-size: 8px;
              color: #fff;
            }

            &.scale-100 {
              top: 0;
              background: transparent;
              .scale-label {
                top: 2px;
              }
            }
            &.scale-75 {
              top: 25%;
            }
            &.scale-50 {
              top: 50%;
            }
            &.scale-25 {
              top: 75%;
            }

            &::after {
              content: '';
              position: absolute;
              right: 16px;
              top: -6px;
              font-size: 10px;
              color: rgba(0, 0, 0, 0.4);
            }
          }
        }
      }

      .dept-stats {
        display: flex;
        padding: 6px 4px;
        background: var(--color-bg-2);
        gap: 2px;

        .stat-column {
          flex: 1;
          text-align: center;
          padding: 0 2px;
          min-width: 0; // 允许内容收缩

          .stat-value {
            font-size: 14px;
            font-weight: 600;
            color: var(--color-text-1);
            margin-bottom: 1px;
            line-height: 1.2;
            display: block;

            &.allocated {
              color: #52c41a;
            }

            &.waiting {
              color: #faad14;
            }
          }

          .stat-label {
            font-size: 10px;
            color: var(--color-text-3);
            line-height: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }

  // 动画关键帧 - 优化性能版本
  @keyframes wave-flow {
    0% {
      transform: translate3d(-100%, 0, 0);
    }
    100% {
      transform: translate3d(0%, 0, 0);
    }
  }

  @keyframes particle-float {
    0%,
    100% {
      transform: translate3d(0, 0, 0) rotate(0deg);
      opacity: 0.6;
    }
    50% {
      transform: translate3d(0, -8px, 0) rotate(180deg);
      opacity: 1;
    }
  }

  @keyframes stream-flow {
    0% {
      transform: translate3d(0, -15px, 0);
    }
    100% {
      transform: translate3d(0, 65px, 0);
    }
  }

  @keyframes mini-wave-flow {
    0% {
      transform: translate3d(-50%, 0, 0);
    }
    100% {
      transform: translate3d(0%, 0, 0);
    }
  }

  // 性能优化：减少重绘和回流
  .water-funnel-container * {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000px;
    perspective: 1000px;
  }

  // 用户偏好：减少动画
  @media (prefers-reduced-motion: reduce) {
    .water-funnel-container {
      .wave,
      .particle,
      .flow-stream,
      .mini-wave {
        animation-duration: 8s; // 大幅减慢动画
        animation-iteration-count: 1; // 只播放一次
      }

      .water-level {
        transition-duration: 0.3s; // 减少过渡时间
      }
    }
  }

  // Arco Design 样式覆盖
  :deep(.arco-card) {
    .arco-card-header {
      border-bottom: 1px solid var(--color-border-2);
      padding: 16px 20px;

      .arco-card-header-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--color-text-1);
      }
    }

    .arco-card-body {
      padding: 20px;
    }
  }
</style>
