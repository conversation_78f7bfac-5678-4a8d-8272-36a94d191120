<template>
  <div class="statistics-cards">
    <a-row :gutter="16">
      <!-- 总线索数 -->
      <a-col :span="8">
        <a-card class="statistic-card" :bordered="false">
          <a-spin :loading="loading">
            <a-statistic
              title="总线索数"
              :value="totalThread"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <icon-bar-chart class="statistic-icon" />
              </template>
              <template #suffix>
                <span>
                  <span class="statistic-unit">个</span>
                  <!-- 昨日（只有选择今天时才显示） -->
                  <span v-if="showYesterdayData" class="statistic-extra">
                    <span class="percentage-label">昨日:</span>
                    <span class="percentage"
                      >{{ yesterdayInfo.yesterday_total_thread }}个</span
                    >
                  </span>
                </span>
              </template>
            </a-statistic>
          </a-spin>
        </a-card>
      </a-col>

      <!-- 已分配数量 -->
      <a-col :span="8">
        <a-card class="statistic-card" :bordered="false">
          <a-spin :loading="loading">
            <a-statistic
              title="已分配"
              :value="alreadyDistribute"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <icon-check-circle class="statistic-icon" />
              </template>
              <template #suffix>
                <span class="statistic-unit">个</span>
                <!-- 昨日（只有选择今天时才显示） -->
                <span v-if="showYesterdayData" class="statistic-extra">
                  <span class="percentage-label">昨日:</span>
                  <span class="percentage"
                    >{{ yesterdayInfo.yesterday_already_distribute }}个</span
                  >
                </span>
              </template>
            </a-statistic>
          </a-spin>
        </a-card>
      </a-col>

      <!-- 待分配数量 -->
      <a-col :span="8">
        <a-card class="statistic-card" :bordered="false">
          <a-spin :loading="loading">
            <a-statistic
              title="待分配"
              :value="waitingDistribute"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <icon-clock-circle class="statistic-icon" />
              </template>
              <template #suffix>
                <span class="statistic-unit">个</span>
                <!-- 昨日（只有选择今天时才显示） -->
                <span v-if="showYesterdayData" class="statistic-extra">
                  <span class="percentage-label">昨日:</span>
                  <span class="percentage"
                    >{{ yesterdayInfo.yesterday_waiting_distribute }}个</span
                  >
                </span>
              </template>
            </a-statistic>
          </a-spin>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import {
    // IconFileText,
    IconCheckCircle,
    IconClockCircle,
  } from '@arco-design/web-vue/es/icon';
  import dayjs from 'dayjs';

  // Props定义
  interface Props {
    totalThread?: number;
    alreadyDistribute?: number;
    waitingDistribute?: number;
    loading?: boolean;
    timeRange?: string[];
    yesterdayInfo?: {
      yesterday_total_thread: number;
      yesterday_already_distribute: number;
      yesterday_waiting_distribute: number;
    };
  }

  const props = withDefaults(defineProps<Props>(), {
    totalThread: 0,
    alreadyDistribute: 0,
    waitingDistribute: 0,
    loading: false,
    timeRange: () => [],
  });

  // 计算分配率
  const distributionRate = computed(() => {
    if (props.totalThread === 0) return 0;
    return Math.round((props.alreadyDistribute / props.totalThread) * 100);
  });

  // 计算待分配率
  const waitingRate = computed(() => {
    if (props.totalThread === 0) return 0;
    return Math.round((props.waitingDistribute / props.totalThread) * 100);
  });

  // 判断是否显示昨日数据（只有当选择的时间范围是今天时才显示）
  const showYesterdayData = computed(() => {
    // 如果没有时间范围，不显示
    if (!props.timeRange || props.timeRange.length !== 2) {
      return false;
    }

    const today = dayjs().format('YYYY-MM-DD');

    // 只有当开始日期和结束日期都是今天时才显示
    return props.timeRange[0] === today && props.timeRange[1] === today;
  });
</script>

<style scoped lang="less">
  .statistics-cards {
    .statistic-card {
      // height: 120px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
      }

      :deep(.arco-card-body) {
        padding: 20px;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      :deep(.arco-statistic) {
        .arco-statistic-title {
          font-size: 14px;
          color: var(--color-text-2);
          margin-bottom: 8px;
          font-weight: 500;
        }

        .arco-statistic-content {
          display: flex;
          align-items: center;

          .arco-statistic-value {
            font-size: 28px;
            font-weight: 600;
            line-height: 1;
          }
        }
      }

      .statistic-icon {
        font-size: 20px;
        margin-right: 8px;
      }

      .statistic-unit {
        font-size: 14px;
        color: var(--color-text-3);
        margin-left: 4px;
      }

      .statistic-extra {
        display: inline-block;
        align-items: center;
        gap: 4px;
        margin-left: 10px;
        vertical-align: center;

        .percentage {
          font-size: 14px;
          font-weight: 600;
          color: var(--color-text-1);
          margin-left: 5px;
        }

        .percentage-label {
          font-size: 12px;
          color: var(--color-text-3);
        }
      }
    }
  }
</style>
