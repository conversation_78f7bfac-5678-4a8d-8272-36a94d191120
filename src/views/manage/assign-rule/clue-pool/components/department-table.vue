<template>
  <a-card title="部门分配统计" :bordered="false">
    <template #extra>
      <a-space>
        <a-tooltip content="刷新数据">
          <a-button
            type="text"
            size="small"
            :loading="loading"
            @click="handleRefresh"
          >
            <template #icon>
              <icon-refresh />
            </template>
          </a-button>
        </a-tooltip>
      </a-space>
    </template>

    <a-spin :loading="loading" class="w100p">
      <a-table
        :columns="columns"
        :data="tableData"
        :pagination="false"
        :scroll="{ y: 350 }"
        size="small"
        :bordered="false"
        :row-class-name="getRowClassName"
      >
        <!-- 部门名称列 -->
        <template #department="{ record }">
          <div class="department-cell">
            <!-- <icon-team class="department-icon" /> -->
            <span class="department-name">{{ record.department_name }}</span>
          </div>
        </template>

        <!-- 分配上限列 -->
        <template #limit="{ record }">
          <div class="limit-edit-wrapper">
            <table-inline-edit
              :model-value="String(record.department_limit_upper || '0')"
              type="input"
              placeholder="点击编辑分配上限"
              :max-length="6"
              :on-save="(value) => handleSaveDepartmentLimit(record, value)"
              edit-mode="click"
              save-mode="manual"
              class="limit-edit"
            />
          </div>
        </template>

        <!-- 已分配数量列 -->
        <template #distributed="{ record }">
          <div class="number-cell">
            <span class="number">{{
              record.department_already_distribute
            }}</span>
            <span class="percentage">
              ({{ getDistributionRate(record) }}%)
            </span>
          </div>
        </template>

        <!-- 待分配数量列 -->
        <template #waiting="{ record }">
          <div class="number-cell">
            <span class="number">{{
              record.department_waiting_distribute
            }}</span>
          </div>
        </template>

        <!-- 使用率列 -->
        <template #usage="{ record }">
          <div class="usage-cell">
            <a-progress
              :percent="getUsageRate(record)"
              :color="getUsageColor(record)"
              size="small"
              :show-text="false"
            />
            <span class="usage-text"> {{ getUsageRate(record) }}% </span>
          </div>
        </template>

        <!-- 状态列 -->
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record)" size="small">
            {{ getStatusText(record) }}
          </a-tag>
        </template>
      </a-table>
    </a-spin>
  </a-card>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import request from '@/api/request';
  import {
    // IconTeam
    IconRefresh,
  } from '@arco-design/web-vue/es/icon';
  import TableInlineEdit from '@/components/table-inline-edit/TableInlineEdit.vue';

  // 部门数据类型定义
  interface DepartmentData {
    department_id?: number;
    department_name: string;
    department_limit_upper: string;
    department_already_distribute: number;
    department_waiting_distribute: number;
  }

  // Props定义
  interface Props {
    departmentData?: DepartmentData[];
    loading?: boolean;
    currentRuleId?: number | null;
    isSingleDay?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    departmentData: () => [],
    loading: false,
    currentRuleId: null,
    isSingleDay: true,
  });

  // Emits定义
  interface Emits {
    (e: 'refresh'): void;
  }

  const emit = defineEmits<Emits>();

  // 保存状态
  const savingLimits = ref<Set<string>>(new Set());

  // 表格列配置 - 使用计算属性实现动态列显示
  const columns = computed(() => {
    const baseColumns = [
      {
        title: '部门名称',
        dataIndex: 'department_name',
        slotName: 'department',
        width: 150,
      },
      {
        title: '单日分配上限',
        dataIndex: 'department_limit_upper',
        slotName: 'limit',
        width: 100,
        align: 'center' as const,
      },
    ];

    // 只有在多天时间段时才显示"总上限"列
    if (!props.isSingleDay) {
      baseColumns.push({
        title: '总上限',
        dataIndex: 'period_department_limit_upper',
        width: 120,
        align: 'center' as const,
      });
    }

    // 添加其余列
    baseColumns.push(
      {
        title: '已分配',
        dataIndex: 'department_already_distribute',
        slotName: 'distributed',
        width: 120,
        align: 'center' as const,
      },
      {
        title: '差额',
        dataIndex: 'department_waiting_distribute',
        slotName: 'waiting',
        width: 120,
        align: 'center' as const,
      },
      {
        title: '状态',
        dataIndex: 'status',
        slotName: 'status',
        width: 100,
        align: 'center' as const,
      }
    );

    return baseColumns;
  });

  // 表格数据
  const tableData = computed(() => {
    return props.departmentData.map((item, index) => ({
      ...item,
      key: index,
    }));
  });

  // 获取分配率
  const getDistributionRate = (record: DepartmentData) => {
    const limit = parseInt(record.period_department_limit_upper, 10) || 0;
    if (limit === 0) return 0;
    return Math.round((record.department_already_distribute / limit) * 100);
  };

  // 获取使用率
  const getUsageRate = (record: DepartmentData) => {
    const limit = parseInt(record.period_department_limit_upper, 10) || 0;
    const total = record.department_already_distribute;
    if (limit === 0) return 0;
    return Math.min(Math.round((total / limit) * 100), 100);
  };

  // 获取使用率颜色
  const getUsageColor = (record: DepartmentData) => {
    const rate = getUsageRate(record);
    if (rate >= 90) return '#ff4d4f'; // 红色
    if (rate >= 70) return '#faad14'; // 橙色
    return '#52c41a'; // 绿色
  };

  // 获取状态文本
  const getStatusText = (record: DepartmentData) => {
    const rate = getUsageRate(record);
    if (rate >= 100) return '已满';
    if (rate >= 90) return '即将满额';
    if (rate >= 70) return '正常';
    return '充足';
  };

  // 获取状态颜色
  const getStatusColor = (record: DepartmentData) => {
    const rate = getUsageRate(record);
    if (rate >= 100) return 'red';
    if (rate >= 90) return 'orange';
    if (rate >= 70) return 'blue';
    return 'green';
  };

  // 获取行样式类名
  const getRowClassName = (record: DepartmentData) => {
    const rate = getUsageRate(record);
    if (rate >= 100) return 'row-full';
    if (rate >= 90) return 'row-warning';
    return '';
  };

  // 处理刷新
  const handleRefresh = () => {
    emit('refresh');
  };

  // 验证正整数
  const validatePositiveInteger = (value: string): boolean => {
    const num = parseInt(value, 10);
    return !Number.isNaN(num) && num >= 0 && String(num) === value.trim();
  };

  // 保存部门分配上限
  const handleSaveDepartmentLimit = async (
    record: DepartmentData,
    newValue: string
  ): Promise<void> => {
    // 输入验证
    if (!newValue.trim()) {
      Message.error('分配上限不能为空');
      throw new Error('分配上限不能为空');
    }

    if (!validatePositiveInteger(newValue)) {
      Message.error('分配上限必须为正整数');
      throw new Error('分配上限必须为正整数');
    }

    const newLimit = parseInt(newValue, 10);
    const currentLimit = parseInt(record.department_limit_upper, 10) || 0;

    // 验证规则ID是否存在
    if (!props.currentRuleId) {
      Message.error('请先选择分配规则');
      throw new Error('请先选择分配规则');
    }

    // 构建保存参数 - 根据现有的分配规则保存逻辑
    // 需要传递当前选中的分配规则ID和部门限制信息
    const saveParams = {
      // 使用传入的当前规则ID
      id: props.currentRuleId,
      department_limit_upper: {
        [record.department_id]: newLimit, // 使用部门名称作为key
      },
    };

    // 设置保存状态
    const saveKey = `${record.department_name}`;
    savingLimits.value.add(saveKey);

    try {
      const response = await request(
        '/api/thread/distributeFunnelEditLimitUpper',
        saveParams
      );

      if (response.code === 0) {
        Message.success('分配上限保存成功');
        // 更新本地数据
        record.department_limit_upper = String(newLimit);
        // 刷新数据
        emit('refresh');
      } else {
        Message.error(response.msg || '保存失败');
        throw new Error(response.msg || '保存失败');
      }
    } catch (error) {
      console.error('保存分配上限失败:', error);
      Message.error('保存失败，请稍后重试');
      throw error;
    } finally {
      savingLimits.value.delete(saveKey);
    }
  };
</script>

<style scoped lang="less">
  .w100p {
    width: 100%;
  }

  .department-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .department-icon {
      color: var(--color-text-3);
      font-size: 14px;
    }

    .department-name {
      font-weight: 500;
      color: var(--color-text-1);
    }
  }

  .number-cell {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;

    .number {
      font-weight: 600;
      color: var(--color-text-1);
    }

    .percentage {
      font-size: 12px;
      color: var(--color-text-3);
    }
  }

  .usage-cell {
    display: flex;
    align-items: center;
    gap: 4px;

    .usage-text {
      font-size: 12px;
      font-weight: 500;
      color: var(--color-text-1);
    }
  }

  // 分配上限编辑样式
  .limit-edit-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;

    .limit-edit {
      width: 100%;
      min-width: 80px;
      max-width: 120px;

      :deep(.table-inline-edit) {
        .display-mode {
          .text-content {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 24px;
            padding: 2px 8px;
            background: rgb(var(--primary-1));
            color: rgb(var(--primary-6));
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background: rgb(var(--primary-2));
              color: rgb(var(--primary-7));
            }
          }
        }

        .edit-mode {
          .edit-input {
            text-align: center;
            font-weight: 500;
          }

          .edit-actions {
            margin-top: 4px;
            display: flex;
            justify-content: center;
            gap: 4px;
          }
        }
      }
    }
  }

  :deep(.arco-card) {
    .arco-card-header {
      border-bottom: 1px solid var(--color-border-2);
      padding: 16px 20px;

      .arco-card-header-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--color-text-1);
      }
    }

    .arco-card-body {
      padding: 20px;
    }
  }
</style>
