<template>
  <div class="department-limit-setting">
    <div v-if="!departmentList.length" class="empty-state">
      <a-empty description="请先选择分配部门" />
    </div>

    <div v-else class="department-list">
      <div
        v-for="department in departmentList"
        :key="department.id"
        class="department-item"
      >
        <div class="department-info">
          <a-tag color="blue" size="small">
            {{ department.department_name }}
          </a-tag>
        </div>
        <div class="limit-input">
          <a-input-number
            :model-value="getLimitValue(department.id)"
            :min="0"
            :max="9999"
            :precision="0"
            :step="1"
            placeholder="请输入上限"
            style="width: 120px"
            size="mini"
            :class="{ 'input-error': hasValidationError(department.id) }"
            @change="(value) => handleLimitChange(department.id, value)"
            @blur="() => handleInputBlur(department.id)"
          />
          <!-- <div v-if="getValidationMessage(department.id)" class="error-message">
            {{ getValidationMessage(department.id) }}
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, watch, onMounted } from 'vue';
  import request from '@/api/request';

  // 部门数据类型定义
  interface Department {
    id: number;
    department_name: string;
    child?: Department[];
  }

  // Props 定义
  interface Props {
    modelValue?: Record<string, number>;
    departmentIds?: number[];
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({}),
    departmentIds: () => [],
  });

  // Emits 定义
  const emit = defineEmits<{
    'update:modelValue': [value: Record<string, number>];
    'change': [value: Record<string, number>];
  }>();

  // 响应式数据
  const allDepartments = ref<Department[]>([]);
  const loading = ref(false);
  const validationErrors = ref<Record<string, string>>({});

  // 计算属性 - 当前选中的部门列表
  const departmentList = computed(() => {
    if (!props.departmentIds.length || !allDepartments.value.length) {
      return [];
    }

    // 扁平化所有部门数据
    const flatDepartments: Department[] = [];
    const flattenDepartments = (departments: Department[]) => {
      departments.forEach((dept) => {
        flatDepartments.push(dept);
        if (dept.child && dept.child.length > 0) {
          flattenDepartments(dept.child);
        }
      });
    };
    flattenDepartments(allDepartments.value);

    // 根据选中的部门ID筛选部门
    return flatDepartments.filter((dept) =>
      props.departmentIds.includes(dept.id)
    );
  });

  // 获取部门的上限值
  const getLimitValue = (departmentId: number): number | undefined => {
    const key = String(departmentId);
    return props.modelValue[key];
  };

  // 验证单个部门的上限值
  const validateDepartmentLimit = (
    departmentId: number,
    value: number | undefined
  ): string => {
    if (value === undefined || value === null) {
      return '请输入单日分配上限';
    }

    if (!Number.isInteger(value) || value < 0) {
      return '请输入大于等于0的整数';
    }

    return '';
  };

  // 检查是否有验证错误
  const hasValidationError = (departmentId: number): boolean => {
    const key = String(departmentId);
    return !!validationErrors.value[key];
  };

  // 获取验证错误信息
  const getValidationMessage = (departmentId: number): string => {
    const key = String(departmentId);
    return validationErrors.value[key] || '';
  };

  // 处理输入失焦事件
  const handleInputBlur = (departmentId: number) => {
    const value = getLimitValue(departmentId);
    const key = String(departmentId);
    const errorMessage = validateDepartmentLimit(departmentId, value);

    if (errorMessage) {
      validationErrors.value[key] = errorMessage;
    } else {
      delete validationErrors.value[key];
    }
  };

  // 处理上限值变化
  const handleLimitChange = (
    departmentId: number,
    value: number | undefined
  ) => {
    const key = String(departmentId);
    const newValue = { ...props.modelValue };

    // 清除之前的验证错误
    delete validationErrors.value[key];

    // 实时验证
    const errorMessage = validateDepartmentLimit(departmentId, value);
    if (errorMessage && value !== undefined) {
      validationErrors.value[key] = errorMessage;
    }

    if (value !== undefined && value >= 0 && Number.isInteger(value)) {
      newValue[key] = value;
    } else {
      delete newValue[key];
    }

    emit('update:modelValue', newValue);
    emit('change', newValue);
  };

  // 获取部门列表数据
  const fetchDepartments = async () => {
    loading.value = true;
    try {
      const response = await request('/api/department/list', {});
      allDepartments.value = response.data || [];
    } catch (error) {
      console.error('获取部门列表失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 监听部门ID变化，清理无效的上限设置
  watch(
    () => props.departmentIds,
    (newIds) => {
      if (!newIds.length) {
        emit('update:modelValue', {});
        validationErrors.value = {}; // 清理验证错误
        return;
      }

      const currentLimits = props.modelValue;
      const newLimits: Record<string, number> = {};
      const newErrors: Record<string, string> = {};

      // 只保留仍然被选中的部门的上限设置和验证错误
      newIds.forEach((id) => {
        const key = String(id);
        if (currentLimits[key]) {
          newLimits[key] = currentLimits[key];
        }
        if (validationErrors.value[key]) {
          newErrors[key] = validationErrors.value[key];
        }
      });

      // 更新验证错误状态
      validationErrors.value = newErrors;

      // 如果有变化才更新
      if (JSON.stringify(newLimits) !== JSON.stringify(currentLimits)) {
        emit('update:modelValue', newLimits);
      }
    },
    { immediate: true }
  );

  // 验证所有部门的上限设置
  const validateAll = (): boolean => {
    let hasError = false;
    const newErrors: Record<string, string> = {};

    props.departmentIds.forEach((departmentId) => {
      const value = getLimitValue(departmentId);
      const errorMessage = validateDepartmentLimit(departmentId, value);

      if (errorMessage) {
        const key = String(departmentId);
        newErrors[key] = errorMessage;
        hasError = true;
      }
    });

    validationErrors.value = newErrors;
    return !hasError;
  };

  // 清除所有验证错误
  const clearValidation = () => {
    validationErrors.value = {};
  };

  // 组件挂载时获取部门数据
  onMounted(() => {
    fetchDepartments();
  });

  // 暴露方法给父组件
  defineExpose({
    validateAll,
    clearValidation,
  });
</script>

<style lang="less" scoped>
  .department-limit-setting {
    width: 100%;
    .empty-state {
      padding: 20px;
      text-align: center;
    }

    .department-list {
      width: 100%;
      .department-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 4px;
        border-radius: 6px;
        border-bottom: 1px solid var(--color-border-1);
        width: 100%;

        &:last-child {
          margin-bottom: 0;
        }

        .department-info {
          flex: 1;
          display: flex;
          align-items: center;
        }

        .limit-input {
          flex-shrink: 0;
          margin-left: 12px;
          position: relative;

          .input-error {
            :deep(.arco-input-number) {
              border-color: rgb(var(--danger-6));
              box-shadow: 0 0 0 2px rgba(var(--danger-2), 0.3);
            }
          }

          .error-message {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            font-size: 12px;
            color: rgb(var(--danger-6));
            line-height: 1.4;
            margin-top: 2px;
            white-space: nowrap;
            z-index: 1;
          }
        }
      }
    }
  }
</style>
