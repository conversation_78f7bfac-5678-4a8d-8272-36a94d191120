<template>
  <div class="filter-form">
    <!-- 分配规则选择 - 单独一行 -->
    <div class="rule-section">
      <!-- <div class="section-label">分配规则：</div> -->
      <div class="rule-tags-container">
        <div class="filter-item">
          <!-- 现有规则标签 -->
          <a-tag
            v-for="rule in distributeRules"
            :key="rule.id"
            :color="formData.distributeRuleId === rule.id ? 'blue' : ''"
            :checkable="true"
            :checked="formData.distributeRuleId === rule.id"
            class="rule-tag"
            @click="handleRuleSelect(rule.id)"
          >
            {{ rule.rule_name || rule.name }}
            <!-- 编辑按钮 -->
            <icon-edit
              v-if="rule.id === formData.distributeRuleId"
              class="edit-icon"
              @click.stop="showAddRuleModal(rule)"
            />
            <!-- 删除按钮 -->
            <a-popconfirm
              :content="`确定删除【${rule.rule_name || rule.name}】吗?`"
              @ok="deleteRule(rule.id)"
            >
              <icon-delete
                v-if="rule.id === formData.distributeRuleId"
                class="delete-icon ml-5"
                @click.stop
              />
            </a-popconfirm>
          </a-tag>
        </div>

        <div class="filter-item right">
          <!-- 刷新按钮 -->
          <a-tag
            type="text"
            class="rule-tag cur-por"
            @click="fetchDistributeRules"
          >
            刷新
            <template #icon>
              <icon-refresh />
            </template>
          </a-tag>
          <!-- 新建规则标签 -->
          <a-tag class="rule-tag add-rule-tag" @click="showAddRuleModal">
            <template #icon>
              <icon-plus />
            </template>
            新建规则
          </a-tag>
          <c-range-picker
            v-model="formData.timeRange"
            :need-default="false"
            :allow-clear="false"
            style="width: 280px"
            size="small"
            @change="handleTimeChange"
          />
        </div>
      </div>
    </div>
    <DistributeAddRuleModal ref="addRuleModal" @refresh="refresh" />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue';
  import {
    IconSearch,
    IconRefresh,
    IconPlus,
  } from '@arco-design/web-vue/es/icon';
  import { Message } from '@arco-design/web-vue';
  import request from '@/api/request';
  import dayjs from 'dayjs';
  import DistributeAddRuleModal from './distribute-add-rule-modal.vue';

  // Props定义
  interface Props {
    distributeRuleId?: number;
    timeRange?: string[];
    loading?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    distributeRuleId: 1,
    timeRange: () => [],
    loading: false,
  });

  // Emits定义
  interface Emits {
    (e: 'update:distributeRuleId', value: number): void;
    (e: 'update:timeRange', value: string[]): void;
    (e: 'search'): void;
  }

  const emit = defineEmits<Emits>();

  // 分配规则数据类型
  interface DistributeRule {
    id: number;
    name: string;
  }

  const addRuleModal = ref();
  const showAddRuleModal = (info: any) => addRuleModal.value.show(info);

  // 表单数据
  const formData = reactive({
    distributeRuleId: props.distributeRuleId,
    timeRange: props.timeRange,
  });

  // 分配规则列表
  const distributeRules = ref<DistributeRule[]>([]);
  const ruleLoading = ref(false);

  // 新建规则相关状态
  const showNewRuleInput = ref(false);
  const newRuleName = ref('');
  const createRuleLoading = ref(false);
  const newRuleInputRef = ref();

  // 计算属性：是否可以搜索
  const canSearch = computed(() => {
    return formData.distributeRuleId && formData.timeRange.length === 2;
  });

  // 获取分配规则列表
  const fetchDistributeRules = async () => {
    ruleLoading.value = true;
    try {
      // 使用分配规则管理的API接口
      const response = await request('/api/thread/distributeFunnelList', {
        type: 'clue', // 线索分配规则
        page: 1,
        pageSize: 100, // 获取所有规则
      });

      if (response.code === 0) {
        distributeRules.value = response.data.data || [];

        if (!formData.distributeRuleId) {
          formData.distributeRuleId = distributeRules.value[0].id;
          emit('update:distributeRuleId', formData.distributeRuleId);
          emit('search');
        }
        // Message.success('刷新成功');
      } else {
        Message.error(response.msg || '获取分配规则失败');
      }
    } catch (error) {
      console.warn('获取分配规则失败，使用默认数据:', error);
      Message.error('获取分配规则失败');
    } finally {
      ruleLoading.value = false;
    }
  };

  const refresh = (id: any) => {
    if (id) {
      formData.distributeRuleId = id;
    }
    fetchDistributeRules();
  };

  // 处理分配规则选择
  const handleRuleSelect = (ruleId: number) => {
    formData.distributeRuleId = ruleId;
    emit('update:distributeRuleId', ruleId);
    emit('search');
  };

  // 显示新建规则输入框
  const showCreateNewRule = () => {
    showNewRuleInput.value = true;
    newRuleName.value = '';
    nextTick(() => {
      newRuleInputRef.value?.focus();
    });
  };

  // 取消新建规则
  const cancelCreateRule = () => {
    showNewRuleInput.value = false;
    newRuleName.value = '';
  };

  // 处理输入框失焦
  const handleNewRuleBlur = () => {
    // 延迟执行，避免与按钮点击冲突
    setTimeout(() => {
      if (!createRuleLoading.value) {
        cancelCreateRule();
      }
    }, 200);
  };

  // 处理时间范围变化
  const handleTimeChange = (value: string[]) => {
    emit('update:timeRange', value);
    emit('search');
  };

  // 处理搜索
  const handleSearch = () => {
    if (!canSearch.value) {
      Message.warning('请选择分配规则和时间范围');
      return;
    }
    emit('search');
  };

  // 处理重置
  const handleReset = () => {
    // 重置为默认值
    formData.distributeRuleId = 1;

    // 设置默认时间范围为今天
    formData.timeRange = [
      dayjs().format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD'),
    ];

    // 触发更新
    emit('update:distributeRuleId', formData.distributeRuleId);
    emit('update:timeRange', formData.timeRange);
  };

  const deleteRule = async (id: number) => {
    try {
      const response = await request(
        '/api/thread/distributeFunnelDelLimitUpper',
        {
          id,
        }
      );

      if (response.code === 0) {
        Message.success('删除成功');
        formData.distributeRuleId = null;
        fetchDistributeRules();
      } else {
        Message.error(response.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除分配规则失败:', error);
      Message.error('删除失败，请稍后重试');
    }
  };
  // 监听props变化
  watch(
    () => props.distributeRuleId,
    (newVal) => {
      formData.distributeRuleId = newVal;
    }
  );

  watch(
    () => props.timeRange,
    (newVal) => {
      formData.timeRange = newVal;
    }
  );

  // 组件挂载时获取分配规则列表
  onMounted(() => {
    fetchDistributeRules();
  });
</script>

<style scoped lang="less">
  .filter-form {
    border-radius: 6px;

    .rule-section {
      display: flex;
      align-items: center;

      .section-label {
        font-size: 14px;
        font-weight: 500;
        color: rgb(var(--color-text-1));
        width: 80px;
      }

      .rule-tags-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        .rule-tag {
          margin-right: 8px;
          margin-bottom: 8px;
          border: 2px solid rgb(var(--color-border-2));
          border-radius: 8px;
          padding: 6px 12px;
          font-weight: 500;
          font-size: 14px;
          transition: all 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
          background: rgb(var(--color-bg-1));
          position: relative;
          min-height: 32px;
          display: inline-flex;
          align-items: center;

          &:hover {
            border-color: rgb(var(--primary-5));
            // box-shadow: 0 2px 8px rgba(var(--primary-6), 0.15);
            // transform: translateY(-1px);
            background: rgb(var(--color-bg-2));
          }

          &:active {
            transform: translateY(0);
            box-shadow: 0 1px 4px rgba(var(--primary-6), 0.2);
          }

          // 选中状态样式增强
          &.arco-tag-checked {
            border-color: rgb(var(--primary-6));
            background: linear-gradient(
              135deg,
              rgb(var(--primary-1)),
              rgb(var(--primary-2))
            );
            color: rgb(var(--primary-6));
            box-shadow: 0 2px 8px rgba(var(--primary-6), 0.2);
          }
        }

        .add-rule-tag {
          cursor: pointer;
          // border: 2px dashed rgb(var(--success-5)) !important;
          background: linear-gradient(
            135deg,
            rgb(var(--success-1)),
            rgba(var(--success-2), 0.8)
          ) !important;
          color: rgb(var(--success-6)) !important;
          font-weight: 600;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
              90deg,
              transparent,
              rgba(255, 255, 255, 0.3),
              transparent
            );
            transition: left 0.5s;
          }

          &:hover {
            border-color: rgb(var(--success-6)) !important;
            // background: linear-gradient(
            //   135deg,
            //   rgb(var(--success-2)),
            //   rgb(var(--success-3))
            // ) !important;
            box-shadow: 0 3px 12px rgba(var(--success-6), 0.25) !important;
            // transform: translateY(-2px) scale(1.02);

            &::before {
              left: 100%;
            }
          }

          &:active {
            transform: translateY(-1px) scale(1.01);
            box-shadow: 0 2px 8px rgba(var(--success-6), 0.3) !important;
          }

          .arco-icon {
            margin-right: 4px;
            font-size: 14px;
            animation: pulse 2s infinite;
          }
        }

        // 刷新按钮特殊样式
        .rule-tag.cur-por {
          border-color: rgb(var(--color-border-3));
          background: rgb(var(--color-fill-2));
          color: rgb(var(--color-text-2));

          &:hover {
            border-color: rgb(var(--color-border-4));
            background: rgb(var(--color-fill-3));
            color: rgb(var(--color-text-1));
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
          }

          .arco-icon {
            transition: transform 0.3s ease;
          }

          &:hover .arco-icon {
            transform: rotate(180deg);
          }

          // 加载状态
          &.arco-tag-loading {
            .arco-icon {
              animation: spin 1s linear infinite;
            }
          }
        }

        // 脉冲动画
        @keyframes pulse {
          0% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.1);
          }
          100% {
            transform: scale(1);
          }
        }

        // 旋转动画
        @keyframes spin {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }
        .new-rule-input {
          display: inline-flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border: 1px dashed rgb(var(--green-6));
          border-radius: 6px;
          background-color: rgb(var(--green-1));

          .arco-btn {
            height: 24px;
            padding: 0 8px;
            font-size: 12px;
          }
        }

        .rule-hint {
          margin-top: 8px;
          font-size: 12px;
          color: rgb(var(--color-text-3));
          font-style: italic;
        }
      }
    }

    .filter-section {
      display: flex;
      align-items: center;
      gap: 16px;
      flex-wrap: wrap;

      .filter-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .item-label {
          font-size: 14px;
          color: rgb(var(--color-text-2));
          white-space: nowrap;
        }
      }

      .action-buttons {
        display: flex;
        gap: 8px;
        margin-left: auto;

        @media (max-width: 768px) {
          margin-left: 0;
          width: 100%;
        }
      }
    }
  }

  .edit-icon {
    margin-left: 4px;
  }
</style>
