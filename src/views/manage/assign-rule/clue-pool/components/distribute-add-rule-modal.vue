<template>
  <d-modal
    v-model:visible="visible"
    width="500px"
    :mask-closable="false"
    :esc-to-close="false"
    :title="`${formModel.id ? '编辑' : '添加'}分配规则`"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formModel" :auto-label-width="true">
      <a-form-item label="规则名称" field="rule_name" :rules="requiredRule">
        <a-input
          v-model="formModel.rule_name"
          allow-clear
          placeholder="请输入"
        />
      </a-form-item>
      <!-- tab切换 类型 -->
      <a-form-item label="类型" field="type" :rules="requiredRule">
        <dict-radio
          v-model="formModel.type"
          :data-list="tableType"
          @change="formModel.type_value = ''"
        />
      </a-form-item>

      <a-form-item
        v-if="formModel.type === 'order'"
        label="订单类型"
        :rules="requiredRuleArr"
        field="type_value"
      >
        <dict-select
          v-model="formModel.type_value"
          placeholder="请选择"
          :data-list="orderTypeListM"
        />
      </a-form-item>
      <a-form-item
        v-else-if="formModel.type === 'thread'"
        label="线索类型"
        field="type_value"
        :rules="requiredRule"
      >
        <dict-select
          v-model="formModel.type_value"
          :data-list="clueTypeListM"
          placeholder="请选择"
        />
      </a-form-item>
      <a-form-item
        label="来源部门"
        field="source_department_ids"
        :rules="requiredRule"
      >
        <template #label>
          <span> 来源部门 </span>
        </template>
        <request-tree-select
          v-model="formModel.source_department_ids"
          request-url="/api/department/list"
          label-key="department_name"
          child-key="child"
          placeholder="请选择"
          multiple
        />
      </a-form-item>
      <a-form-item label="旅游线路" field="line_ids" :rules="requiredRule">
        <dict-select
          v-model="formModel.line_ids"
          :data-list="dataStore.lineList"
          label-key="line_name"
          value-key="line_id"
          placeholder="请选择"
        />
      </a-form-item>
      <a-divider>分配规则</a-divider>
      <a-form-item
        label="分配给"
        field="distribute_department_ids"
        :rules="requiredRuleArr"
      >
        <request-tree-select
          v-model="formModel.distribute_department_ids"
          request-url="/api/department/list"
          label-key="department_name"
          child-key="child"
          :tree-check-strictly="true"
          tree-checked-strategy="all"
          multiple
          @change="handleDistributeDepartmentChange"
        />
      </a-form-item>

      <!-- 部门分配上限设置 -->
      <a-form-item
        label="单日分配上限"
        field="department_limit_upper"
        :rules="departmentLimitRules"
      >
        <department-limit-setting
          ref="departmentLimitRef"
          v-model="formModel.department_limit_upper"
          :department-ids="formModel.distribute_department_ids"
          @change="handleDepartmentLimitChange"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { requiredRule, requiredRuleArr } from '@/utils/util';
  import RequestTreeSelect from '@/components/select/request-tree-select.vue';
  import { useDataCacheStore } from '@/store';
  import { orderTypeListM } from '@/components/dict-select/dict-travel';
  import { clueTypeListM } from '@/components/dict-select/dict-clue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import DepartmentLimitSetting from './department-limit-setting.vue';

  const dataStore = useDataCacheStore();
  const defaultForm = () => ({
    rule_name: '',
    type: 'thread',
    type_value: '',
    source_department_ids: [],
    line_ids: [],
    distribute_department_ids: [],
    department_limit_upper: {},
  });

  const props = defineProps({
    sendParams: {
      type: Object,
      default: () => ({}),
    },
  });

  const emit = defineEmits(['save', 'refresh']);

  const tableType = [
    {
      label: '线索',
      value: 'thread',
    },
    {
      label: '订单',
      value: 'order',
    },
  ];

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const formModel = ref(defaultForm());
  const departmentLimitRef = ref();

  // 部门分配上限验证规则
  const departmentLimitRules = [
    {
      required: true,
      validator: (
        value: Record<string, number>,
        callback: (error?: string) => void
      ) => {
        // 检查是否选择了分配部门
        if (!formModel.value.distribute_department_ids.length) {
          callback('请先选择分配部门');
          return;
        }

        // 检查是否为每个选中的部门设置了上限
        const selectedDepartmentIds = formModel.value.distribute_department_ids;
        const hasEmptyLimit = selectedDepartmentIds.some((id) => {
          const key = String(id);
          const limitValue = value[key];
          return (
            limitValue === undefined || limitValue === null || limitValue === ''
          );
        });

        if (hasEmptyLimit) {
          callback('请为所有选中的部门设置单日分配上限');
          return;
        }

        // 检查所有上限值是否为有效的非负整数
        const hasInvalidValue = Object.values(value).some((limit) => {
          return !Number.isInteger(limit) || limit < 0;
        });

        if (hasInvalidValue) {
          callback('请输入大于等于0的整数');
          return;
        }

        callback();
      },
      trigger: ['change', 'blur'],
    },
  ];

  const validate = () => {
    return formRef.value?.validate();
  };
  const clearValidate = () => {
    formRef.value?.clearValidate();
    departmentLimitRef.value?.clearValidation();
  };

  const show = (dinfo: any) => {
    let initForm = defaultForm();
    formModel.value = initForm;
    if (dinfo) {
      Object.assign(formModel.value, dinfo);
    }

    clearValidate();
    visible.value = true;
    loading.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  // 处理分配部门变化
  const handleDistributeDepartmentChange = (departmentIds: number[]) => {
    // 当分配部门发生变化时，清理不再存在的部门的上限设置
    const currentLimits = formModel.value.department_limit_upper || {};
    const newLimits: Record<string, number> = {};

    // 只保留仍然被选中的部门的上限设置
    departmentIds.forEach((id) => {
      const key = String(id);
      if (currentLimits[key]) {
        newLimits[key] = currentLimits[key];
      }
    });

    formModel.value.department_limit_upper = newLimits;

    // 触发表单验证
    formRef.value?.validateField('department_limit_upper');
  };

  // 处理部门分配上限变化
  const handleDepartmentLimitChange = (value: Record<string, number>) => {
    formModel.value.department_limit_upper = value;

    // 触发表单验证
    formRef.value?.validateField('department_limit_upper');
  };

  const handleBeforeOk = async () => {
    // 先验证表单基本字段
    const formValidationResult = await validate();

    // 再验证部门分配上限
    const departmentLimitValid =
      departmentLimitRef.value?.validateAll() ?? true;

    if (!formValidationResult && departmentLimitValid) {
      loading.value = true;
      request('/api/thread/distributeFunnelSaveOrEdit', {
        ...formModel.value,
        ...props.sendParams,
      })
        .then((resData: any) => {
          Message.success('保存成功');
          emit('refresh', formModel.value.id);
          handleCancel();
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      // 如果验证失败，显示错误提示
      if (formValidationResult) {
        Message.error('请完善表单信息');
      }
      if (!departmentLimitValid) {
        Message.error('请完善部门分配上限设置');
      }
    }
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>
