<template>
  <div class="clue-distribute-statistics">
    <!-- 筛选区域 -->
    <a-card class="filter-card" :bordered="false">
      <filter-form
        v-model:distribute-rule-id="filterParams.id"
        v-model:time-range="filterParams.times"
        :loading="showLoading"
        @search="handleSearch"
      />

      <!-- 自动刷新控制 -->
      <!-- <div class="auto-refresh-control">
        <a-switch
          v-model="autoRefreshEnabled"
          size="small"
          @change="toggleAutoRefresh"
        />
        <span class="refresh-label"
          >自动刷新 ({{ REFRESH_INTERVAL_MS / 1000 }}秒)</span
        >
        <a-tag
          v-if="userInteracting && autoRefreshEnabled"
          color="orange"
          size="small"
          class="refresh-indicator"
        >
          已暂停 (用户操作中)
        </a-tag>
      </div> -->
    </a-card>

    <!-- 统计卡片区域 -->
    <statistics-cards
      :total-thread="statisticsData.total"
      :already-distribute="statisticsData.already_distribute"
      :waiting-distribute="statisticsData.waiting_distribute"
      :yesterday-info="yesterdayInfo"
      :loading="showLoading"
      :time-range="filterParams.times"
    />

    <!-- 图表和表格区域 -->
    <a-row :gutter="16" class="chart-section">
      <!-- 分配漏斗图 -->
      <a-col :span="24">
        <distribute-funnel
          :statistics-data="statisticsData"
          :loading="showLoading"
        />
      </a-col>

      <!-- 部门统计表格 -->
      <a-col :span="24">
        <department-table
          :department-data="statisticsData.departmentData"
          :current-rule-id="filterParams.id"
          :loading="showLoading"
          :is-single-day="isSingleDay"
          @refresh="handleSearch"
        />
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { IconLoading } from '@arco-design/web-vue/es/icon';
  import request from '@/api/request';
  import dayjs from 'dayjs';
  import FilterForm from './components/filter-form.vue';
  import StatisticsCards from './components/statistics-cards.vue';
  import DistributeFunnel from './components/distribute-funnel.vue';
  import DepartmentTable from './components/department-table.vue';

  // 接口数据类型定义
  interface DepartmentData {
    department_name: string;
    department_limit_upper: string;
    department_already_distribute: number;
    department_waiting_distribute: number;
  }

  interface StatisticsData {
    total: number;
    already_distribute: number;
    waiting_distribute: number;
    departmentData: DepartmentData[];
  }

  // 筛选参数
  const filterParams = reactive({
    id: null, // 分配规则ID
    times: [
      dayjs().format('YYYY-MM-DD'), // 今天
      dayjs().format('YYYY-MM-DD'), // 今天
    ] as string[], // 时间范围（默认为今天）
  });

  // 统计数据
  const statisticsData = ref<StatisticsData>({
    total: 0,
    already_distribute: 0,
    waiting_distribute: 0,
    departmentData: [],
  });

  // 加载状态
  const loading = ref(false);
  const silentRefreshing = ref(false); // 静默刷新状态标识

  // 计算属性：是否显示loading状态（静默刷新时不显示）
  const showLoading = computed(() => loading.value && !silentRefreshing.value);

  // 计算属性：判断是否为单天时间段
  const isSingleDay = computed(() => {
    if (!filterParams.times || filterParams.times.length !== 2) {
      return true; // 默认为单天
    }

    const startDate = filterParams.times[0];
    const endDate = filterParams.times[1];

    // 如果开始日期和结束日期相同，则为单天
    return startDate === endDate;
  });

  // 自动刷新相关状态
  const autoRefreshEnabled = ref(true); // 自动刷新开关
  const refreshInterval = ref<number | null>(null); // 定时器引用
  const REFRESH_INTERVAL_MS = 5000; // 5秒刷新间隔
  const userInteracting = ref(false); // 用户交互状态，用于暂停自动刷新

  // 重置统计数据
  const resetStatisticsData = () => {
    statisticsData.value = {
      total: 0,
      already_distribute: 0,
      waiting_distribute: 0,
      departmentData: [],
    };
  };

  const yesterdayInfo = ref({
    yesterday_total_thread: 0,
    yesterday_already_distribute: 0,
    yesterday_waiting_distribute: 0,
  });

  // 获取统计数据
  const fetchStatisticsData = async (isSilent = false) => {
    if (!filterParams.id || !filterParams.times.length) {
      // Message.warning('请选择分配规则和时间范围');
      return;
    }

    // 根据是否静默刷新设置不同的loading状态
    if (isSilent) {
      silentRefreshing.value = true;
      // 静默刷新时不设置 loading.value，避免显示加载状态
    } else {
      loading.value = true;
      silentRefreshing.value = false; // 确保手动刷新时清除静默状态
    }

    try {
      const response = await request('/api/thread/distributeFunnelStatistics', {
        id: filterParams.id,
        times: filterParams.times,
      });
      if (response.code === 0) {
        // 确保数据结构完整
        statisticsData.value = {
          total: response.data?.total || 0,
          already_distribute: response.data?.already_distribute || 0,
          waiting_distribute: response.data?.waiting_distribute || 0,
          departmentData: response.data?.departmentData || [],
        };
        yesterdayInfo.value = {
          yesterday_total_thread: response.data?.yesterday_total_thread || 0,
          yesterday_already_distribute:
            response.data?.yesterday_already_distribute || 0,
          yesterday_waiting_distribute:
            response.data?.yesterday_waiting_distribute || 0,
        };

        if (isSilent) {
          console.log('静默刷新数据成功:', statisticsData.value);
        } else {
          console.log('获取统计数据成功:', statisticsData.value);
        }
      } else {
        // 静默刷新时不显示错误消息，避免干扰用户
        if (!isSilent) {
          Message.error(response.msg || '获取数据失败');
        } else {
          console.warn('静默刷新失败:', response.msg);
        }
        // 静默刷新失败时不重置数据，保持当前数据显示
        if (!isSilent) {
          resetStatisticsData();
        }
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
      // 静默刷新时不显示错误消息，避免干扰用户
      if (!isSilent) {
        Message.error('获取数据失败，请稍后重试');
        resetStatisticsData();
      } else {
        console.warn('静默刷新网络错误:', error);
        // 静默刷新失败时保持当前数据
      }
    } finally {
      if (isSilent) {
        silentRefreshing.value = false;
      } else {
        loading.value = false;
      }
    }
  };

  // 启动自动刷新定时器
  const startAutoRefresh = () => {
    if (
      !autoRefreshEnabled.value ||
      refreshInterval.value ||
      !filterParams.id
    ) {
      return; // 如果已经启动或者自动刷新被禁用，则不重复启动
    }

    refreshInterval.value = window.setInterval(() => {
      // 只有在有有效的筛选条件时才自动刷新，且不在手动刷新或静默刷新进行中，且用户没有在交互
      if (
        filterParams.id &&
        filterParams.times.length &&
        !loading.value &&
        !silentRefreshing.value &&
        !userInteracting.value
      ) {
        fetchStatisticsData(true); // 传入true表示静默刷新
      }
    }, REFRESH_INTERVAL_MS);

    console.log(
      '自动刷新定时器已启动，间隔:',
      REFRESH_INTERVAL_MS / 1000,
      '秒'
    );
  };

  // 停止自动刷新定时器
  const stopAutoRefresh = () => {
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value);
      refreshInterval.value = null;
      console.log('自动刷新定时器已停止');
    }
  };

  // 切换自动刷新状态
  const toggleAutoRefresh = () => {
    // autoRefreshEnabled.value = !autoRefreshEnabled.value;
    if (autoRefreshEnabled.value) {
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }
  };

  // 暂停自动刷新（用户交互时）
  const pauseAutoRefresh = () => {
    userInteracting.value = true;
  };

  // 恢复自动刷新（用户交互结束时）
  const resumeAutoRefresh = () => {
    userInteracting.value = false;
  };

  // 处理搜索
  const handleSearch = () => {
    fetchStatisticsData(false); // 手动搜索显示loading
    // 手动搜索后重启自动刷新定时器
    if (autoRefreshEnabled.value) {
      stopAutoRefresh();
      startAutoRefresh();
    }
  };

  // 处理筛选条件重置
  const handleReset = () => {
    // 重置筛选参数
    filterParams.id = 1;

    // 设置默认时间范围为今天
    const today = dayjs().format('YYYY-MM-DD');
    filterParams.times = [today, today];

    // 重新获取数据
    fetchStatisticsData(false); // 手动重置显示loading
  };

  // 用户活动检测
  let userActivityTimer: number | null = null;
  const USER_ACTIVITY_TIMEOUT = 3000; // 3秒无活动后恢复自动刷新

  const handleUserActivity = () => {
    pauseAutoRefresh();

    // 清除之前的定时器
    if (userActivityTimer) {
      clearTimeout(userActivityTimer);
    }

    // 设置新的定时器，在用户停止活动后恢复自动刷新
    userActivityTimer = window.setTimeout(() => {
      resumeAutoRefresh();
    }, USER_ACTIVITY_TIMEOUT);
  };

  // 页面初始化
  onMounted(() => {
    // 设置默认时间范围为今天
    const today = dayjs().format('YYYY-MM-DD');
    filterParams.times = [today, today];

    // 初始加载数据
    fetchStatisticsData(false); // 初始加载显示loading

    // 启动自动刷新定时器
    if (autoRefreshEnabled.value) {
      startAutoRefresh();
    }

    // 添加用户活动监听器
    document.addEventListener('mousedown', handleUserActivity);
    document.addEventListener('keydown', handleUserActivity);
    document.addEventListener('scroll', handleUserActivity);
    document.addEventListener('touchstart', handleUserActivity);
  });

  // 组件卸载时清理定时器
  onUnmounted(() => {
    stopAutoRefresh();

    // 清理用户活动定时器
    if (userActivityTimer) {
      clearTimeout(userActivityTimer);
    }

    // 移除用户活动监听器
    document.removeEventListener('mousedown', handleUserActivity);
    document.removeEventListener('keydown', handleUserActivity);
    document.removeEventListener('scroll', handleUserActivity);
    document.removeEventListener('touchstart', handleUserActivity);
  });
</script>

<style scoped lang="less">
  .clue-distribute-statistics {
    // padding: 16px;
    // background-color: var(--color-bg-1);
    min-height: calc(100vh - 60px);
    margin-top: 10px;

    .page-header {
      margin-bottom: 16px;

      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: var(--color-text-1);
        line-height: 1.4;
      }
    }

    .filter-card {
      margin-bottom: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      border-radius: 8px;

      :deep(.arco-card-body) {
        padding: 16px 20px;
      }

      .auto-refresh-control {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid var(--color-border-2);

        .refresh-label {
          font-size: 14px;
          color: var(--color-text-2);
        }

        .refresh-indicator {
          margin-left: 8px;

          .refresh-icon {
            animation: spin 1s linear infinite;
          }
        }
      }

      @keyframes spin {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }
    }

    .chart-section {
      margin-top: 16px;

      .arco-col {
        margin-bottom: 16px;
      }
    }
  }

  // 响应式适配
  @media (max-width: 1200px) {
    .clue-distribute-statistics {
      .chart-section {
        :deep(.arco-col) {
          flex: 0 0 100% !important;
          max-width: 100% !important;
          margin-bottom: 16px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .clue-distribute-statistics {
      padding: 12px;

      .page-header {
        margin-bottom: 12px;

        h2 {
          font-size: 18px;
        }
      }

      .filter-card {
        margin-bottom: 12px;

        :deep(.arco-card-body) {
          padding: 12px 16px;
        }
      }

      .chart-section {
        margin-top: 12px;

        .arco-col {
          margin-bottom: 12px;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .clue-distribute-statistics {
      padding: 8px;

      .page-header {
        margin-bottom: 8px;

        h2 {
          font-size: 16px;
        }
      }

      .filter-card {
        margin-bottom: 8px;

        :deep(.arco-card-body) {
          padding: 8px 12px;
        }
      }

      .chart-section {
        margin-top: 8px;

        .arco-col {
          margin-bottom: 8px;
        }
      }
    }
  }
</style>
