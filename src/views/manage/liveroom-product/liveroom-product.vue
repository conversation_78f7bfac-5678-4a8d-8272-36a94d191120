<template>
  <div class="df">
    <a-card size="small" class="table-card">
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="handleSubmit()"
      >
        <template #formItemGroup>
          <a-form-item label="旅游线路">
            <dict-select
              v-model="formModel.line_id"
              :data-list="dataStore.lineList"
              value-key="line_id"
              label-key="line_name"
            />
          </a-form-item>
          <a-form-item label="商品名称">
            <a-input v-model="formModel.live_product_name" allow-clear />
          </a-form-item>
        </template>
      </search-form-fold>

      <div class="table-card-header mt-10">
        <div> </div>
        <a-space>
          <a-button type="primary" @click="showEditFn()">
            <template #icon>
              <icon-plus />
            </template>
            新增
          </a-button>
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formModel"
        :scroll-percent="{ x: 1000, maxHeight: '70vh' }"
      >
        <template #action="{ record }: TableColumnSlot">
          <a-space>
            <a-link @click="showEditFn(record)"> <icon-edit />编辑 </a-link>
          </a-space>
        </template>
      </base-table>
    </a-card>
    <liveroom-product-save ref="editRef" @save="handleSubmit()" />
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import { stateM } from '@/components/dict-select/dict-common';
  import LiveroomProductSave from '@/views/manage/liveroom-product/liveroom-product-save.vue';
  import { useDataCacheStore } from '@/store';

  const dataStore = useDataCacheStore();
  const generateFormModel = () => {
    return {
      line_id: '',
      live_product_name: '',
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    return request('/api/product/productPackageList', {
      ...data,
    });
  };

  const editRef = ref();
  function showEditFn(record?: any) {
    editRef.value?.show(record);
  }

  const columns = [
    {
      title: '直播间商品ID',
      dataIndex: 'live_product_id',
    },
    {
      title: '直播间',
      dataIndex: 'live_name',
    },
    {
      title: '商品名称',
      dataIndex: 'live_product_name',
    },
    {
      title: '旅游线路',
      dataIndex: 'line_name',
    },
    {
      title: '产品',
      dataIndex: 'product_name',
    },
    {
      title: '媒体平台',
      dataIndex: 'platform',
    },
    {
      title: '成人数',
      dataIndex: 'adult_num',
    },
    {
      title: '儿童数',
      dataIndex: 'children_num',
    },
    {
      title: '床位数',
      dataIndex: 'bed_num',
    },
    {
      title: '结算价',
      dataIndex: 'total_final_price',
    },
    {
      title: '总团款',
      dataIndex: 'total_cut_price',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less"></style>
