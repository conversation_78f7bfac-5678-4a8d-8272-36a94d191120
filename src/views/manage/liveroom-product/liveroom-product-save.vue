<template>
  <d-modal
    v-model:visible="visible"
    width="500px"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    :mask-closable="false"
    :esc-to-close="false"
    :title="`${formModel.id ? '编辑' : '添加'}直播间套餐`"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formModel" :auto-label-width="true">
      <a-form-item
        label="直播间商品ID"
        field="live_product_id"
        :rules="requiredRule"
      >
        <a-input v-model="formModel.live_product_id" allow-clear />
      </a-form-item>
      <a-form-item
        label="商品名称"
        field="live_product_name"
        :rules="requiredRule"
      >
        <a-input v-model="formModel.live_product_name" allow-clear />
      </a-form-item>
      <a-form-item label="旅游线路" field="line_id" :rules="requiredRule">
        <dict-select
          v-model="formModel.line_id"
          :data-list="dataStore.lineList"
          value-key="line_id"
          label-key="line_name"
        />
      </a-form-item>
      <a-form-item label="产品" field="line_id" :rules="requiredRule">
        <dict-select
          v-model="formModel.product_id"
          :data-list="productList"
          value-key="product_id"
          label-key="product_name"
        />
      </a-form-item>
      <a-form-item label="媒体平台" field="platform" :rules="requiredRule">
        <dict-select
          v-model="formModel.platform"
          :data-list="contactWayListM"
        />
      </a-form-item>
      <a-form-item label="直播间" field="live_room_id" :rules="requiredRule">
        <request-select v-model="formModel.live_room_id" api="live_room" />
      </a-form-item>
      <a-form-item label="成人数" field="adult_num" :rules="requiredRule">
        <a-input-number v-model="formModel.adult_num" :min="0" :precision="0" />
      </a-form-item>
      <a-form-item label="儿童数" field="children_num" :rules="requiredRule">
        <a-input-number
          v-model="formModel.children_num"
          :min="0"
          :precision="0"
        />
      </a-form-item>
      <a-form-item label="床位数" field="bed_num" :rules="requiredRule">
        <a-input-number v-model="formModel.bed_num" :min="0" :precision="0" />
      </a-form-item>
      <a-form-item
        label="结算价"
        field="total_final_price"
        :rules="requiredRule"
      >
        <a-input-number
          v-model="formModel.total_final_price"
          :min="0"
          :precision="2"
        />
      </a-form-item>
      <a-form-item label="总团款" field="total_cut_price" :rules="requiredRule">
        <a-input-number
          v-model="formModel.total_cut_price"
          :min="0"
          :precision="2"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { requiredRule, requiredRuleArr } from '@/utils/util';
  import { useDataCacheStore } from '@/store';
  import { contactWayListM } from '@/components/dict-select/dict-travel';
  import RequestSelect from '@/components/select/request-select.vue';

  const dataStore = useDataCacheStore();
  const defaultForm = () => ({
    id: null,
    live_product_id: '',
    live_product_name: '',
    line_id: null,
    product_id: null,
    platform: '',
    live_room_id: null,
    adult_num: null,
    children_num: null,
    bed_num: null,
    total_final_price: null,
    total_cut_price: null,
  });

  const emit = defineEmits(['save']);

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const formModel = ref(defaultForm());

  const productList = computed(() => {
    const { line_id } = formModel.value;
    if (!line_id) {
      return [];
    }
    return dataStore.getProductList(line_id);
  });
  const validate = () => {
    return formRef.value?.validate();
  };
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  const show = (dinfo: any) => {
    let initForm = defaultForm();
    formModel.value = initForm;
    if (dinfo) {
      Object.keys(initForm).forEach((key) => {
        if (
          dinfo[key] &&
          ['total_cut_price', 'total_final_price'].includes(key)
        ) {
          // @ts-ignore
          formModel.value[key] = parseFloat(dinfo[key]);
        } else {
          // @ts-ignore
          formModel.value[key] =
            dinfo[key] || initForm[key as keyof typeof initForm];
        }
      });
    }

    clearValidate();
    visible.value = true;
    loading.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  const handleBeforeOk = async () => {
    const result = await validate();
    if (!result) {
      loading.value = true;
      request('/api/product/productPackageSave', {
        ...formModel.value,
      })
        .then(() => {
          Message.success('保存成功');
          handleCancel();
          emit('save');
        })
        .finally(() => {
          loading.value = false;
        });
    }
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>
