<template>
  <div>
    <!--<a-card>
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="handleSubmit()"
      >
        <template #formItemGroup>
          <a-form-item label="添加人"> </a-form-item>
        </template>
      </search-form-fold>
    </a-card>-->
    <a-card size="small" class="table-card">
      <div class="table-card-header">
        <div> </div>
        <a-space>
          <a-button type="primary" @click="showEditFn()">
            <template #icon>
              <icon-plus />
            </template>
            新增
          </a-button>
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formParams"
        :scroll-percent="{ x: 1000, maxHeight: '70vh' }"
      >
        <template #action="{ record }: TableColumnSlot">
          <a-space>
            <a-link @click="showEditFn(record)"> 编辑 </a-link>
          </a-space>
        </template>
      </base-table>
    </a-card>
    <receive-payment-edit ref="editRef" @save="handleSubmit()" />
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive, computed } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import ReceivePaymentEdit from '@/views/manage/receive-payment/receive-payment-edit.vue';

  const generateFormModel = () => {
    return {
      id: '',
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const formParams = computed(() => {
    return {
      ...formModel,
    };
  });

  const getList = async (data: any) => {
    return request('/api/getPayees', {
      ...data,
    });
  };

  const editRef = ref();
  function showEditFn(record?: any) {
    editRef.value?.show(record);
  }

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
    },
    {
      title: '名称',
      dataIndex: 'payee_name',
    },
    // {
    //  title: '添加人',
    //  dataIndex: 'user_name',
    // },
    {
      title: '添加时间',
      dataIndex: 'created_at',
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 180,
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less"></style>
