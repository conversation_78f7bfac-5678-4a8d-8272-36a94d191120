<template>
  <d-modal
    v-model:visible="visible"
    width="500px"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    :mask-closable="false"
    :esc-to-close="false"
    :title="`${formModel.id ? '编辑' : '添加'}内容号`"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formModel" :auto-label-width="true">
      <a-form-item label="平台" field="platform" :rules="requiredRule">
        <dict-select
          v-model="formModel.platform"
          :data-list="contactWayListM"
        />
      </a-form-item>
      <a-form-item
        label="内容号名称"
        field="account_name"
        :rules="requiredRule"
      >
        <a-input v-model="formModel.account_name" />
      </a-form-item>
      <a-form-item label="内容号ID" field="account_id" :rules="requiredRule">
        <a-input v-model="formModel.account_id" />
      </a-form-item>
      <a-form-item
        label="旅游线路"
        field="product_line_id"
        :rules="requiredRule"
      >
        <dict-select
          v-model="formModel.product_line_id"
          :data-list="dataStore.lineList"
          label-key="line_name"
          value-key="line_id"
          placeholder="请选择"
        />
      </a-form-item>
      <a-form-item label="运营" field="operate_user_id" :rules="requiredRule">
        <request-select
          v-model="formModel.operate_user_id"
          request-url="/api/user/userList"
          label-key="user_name"
          :send-params="{ pageSize: 999999 }"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { requiredRule } from '@/utils/util';
  import { contactWayListM } from '@/components/dict-select/dict-travel';
  import { useDataCacheStore } from '@/store';
  import RequestSelect from '@/components/select/request-select.vue';

  const dataStore = useDataCacheStore();
  const defaultForm = () => ({
    id: null,
    platform: contactWayListM[0].value,
    account_name: '',
    account_id: '',
    product_line_id: '',
    operate_user_id: '',
    state: 1,
  });

  const emit = defineEmits(['save']);

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const formModel = ref(defaultForm());
  const validate = () => {
    return formRef.value?.validate();
  };
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  const show = (dinfo: any) => {
    let initForm = defaultForm();
    formModel.value = initForm;
    if (dinfo) {
      Object.keys(initForm).forEach((key) => {
        // @ts-ignore
        formModel.value[key] =
          dinfo[key] || initForm[key as keyof typeof initForm];
      });
    }

    clearValidate();
    visible.value = true;
    loading.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  const handleBeforeOk = async () => {
    const result = await validate();
    if (!result) {
      loading.value = true;
      request('/api/contentAccount/save', {
        ...formModel.value,
      })
        .then(() => {
          Message.success('保存成功');
          handleCancel();
          emit('save');
        })
        .finally(() => {
          loading.value = false;
        });
    }
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>
