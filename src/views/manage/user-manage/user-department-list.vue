<template>
  <div class="layout-left-side-content">
    <a-spin
      :loading="loading"
      style="min-height: 200px; width: 100%"
      tip="加载中..."
    >
      <a-link
        class="department-title"
        :class="{ active: isAll }"
        @click="allClickHandler"
        ><template #icon> <icon-user /> </template> 全部
      </a-link>
      <a-tree
        v-model:selected-keys="selectedKeys"
        :data="treeData"
        :show-line="true"
        :field-names="{
          key: 'id',
          title: 'department_name',
          children: 'child',
        }"
        @select="onSelect"
      />
    </a-spin>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import request from '@/api/request';

  const props = defineProps({
    departmentId: {
      type: [String, Number],
      default: () => '',
    },
  });
  const emit = defineEmits(['change', 'update:departmentId']);
  const treeData = ref([]);
  const selectedKeys = ref([]);
  const loading = ref(true);
  const getDept = () => {
    loading.value = false;
    request('/api/department/list', {})
      .then((resData) => {
        treeData.value = resData.data;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const isAll = ref(true);
  const onSelect = async (val: any = []) => {
    // console.log(val);
    // console.log(selectedKeys.value);
    isAll.value = val.length === 0;
    emit('update:departmentId', val.length === 0 ? '' : val.slice(-1)[0]);
    emit('change', val.slice(-1)[0]);
  };
  const allClickHandler = () => {
    selectedKeys.value = [];
    onSelect();
  };

  onMounted(() => {
    getDept();
  });
</script>

<style lang="less" scoped>
  .layout-left-side-content {
    //padding: 20px;
  }
  .department-title {
    padding: 5px;
    color: var(--color-text-1);
    &.active {
      color: rgb(var(--primary-6));
    }
  }
</style>
