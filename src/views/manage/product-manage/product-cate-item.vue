<template>
  <a-space wrap>
    <a-input-search
      v-if="showSearch"
      v-model="keyword"
      size="small"
      placeholder="请输入关键词"
    />
    <template v-for="item in showList" :key="item[valueKey]">
      <a-input-group v-if="item.iptVisible">
        <a-input
          v-model="item.name_new"
          placeholder="请输入名称"
          allow-clear
          @press-enter="updateData(item)"
        />
        <a-button
          type="primary"
          :loading="!!item.loading"
          @click="updateData(item)"
        >
          <template #icon>
            <icon-check />
          </template>
        </a-button>
        <a-button
          type="outline"
          @click="
            item.name_new = item[labelKey];
            item.iptVisible = false;
          "
        >
          <template #icon>
            <icon-close />
          </template>
        </a-button>
      </a-input-group>
      <a-tag
        v-else
        :color="modelValue === item[valueKey] ? 'arcoblue' : undefined"
        style="cursor: pointer"
        @click="updateVal(item)"
      >
        <a-space>
          <span>{{ item[labelKey] }}</span>
          <!--计调角色权限-->
          <icon-edit
            v-if="userStore.hasPermission(14)"
            class="edit-icon"
            @click.stop="
              item.name_new = item[labelKey];
              item.iptVisible = true;
            "
          />
        </a-space>
      </a-tag>
    </template>
    <template v-if="userStore.hasPermission(14)">
      <a-tag
        v-if="!iptVisible"
        color="arcoblue"
        @click="changeIptVisible(true)"
      >
        <icon-plus />
        新增
      </a-tag>
      <a-input-group v-else>
        <a-input
          v-model="iptVal"
          placeholder="请输入名称"
          allow-clear
          @press-enter="addData()"
        />
        <a-button type="primary" :loading="loading" @click="addData()">
          <template #icon>
            <icon-check />
          </template>
        </a-button>
        <a-button type="outline" @click="changeIptVisible(false)">
          <template #icon>
            <icon-close />
          </template>
        </a-button>
      </a-input-group>
    </template>
  </a-space>
</template>

<script setup lang="ts">
  import { computed, reactive, ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import request from '@/api/request';
  import { useDataCacheStore, useUserStore } from '@/store';

  const userStore = useUserStore();

  const props = defineProps({
    modelValue: {
      type: [Number, String],
      default: null,
    },
    info: {
      type: Object,
      default: () => {
        return {};
      },
    },
    type: {
      type: String,
      default: '',
    },
    showSearch: {
      type: Boolean,
      default: false,
    },
  });
  const iptVisible = ref(false);
  const iptVal = ref('');
  const loading = ref(false);
  const keyword = ref('');
  const emit = defineEmits(['update:modelValue', 'change']);
  const valueKey = computed(() => `${props.type}_id`);
  const labelKey = computed(() => `${props.type}_name`);
  const dataStore = useDataCacheStore();

  const showList = computed(
    () =>
      (keyword.value
        ? props.info.child?.filter((item: any) =>
            item[labelKey.value]?.includes(keyword.value)
          )
        : props.info.child) || []
  );

  function updateVal(item: any) {
    emit('update:modelValue', item[valueKey.value]);
    emit('change', valueKey.value, item[valueKey.value], item);
  }

  function changeIptVisible(val: boolean) {
    iptVal.value = '';
    iptVisible.value = val;
  }

  function addData() {
    if (!iptVal.value) {
      return Message.error('请输入名称');
    }
    loading.value = true;
    request('/api/product/productSave', {
      ...props.info,
      child: undefined,
      [labelKey.value]: iptVal.value,
      type: props.type,
    })
      .then(async (res) => {
        changeIptVisible(false);
        Message.success('保存成功');
        await dataStore.getLineConfig();
        if (res.data?.[valueKey.value]) {
          updateVal(res.data);
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }
  function updateData(item: any) {
    if (!item.name_new) {
      return Message.error('请输入名称');
    }
    item.loading = true;
    request('/api/product/productSave', {
      ...item,
      [labelKey.value]: item.name_new,
      type: props.type,
    })
      .then(() => {
        item[labelKey.value] = item.name_new;
        Message.success('保存成功');
        dataStore.getLineConfig();
      })
      .finally(() => {
        item.loading = false;
      });
  }
</script>

<style scoped lang="less">
  :deep(.arco-form-item:last-child) {
    margin-bottom: 0;
  }
  .edit-icon {
    cursor: pointer;
    &:hover {
      color: rgb(var(--primary-6));
    }
  }
</style>
