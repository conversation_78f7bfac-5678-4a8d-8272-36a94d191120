<template>
  <div>
    <a-form :model="modelValue" auto-label-width>
      <a-form-item label="旅游线路">
        <product-cate-item
          :model-value="modelValue.line_id"
          :info="{ child: lineList }"
          type="line"
          @change="cateChange"
        />
      </a-form-item>
      <a-form-item v-if="curObj.line_id" label="地接">
        <product-cate-item
          :model-value="modelValue.agency_id"
          :info="curObj.line_id"
          type="agency"
          @change="cateChange"
        />
      </a-form-item>
      <a-form-item v-if="curObj.agency_id" label="团型">
        <product-cate-item
          :model-value="modelValue.tour_id"
          :info="curObj.agency_id"
          type="tour"
          @change="cateChange"
        />
      </a-form-item>
      <a-form-item v-if="curObj.tour_id" label="大小团">
        <product-cate-item
          :model-value="modelValue.size_id"
          :info="curObj.tour_id"
          type="size"
          @change="cateChange"
        />
      </a-form-item>
      <a-form-item v-if="curObj.size_id" label="产品">
        <product-cate-item
          :model-value="modelValue.product_id"
          :info="curObj.size_id"
          type="product"
          show-search
          @change="cateChange"
        />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { useDataCacheStore } from '@/store';
  import { computed, reactive, ref } from 'vue';
  import ProductCateItem from '@/views/manage/product-manage/product-cate-item.vue';

  const dataStore = useDataCacheStore();
  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  const emit = defineEmits(['update:modelValue', 'change']);
  const lineList = computed(() => dataStore.lineList);
  const curObj = computed(() => {
    let items: any = {};
    if (props.modelValue.line_id) {
      items.line_id = lineList.value.find(
        (item: any) => item.line_id === props.modelValue.line_id
      );
      if (props.modelValue.agency_id) {
        items.agency_id = items.line_id?.child?.find(
          (item: any) => item.agency_id === props.modelValue.agency_id
        );
        if (props.modelValue.tour_id) {
          items.tour_id = items.agency_id?.child?.find(
            (item: any) => item.tour_id === props.modelValue.tour_id
          );
          if (props.modelValue.size_id) {
            items.size_id = items.tour_id?.child?.find(
              (item: any) => item.size_id === props.modelValue.size_id
            );
            if (props.modelValue.product_id) {
              items.product_id = items.size_id?.child?.find(
                (item: any) => item.product_id === props.modelValue.product_id
              );
            }
          }
        }
      }
    }
    return items;
  });

  function updateVal(key: string, val: any) {
    Object.assign(props.modelValue, { [key]: val });
    emit('change', key, val);
  }

  function cateChange(key: string, val: number, item: any) {
    updateVal(key, val);
    switch (key) {
      case 'line_id':
        cateChange(
          'agency_id',
          item?.child?.[0]?.agency_id || '',
          item?.child?.[0]
        );
        break;
      case 'agency_id':
        cateChange(
          'tour_id',
          item?.child?.[0]?.tour_id || '',
          item?.child?.[0]
        );
        break;
      case 'tour_id':
        cateChange(
          'size_id',
          item?.child?.[0]?.size_id || '',
          item?.child?.[0]
        );
        break;
      case 'size_id':
        cateChange(
          'product_id',
          item?.child?.[0]?.product_id || '',
          item?.child?.[0]
        );
        break;
      default:
        break;
    }
  }
</script>

<style scoped lang="less">
  :deep(.arco-form-item:last-child) {
    margin-bottom: 0;
  }
</style>
