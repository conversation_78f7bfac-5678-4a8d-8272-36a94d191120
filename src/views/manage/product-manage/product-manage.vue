<template>
  <div>
    <a-card>
      <product-cate v-model="formModel" />
    </a-card>
    <a-card size="small" class="table-card">
      <div class="df jc-sb ai-cen mb-10">
        <a-space>
          <a-button type="primary" @click="getList">
            <icon-refresh />
            刷新
          </a-button>
        </a-space>
        <a-space>
          <a-button
            v-if="userStore.hasPermission(14)"
            type="primary"
            @click="uploadRef?.show(formModel)"
          >
            <icon-upload />
            上传文档
          </a-button>
        </a-space>
      </div>
      <!--table 区域-->
      <a-table
        :loading="loading"
        :columns="columns"
        :data="list"
        :scroll-percent="{ x: 1000, maxHeight: '70vh' }"
      >
        <template #action="{ record }: TableColumnSlot">
          <a-space>
            <a-link @click="viewFile(record)"> <icon-eye />查看 </a-link>
            <a-popconfirm
              v-if="userStore.hasPermission(14)"
              :content="`确认删除【${record.file_name}】吗？`"
              :ok-loading="!!record.loading"
              :position="'tr'"
              @ok="delAction(record)"
            >
              <a-link status="danger"> <icon-delete />删除 </a-link>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
    </a-card>
    <product-file-upload ref="uploadRef" @save="getList" />
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, watch } from 'vue';
  import ProductCate from '@/views/manage/product-manage/product-cate.vue';
  import { useDataCacheStore, useUserStore } from '@/store';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import ProductFileUpload from '@/views/manage/product-manage/product-file-upload.vue';

  const userStore = useUserStore();
  const dataStore = useDataCacheStore();
  const formModel = reactive({
    line_id: '',
    agency_id: '',
    tour_id: '',
    size_id: '',
    product_id: '',
  });

  const uploadRef = ref();
  const loading = ref(false);
  const columns = ref([
    { title: '文件', dataIndex: 'file_name', align: 'center' },
    { title: '上传人', dataIndex: 'create_user_name', align: 'center' },
    { title: '上传时间', dataIndex: 'created_at', align: 'center' },
    {
      title: '操作',
      dataIndex: 'action',
      slotName: 'action',
      width: 140,
      align: 'center',
    },
  ]);
  const list = ref([]);
  let reqToken: any;
  function getList() {
    reqToken?.abort('重复请求取消');
    reqToken = new AbortController();
    loading.value = true;
    request(
      '/api/product/productInformationList',
      {
        ...formModel,
      },
      reqToken.signal
    ).then((res) => {
      list.value = res.data || [];
      loading.value = false;
    });
  }

  function viewFile(record: any) {
    window.open(record.url, '__blank');
  }

  watch(
    () => dataStore.lineList.length,
    () => {
      if (dataStore.lineList.length) {
        formModel.line_id = dataStore.lineList[0]?.line_id || '';
        formModel.agency_id =
          dataStore.lineList[0]?.child?.[0]?.agency_id || '';
        formModel.tour_id =
          dataStore.lineList[0]?.child?.[0]?.child?.[0]?.tour_id || '';
        formModel.size_id =
          dataStore.lineList[0]?.child?.[0]?.child?.[0]?.child?.[0]?.size_id ||
          '';
        formModel.product_id =
          dataStore.lineList[0]?.child?.[0]?.child?.[0]?.child?.[0]
            ?.product_id || '';
        formModel.product_id =
          dataStore.lineList[0]?.child?.[0]?.child?.[0]?.child?.[0]?.child?.[0]
            ?.product_id || '';
      }
    },
    {
      immediate: true,
    }
  );
  watch(
    () => formModel.product_id,
    (val, oldValue) => {
      if (!val) {
        list.value = [];
      } else if (val !== oldValue) {
        getList();
      }
    },
    {
      immediate: true,
    }
  );

  function delAction(item: any) {
    item.loading = true;
    request('/api/product/productInformationDel', {
      id: item.id,
    }).then(() => {
      Message.success('删除成功');
      getList();
    });
  }
</script>

<style scoped lang="less"></style>
