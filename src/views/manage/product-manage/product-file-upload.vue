<template>
  <d-modal
    v-model:visible="visible"
    width="800px"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    :mask-closable="false"
    :esc-to-close="false"
    title="上传文档"
    @cancel="handleCancel"
  >
    <product-cate v-model="formModel" class="w100p" />
    <a-upload
      v-model:file-list="formModel.data"
      class="mt-10"
      draggable
      multiple
      action="/api/uploadFile"
      :data="uploadData"
      :headers="headers"
    >
    </a-upload>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { FileItem, Message } from '@arco-design/web-vue';
  import { cloneDeep } from 'lodash';
  import ProductCate from '@/views/manage/product-manage/product-cate.vue';
  import { getToken } from '@/utils/auth';
  import dayjs from 'dayjs';
  import { useUserStore } from '@/store';

  const headers = { Authorization: getToken() };
  const defaultForm = () => ({
    data: [],
    product_id: '',
  });

  const emit = defineEmits(['save']);

  const visible = ref(false);
  const loading = ref(false);
  const formModel = ref(defaultForm());

  const show = (dinfo: any) => {
    formModel.value = defaultForm();
    if (dinfo) {
      Object.assign(formModel.value, cloneDeep(dinfo));
    }
    visible.value = true;
    loading.value = false;
  };

  const userStore = useUserStore();
  function uploadData(fileItem: FileItem) {
    let fileType = fileItem.name?.split('.').pop() || '';
    return {
      type: 'travel_product_file',
      path: `travel/travel_product_file/uid${userStore.id}/${dayjs().format(
        'YYYYMMDD'
      )}/${fileItem.name?.replace(
        `.${fileType}`,
        ''
      )}-${Date.now()}.${fileType}`,
    };
  }

  const handleCancel = () => {
    visible.value = false;
  };

  function sendReq() {
    loading.value = true;
    request('/api/product/productInformationSave', {
      ...formModel.value,
      data: formModel.value.data.map((item: FileItem) => {
        return {
          file_name: item.name,
          url: item.response?.data?.url,
        };
      }),
    })
      .then(() => {
        Message.success('保存成功');
        handleCancel();
        emit('save');
      })
      .finally(() => {
        loading.value = false;
      });
  }
  const handleBeforeOk = async () => {
    if (!formModel.value.product_id) {
      return Message.error('请选择产品');
    }
    if (!formModel.value.data.length) {
      return Message.error('请上传文件');
    }
    if (
      formModel.value.data.some((item: FileItem) => item.status === 'uploading')
    ) {
      return Message.error('文件上传中，请等待上传完成');
    }
    sendReq();
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>
