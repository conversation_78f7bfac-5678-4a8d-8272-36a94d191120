<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form-fold
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="handleSubmit()"
      >
        <template #formItemGroup>
          <a-form-item label="添加时间">
            <a-range-picker v-model="formModel.date" allow-clear />
          </a-form-item>
          <a-form-item label="店铺">
            <request-select
              v-model="formModel.id"
              request-url="/api/store/map"
              label-key="store_name"
              :send-params="{ num }"
            />
          </a-form-item>
          <a-form-item label="平台">
            <dict-select
              v-model="formModel.platform"
              :data-list="contactWayListM"
            />
          </a-form-item>
          <a-form-item label="添加人">
            <request-select
              v-model="formModel.user_id"
              request-url="/api/user/userList"
              label-key="user_name"
              :send-params="{ pageSize: 999999 }"
            />
          </a-form-item>
        </template>
      </search-form-fold>

      <div class="table-card-header mt-10">
        <div> </div>
        <a-space>
          <a-button type="primary" @click="showEditFn()">
            <template #icon>
              <icon-plus />
            </template>
            新增
          </a-button>
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formParams"
        :scroll-percent="{ x: 1000, maxHeight: '70vh' }"
      >
        <template #action="{ record }: TableColumnSlot">
          <a-space>
            <a-link @click="showEditFn(record)"> 编辑 </a-link>
            <a-popconfirm
              :content="`此操作不可逆，确定要删除【${record.store_name}】吗?`"
              @ok="updateState(record, { state: -1 })"
            >
              <a-link :loading="record.loading" status="danger"> 删除 </a-link>
            </a-popconfirm>
          </a-space>
        </template>
        <template #state="{ record }: TableColumnSlot">
          <a-switch
            :loading="record.loading"
            :model-value="record.state === 1"
            @change="
              updateState(record, { state: record.state === 1 ? -1 : 1 })
            "
          />
        </template>
      </base-table>
    </a-card>
    <shop-edit
      ref="editRef"
      @save="
        num += 1;
        handleSubmit();
      "
    />
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive, computed } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/search-form-fold/search-form-fold.vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import { contactWayListM } from '@/components/dict-select/dict-travel';
  import ShopEdit from '@/views/manage/shop-manage/shop-edit.vue';

  const generateFormModel = () => {
    return {
      id: '',
      platform: '',
      user_id: '',
      date: [],
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const num = ref(1);
  const formModel = reactive(generateFormModel());

  const formParams = computed(() => {
    return {
      ...formModel,
      add_time_begin: formModel.date?.[0],
      add_time_end: formModel.date?.[1],
    };
  });

  const getList = async (data: any) => {
    return request('/api/store/list', {
      ...data,
    });
  };

  const editRef = ref();
  function showEditFn(record?: any) {
    editRef.value?.show(record);
  }

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
    },
    {
      title: '店铺名称',
      dataIndex: 'store_name',
    },
    {
      title: '店铺ID',
      dataIndex: 'store_id',
    },
    {
      title: '平台',
      dataIndex: 'platform',
    },
    {
      title: '添加人',
      dataIndex: 'user_name',
    },
    {
      title: '添加时间',
      dataIndex: 'add_time',
    },
    {
      title: '更新时间',
      dataIndex: 'update_time',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 180,
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function updateState(record: any, params: any) {
    record.loading = true;
    request('/api/store/save', {
      id: record.id,
      ...params,
    })
      .then(() => {
        handleSubmit();
      })
      .finally(() => {
        record.loading = false;
      });
  }

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less"></style>
