import { defineStore } from 'pinia';
import request from '@/api/request';
import { getToken } from '@/utils/auth';

const useDataCacheStore = defineStore('data-cache', {
  state: () => ({
    travelLines: [] as any[], // 旅游线路
    travelProducts: [] as any[], // 旅游产品
    travelAgencys: [] as any[], // 旅游地接
    travelCars: [] as any[], // 车型
    travelTypes: [] as any[], // 出游类型
    localGuide: [] as any[], // 地接和合同服务
    lineList: [] as any[], // 产品相关列表
  }),
  actions: {
    getTravelSetting() {
      request('/api/travel/getSetting', {}).then((res) => {
        this.travelCars = res.data.car_type;
        this.travelTypes = res.data.travel_type.map((item: any) => ({
          label: item,
          value: item,
        }));
        let travelLines: string[] = res.data.area;
        let travelAgencys: string[] = [];
        this.travelProducts = res.data.travel_line.map((line: string) => {
          let itemData = res.data.jingdian_setting[line];
          travelAgencys.push(itemData['地接']);
          return {
            ...itemData,
            label: line,
            area: itemData['线路'],
            value: line,
            travel_date:
              res.data.travel_line_date.find((item: any) => item.line === line)
                ?.date || [],
          };
        });
        this.travelLines = Array.from(new Set(travelLines)).map((item) => ({
          label: item,
          value: item,
        }));
        this.travelAgencys = Array.from(new Set(travelAgencys)).map((item) => ({
          label: item,
          value: item,
        }));
        this.localGuide = res.data.local_guide;
      });
    },
    async getLineConfig() {
      if (getToken()) {
        return request('/api/product/productList', {}).then((res) => {
          this.lineList = res.data;
        });
      }
      return Promise.resolve();
    },
    // 根据传参获取相关层级的数据
    getProductAboutItem(
      line_id?: number,
      agency_id?: number,
      tour_id?: number,
      size_id?: number
    ) {
      function getDeepItem(arr: any[]): any {
        let res = arr.find(
          (item) =>
            item.line_id === line_id &&
            (!agency_id || item.agency_id === agency_id) &&
            (!tour_id || item.tour_id === tour_id) &&
            (!size_id || item.size_id === size_id)
        );
        if (res) {
          return res;
        }
        arr?.some((citem: any) => {
          res = getDeepItem(citem.child || []);
          return res;
        });
        return res;
      }
      return getDeepItem(this.lineList);
    },
    getProductList(line_id?: number, agency_id?: number) {
      function getDeepItem(sum: any[], item: any) {
        if (item.child?.length) {
          item.child.forEach((child: any) => {
            getDeepItem(sum, child);
          });
        } else if (item.product_id) {
          sum.push(item);
        }
      }
      let arr: any[] = [];
      getDeepItem(arr, this.getProductAboutItem(line_id, agency_id) || {});

      return arr;
    },
    getAgencyList(line_id?: number) {
      function getDeepItem(sum: any[], item: any) {
        if (!item.agency_id && item.child?.length) {
          item.child.forEach((child: any) => {
            getDeepItem(sum, child);
          });
        } else if (item.agency_id && (!line_id || item.line_id === line_id)) {
          sum.push(item);
        }
      }
      let arr: any[] = [];
      this.lineList.forEach((item) => {
        getDeepItem(arr, item);
      });

      return arr;
    },
    getLineItem(key: string, val: number, nameKey: string) {
      function getDeepItem(arr: any[]): any {
        let res = arr.find((item) => item[nameKey] && item[key] === val);
        if (res) {
          return res;
        }
        arr?.some((citem: any) => {
          res = getDeepItem(citem.child || []);
          return res;
        });
        return res;
      }
      return getDeepItem(this.lineList);
    },
  },
});

export default useDataCacheStore;
