export type RoleType = '' | '*' | 'admin' | 'user';
export interface UserState {
  id?: number;
  create_user_id?: number;
  company_id?: number;
  account_name?: string;
  email?: string;
  user_name?: string;
  phone?: string;
  qy_wchat_id?: string | number;
  feishu_user_id?: string | number;
  feishu_open_id?: string | number;
  role_id: number;
  // role: number;
  roles: number[];
  department_id?: number;
  state?: number;
  group_id?: number;
  is_assistant?: number;
  add_time?: string;
  update_time?: string;
  entry_time?: string;
  leave_time?: string;
  avatar?: string;
  assistant_user_id?: number;
  supplier_type?: number;
  is_from_saas?: number;
  token?: string;
  host?: string;
  name?: string;
  // 是否是外部用户 (1: 外部用户, 0: 内部用户)
  out_user?: number;
  // 是否是三木且是电销角色 拥有更多权限
  is_three_wood_customer_service?: number;
  service_status?: string;
}
