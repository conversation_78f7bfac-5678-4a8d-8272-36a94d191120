import { defineStore } from 'pinia';
import {
  setToken,
  clearToken,
  setUser,
  clearUser,
  getUser,
  getToken,
} from '@/utils/auth';
import { removeRouteListener } from '@/utils/route-listener';
import request from '@/api/request';
import { isArray, isString, toNumber } from 'lodash';
import { useDataCacheStore } from '@/store';
import { UserState } from './types';
import useAppStore from '../app';

const useUserStore = defineStore('user', {
  state: (): UserState => ({
    id: undefined,
    create_user_id: undefined,
    company_id: undefined,
    user_name: undefined,
    email: undefined,
    account_name: undefined,
    phone: undefined,
    qy_wchat_id: undefined,
    feishu_user_id: undefined,
    feishu_open_id: undefined,
    role_id: undefined,
    // role: undefined,
    roles: undefined,
    department_id: undefined,
    state: undefined,
    group_id: undefined,
    is_assistant: undefined,
    add_time: undefined,
    update_time: undefined,
    entry_time: undefined,
    leave_time: undefined,
    avatar: undefined,
    assistant_user_id: undefined,
    supplier_type: undefined,
    is_from_saas: undefined,
    host: undefined,
    name: undefined,
    service_status: undefined,
    // 是否是外部用户
    out_user: undefined,
    // 是否是三木且是电销角色 拥有更多权限
    is_three_wood_customer_service: undefined,
    ...getUser(),
    token: getToken(),
  }),

  getters: {
    userInfo(store: any): UserState {
      return store.$state;
    },
  },

  actions: {
    hasPermission(role: number | string | number[]): boolean {
      if (this.roles?.includes(1)) return true;
      if (isArray(role)) {
        return role.includes(this.role_id);
      }
      if (isString(role)) {
        return this.roles?.includes(toNumber(role));
      }
      return this.roles?.includes(role);
    },
    hasRolePermission(role: number | string | number[]): boolean {
      if (isArray(role)) {
        return role.includes(this.role_id);
      }
      if (isString(role)) {
        return this.roles?.includes(toNumber(role));
      }
      return this.roles?.includes(role);
    },
    // 检查外部用户权限
    hasOutUserPermission(allowOutUser?: boolean): boolean {
      // 如果是外部用户
      if (this.out_user === 1) {
        return allowOutUser === true;
      }
      // 内部用户默认有权限
      return true;
    },
    // 综合权限检查（角色权限 + 外部用户权限）
    hasFullPermission(
      role: number | string | number[],
      allowOutUser?: boolean
    ): boolean {
      // 先检查基础角色权限
      const hasRole = this.hasPermission(role);
      if (!hasRole) {
        return false;
      }
      // 再检查外部用户权限
      return this.hasOutUserPermission(allowOutUser);
    },
    switchRoles(role: number) {
      return new Promise((resolve) => {
        this.role_id = role;
        resolve(this.role_id);
      });
    },
    // Set user's information
    setInfo(partial: Partial<UserState>) {
      this.$patch(partial);
      setUser(partial);
    },
    // Set user's information
    setStateInfo(partial: Partial<UserState>) {
      this.$patch(partial);
    },

    // Reset user's information
    resetInfo() {
      this.$reset();
    },

    async info() {},

    async getUserInfo() {
      if (getToken()) {
        request('/api/user/getSelfInfo').then((res: any) => {
          this.setInfo({ ...res.data });
        });
      }
    },

    async login(loginForm: any) {
      try {
        const res: any = await request('/api/userLogin', loginForm);
        setToken(res.data.token);
        this.setInfo({ ...res.data });
        const dataCache = useDataCacheStore();
        dataCache.getTravelSetting();
        dataCache.getLineConfig();
      } catch (err) {
        clearToken();
        throw err;
      }
    },
    async loginFs(loginForm: any) {
      try {
        const res: any = await request('/api/fsLogin', loginForm);
        setToken(res.data.token);
        this.setInfo({ ...res.data });
        const dataCache = useDataCacheStore();
        dataCache.getTravelSetting();
        dataCache.getLineConfig();
      } catch (err) {
        clearToken();
        throw err;
      }
    },
    logoutCallBack() {
      const appStore = useAppStore();
      this.resetInfo();
      clearToken();
      clearUser();
      removeRouteListener();
      appStore.clearServerMenu();
    },
    // Logout
    async logout() {
      this.logoutCallBack();
    },
  },
});

export default useUserStore;
