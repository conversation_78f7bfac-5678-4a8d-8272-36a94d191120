import axios, { AxiosPromise } from 'axios';
import type { AxiosResponse } from 'axios';
import { Message } from '@arco-design/web-vue';
import { getToken } from '@/utils/auth';
import { exportForm } from '@/utils/util';
import { useUserStore } from '@/store';
import router from '@/router';
import sparkMd5 from 'spark-md5';

// http method
export const METHOD = {
  GET: 'get',
  POST: 'post',
};

export interface HttpResponse<T = unknown> {
  msg: string;
  code: number;
  data: any | T;
}

const instance = axios.create({
  baseURL: '',
});

const reqObjectList: {
  [key: string]: Promise<AxiosResponse<any>> | undefined;
} = {};

const whiteList = ['/api/product/productList'];
/**
 * axios请求
 * @param url 请求地址
 * @param params 请求参数
 * @param signal {AbortSignal} 取消请求
 * @param method { "get" | "post" } http method
 * @param dconfig { object } 自定义配置
 * @returns {Promise<AxiosResponse<any>>}
 */
export default async function request(
  url: string,
  params?: any,
  signal?: AbortSignal,
  method = 'post',
  dconfig = {}
): Promise<any> {
  if (params?.export_now) {
    return exportForm(url, params) as any;
  }
  if (params?.export) {
    params.is_async_down = true;
  }
  const token = getToken();
  // 不是登录接口 则检验是否存在token
  if (!url.includes('ogin') && !token) {
    useUserStore().logout();
    return new Promise((resolve, reject) => {
      reject(new Error(`token失效-${url}-${token}`));
    });
  }
  const config: any = {
    headers: {
      Authorization: token,
    },
    signal,
    ...dconfig,
  };
  let reqHash = sparkMd5.hash(url + JSON.stringify(params));
  if (params instanceof FormData) {
    reqHash = `${Date.now()}${Math.round(Math.random() * 10000)}`;
  }

  // 如果有取消请求控制，则不再缓存
  if (!signal && reqObjectList[reqHash] && !whiteList.includes(url)) {
    return reqObjectList[reqHash];
  }
  switch (method) {
    case 'get':
      reqObjectList[reqHash] = instance.get(url, { params, ...config });
      break;
    case 'post':
      reqObjectList[reqHash] = instance.post(url, params, config);
      break;
    default:
      reqObjectList[reqHash] = instance({
        url,
        method,
        data: params,
        ...config,
      });
      break;
  }
  reqObjectList[reqHash]?.finally(() => {
    delete reqObjectList[reqHash];
  });
  return reqObjectList[reqHash];
}

// 响应拦截器
instance.interceptors.response.use(
  (res: AxiosResponse) => {
    if (res.headers['content-disposition']) {
      return Promise.resolve(res);
    }
    if (res.data.code === 0) {
      return Promise.resolve(res.data);
    }
    if (res.data.code === 999) {
      if (router.currentRoute.value.name !== 'login') {
        useUserStore().logout();
        Message.error('登录失效，请重新登录');
        router.push({ name: 'login' });
      }
      return Promise.reject(new Error('登录失效'));
    }
    if (res.config.url !== '/api/travel/orderCalculate') {
      Message.error({
        content: JSON.stringify(res.data.msg || '网络错误'),
      });
    }

    return Promise.reject(res.data);
  },
  (error) => {
    // eslint-disable-next-line prefer-rest-params
    if (!axios.isCancel(error)) {
      Message.error({
        content: JSON.stringify('网络错误'),
        resetOnHover: true,
      });
    }
    return Promise.reject(error);
  }
);
