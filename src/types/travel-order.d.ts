export interface InquiryOrderInfo {
  // 询价单编号 编辑用
  order_no: string;
  /**
   * 成人人数
   */
  adult_num: number | null;
  /**
   * new-地接id
   */
  agency_id: number | null;
  /**
   * 路线名称
   */
  area: string;
  /**
   * 儿童人数
   */
  children_num: number | null;
  /**
   * 客户昵称
   */
  customer_name: string;
  /**
   * new-路线id
   */
  line_id: number | null;
  /**
   * 地接名称
   */
  local_travel_agency: string;
  /**
   * new-产品id
   */
  product_id: number | null;
  /**
   * 返程日期
   */
  real_travel_end_date: null;
  /**
   * 出行日期
   */
  real_travel_start_date: null;
  /**
   * 备注
   */
  remark: null;
  /**
   * 床位数
   */
  standard_room: number | null;
  /**
   * 线索id(二级线索录入用)
   */
  thread_id: number | null;
  /**
   * new-票务公司
   */
  ticket_company: string;
  /**
   * 总团款
   */
  total_cut_price: number | null;
  /**
   * 结算价
   */
  total_final_price: null;
  /**
   * 结算价说明
   */
  total_final_price_detail: string;
  /**
   * 产品名称
   */
  travel_line: string;
}

export interface ContractOtherDetail {
  expend: number | null;
  income: number | null;
  name: string;
}

export interface MediaOrderInfo {
  advance_price?: number | null;
  media_order_no?: string;
  order_create_time?: string;
}

export interface ServiceOtherDetail {
  expend: number | null;
  income: number | null;
  name: string;
}

export interface OrderInfo {
  // 订单编号 编辑用
  order_no: string;
  /**
   * new-订单创建时间
   */
  /**
   * new-机票支出
   */
  air_ticket_expend: number | null;
  /**
   * new-赔付金额
   */
  compensate_amount: number | null;
  /**
   * new-赔付证明 (两个截图)
   */
  compensate_explain_image_1: string;
  compensate_explain_image_2: string;
  contract_other_detail: ContractOtherDetail[];
  /**
   * new-客服其他支出
   */
  contract_other_expend_total: number | null;
  /**
   * new-合同其他收入
   */
  contract_other_income_total: number | null;
  /**
   * new-出发城市
   */
  departure_city: string;
  /**
   * new-返回城市
   */
  return_city: string;
  /**
   * new-预估服务费
   */
  estimated_service_price: number | null;
  media_order_info: MediaOrderInfo[];
  miniapp_product_id: string;
  miniapp_product_name: string;
  // 小程序订单号
  miniapp_no: string;
  order_new_type: string;
  order_new_type_detail: string;
  order_new_type_origin_customer_nickname: string;
  order_new_type_origin_customer_phone: string;
  order_source: string;
  payment_type: string;
  /**
   * new-利润
   */
  profit: number | null;
  /**
   * new-房间信息
   */
  room_info: string;
  service_other_detail: ServiceOtherDetail[];
  /**
   * new-客服其他收入支出-明细 [{"name":"测试的", "income":100, "expend": 100}]
   */
  service_other_expend_total: number | null;
  /**
   * new-客服其他支出
   */
  service_other_income_total: number | null;
  // 客服
  service_user: string;
  service_user_fs_id: string;
  // 来源媒体
  source: string;
  // 手机号
  phone: string;
  // 联系人姓名
  customer_name: string;
  // 联系人手机号
  customer_phone: string;
  // 是否小程序支付 -1:不是 1:是
  is_miniapp_pay: -1 | 1 | 2;
  // 出行航班时间
  fs_doc_travel_time: string;
  // 游客信息
  fs_doc_travel_people: string;
  advance_price: number | null;
  is_refund_follow_up_again: '是' | '否' | null;
  is_call_connected: '是' | '否' | null;
  customer_intentionality: string;
  confirmation_images: string[];
  // 定金收款方
  payee_id: number | null;
  payee_name: string;
  // 收款截图凭证
  payee_images: string[];
}

export interface OrderAndPrice {
  // 询价单信息
  inquiry_order_info: InquiryOrderInfo;
  // 订单信息
  order_info: OrderInfo;
  // 转线下订单号
  transfer_offline_order: null | string;
  order_settle: boolean;
}
