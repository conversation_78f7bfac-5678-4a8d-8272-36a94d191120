<template>
  <div class="tourist-info-editor">
    <!-- 文本输入框 -->
    <a-textarea
      ref="textareaRef"
      v-model="localValue"
      :auto-size="autoSize"
      :placeholder="placeholder"
      allow-clear
      @blur="handleBlur"
      @input="handleInput"
    />
    <!-- 格式错误提示 -->
    <!-- <div v-if="showError && validationResult.message" class="error-message">
      <icon-exclamation-circle-fill class="error-icon" />
      <span>{{ validationResult.message }}</span>
    </div> -->

    <!-- 高亮预览（仅在有错误时显示） -->
    <!-- <div
      v-if="showHighlight && !validationResult.isValid"
      class="highlight-preview"
    >
      <div class="highlight-title">格式检测预览：</div>
      <div class="highlight-content">
        <span
          v-for="(item, index) in highlightInfo"
          :key="index"
          :class="{
            'highlight-invalid': item.isInvalid,
            'highlight-phone': item.type === 'phone' && item.isInvalid,
            'highlight-idcard': item.type === 'idcard' && item.isInvalid,
          }"
          >{{ item.text }}</span
        >
      </div>
    </div> -->
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, nextTick } from 'vue';
  import {
    validateTouristInfo,
    generateHighlightInfo,
    type ValidationResult,
    type HighlightInfo,
  } from '@/utils/tourist-info-validator';

  interface Props {
    modelValue?: string;
    placeholder?: string;
    autoSize?: { minRows?: number; maxRows?: number } | boolean;
    showHighlight?: boolean; // 是否显示高亮预览
  }

  interface Emits {
    (e: 'update:modelValue', value: string): void;
    (e: 'validationChange', result: ValidationResult): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: '',
    placeholder: '请输入游客信息',
    autoSize: () => ({ minRows: 5 }),
    showHighlight: true,
  });

  const emit = defineEmits<Emits>();

  const textareaRef = ref();
  const localValue = ref(props.modelValue);
  const showError = ref(false);
  const validationResult = ref<ValidationResult>({
    isValid: true,
    invalidPhones: [],
    invalidIdCards: [],
    message: '',
  });

  // 验证内容
  const validateContent = () => {
    const result = validateTouristInfo(localValue.value);
    validationResult.value = result;
    emit('validationChange', result);
  };

  // 计算高亮信息
  const highlightInfo = computed<HighlightInfo[]>(() => {
    if (!localValue.value) return [];
    return generateHighlightInfo(localValue.value);
  });

  // 监听外部值变化
  watch(
    () => props.modelValue,
    (newValue) => {
      localValue.value = newValue || '';
      validateContent();
    },
    { immediate: true }
  );

  // 监听本地值变化
  watch(localValue, (newValue) => {
    emit('update:modelValue', newValue);
  });

  // 处理输入
  const handleInput = () => {
    // 输入时隐藏错误提示，避免干扰用户输入
    showError.value = false;
  };

  // 处理失去焦点
  const handleBlur = () => {
    validateContent();
    // 失去焦点时显示错误提示
    showError.value = !validationResult.value.isValid;
  };

  // 暴露验证方法
  const validate = () => {
    validateContent();
    showError.value = !validationResult.value.isValid;
    return validationResult.value;
  };

  defineExpose({
    validate,
    validationResult: computed(() => validationResult.value),
  });
</script>

<style scoped lang="less">
  .tourist-info-editor {
    width: 100%;
    .error-message {
      display: flex;
      align-items: center;
      margin-top: 8px;
      padding: 8px 12px;
      background-color: #fff2f0;
      border: 1px solid #ffccc7;
      border-radius: 6px;
      color: #ff4d4f;
      font-size: 14px;

      .error-icon {
        margin-right: 6px;
        font-size: 16px;
      }
    }

    .highlight-preview {
      margin-top: 12px;
      padding: 12px;
      background-color: #fafafa;
      border: 1px solid #d9d9d9;
      border-radius: 6px;

      .highlight-title {
        font-size: 12px;
        color: #666;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .highlight-content {
        line-height: 1.6;
        white-space: pre-wrap;
        word-break: break-all;

        .highlight-invalid {
          background-color: #ff4d4f;
          color: #fff;
          padding: 2px 4px;
          border-radius: 3px;
          font-weight: 500;
        }

        .highlight-phone {
          background-color: #ff7875;
        }

        .highlight-idcard {
          background-color: #ff4d4f;
        }
      }
    }
  }
</style>
