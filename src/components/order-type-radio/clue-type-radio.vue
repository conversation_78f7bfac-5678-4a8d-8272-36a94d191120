<template>
  <div class="order-type-radio-container">
    <!-- 加载状态 -->
    <div v-if="loading && !hasData" class="loading-state">
      <a-spin />
      <span class="loading-text">正在加载线索类型数据...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error && !hasData" class="error-state">
      <icon-exclamation-circle class="error-icon" />
      <span class="error-text">{{ error }}</span>
      <a-button size="small" type="text" @click="fetchOrderStatistics">
        重试
      </a-button>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!hasData && !loading" class="empty-state">
      <icon-empty class="empty-icon" />
      <span class="empty-text">暂无线索类型数据</span>
    </div>

    <!-- 正常数据显示 -->
    <a-radio-group
      v-else
      :model-value="internalValue"
      class="order-type-radio-group"
      @update:model-value="handleChange"
    >
      <template v-for="item in dataList" :key="item.value">
        <a-badge
          :count="item.show_num ? item.num : 0"
          :offset="[-11, 4]"
          :dot-style="{
            minWidth: '16px',
            fontSize: '10px',
            lineHeight: '18px',
            padding: '0 4px',
            height: '18px',
          }"
        >
          <a-radio :value="item.value">
            <template #radio="{ checked }">
              <a-space
                align="start"
                class="custom-radio-card"
                :class="{ 'custom-radio-card-checked': checked }"
              >
                <div className="custom-radio-card-mask">
                  <div className="custom-radio-card-mask-dot" />
                </div>
                <div>
                  <div className="custom-radio-card-title">
                    {{ item.label }}
                    <a-tooltip
                      v-if="item.tooltip"
                      position="top"
                      :content-style="{ whiteSpace: 'pre-line' }"
                    >
                      <template #content>
                        {{ item.tooltip }}
                      </template>
                      <icon-question-circle class="tooltip-icon" />
                    </a-tooltip>
                  </div>
                </div>
              </a-space>
            </template>
          </a-radio>
        </a-badge>
      </template>
    </a-radio-group>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, onMounted, watch, nextTick } from 'vue';
  import request from '@/api/request';

  interface OrderTypeItem {
    label: string;
    value: string | number;
    tooltip?: string;
    num?: number; // 统计数量字段
    show_num?: boolean; // 是否显示数字
  }

  // 接口返回的原始数据结构
  interface ApiOrderTypeItem {
    label: string;
    value: string | number;
    tooltip?: string;
    num?: number;
    show_num?: boolean;
    // 可能的其他字段
    [key: string]: any;
  }

  interface Props {
    modelValue?: string | number;
    dataList?: OrderTypeItem[];
    size?: 'mini' | 'small' | 'medium' | 'large';
    type?: 'radio' | 'button';
    disabled?: boolean;
    disabledValues?: (string | number)[];
    compact?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: '',
    dataList: () => [],
    size: 'medium',
    type: 'button',
    disabled: false,
    disabledValues: () => [],
    compact: false,
  });

  const emit = defineEmits<{
    'update:modelValue': [value: string | number];
    'change': [value: string | number];
  }>();

  // 响应式数据
  const loading = ref(false);
  const dynamicDataList = ref<OrderTypeItem[]>([]);
  const error = ref<string>('');

  // 计算属性：优先使用传入的 dataList，否则使用动态获取的数据
  const dataList = computed(() => {
    if (props.dataList && props.dataList.length > 0) {
      return props.dataList;
    }
    return dynamicDataList.value;
  });

  // 判断是否有数据
  const hasData = computed(() => {
    return dataList.value && dataList.value.length > 0;
  });

  // 验证值是否在数据列表中存在
  const isValidValue = (value: string | number): boolean => {
    if (value === '' || value === null || value === undefined) {
      return true; // 空值总是有效的
    }
    return dataList.value.some((item) => item.value === value);
  };

  // 获取有效的值（如果当前值无效，返回默认值）
  const getValidValue = (value: string | number): string | number => {
    // 如果值为空或未定义，返回空字符串
    if (value === null || value === undefined) {
      return '';
    }

    // 如果数据还在加载中，暂时保持原值
    if (loading.value) {
      return value;
    }

    // 如果没有数据，返回空字符串
    if (!hasData.value) {
      return '';
    }

    // 检查值是否在数据列表中存在
    if (isValidValue(value)) {
      return value;
    }

    // 如果值无效，返回第一个选项的值或空字符串
    return dataList.value.length > 0 ? dataList.value[0].value : '';
  };

  // 内部值状态管理
  const internalValue = ref<string | number>(
    getValidValue(props.modelValue ?? '')
  );

  // 数据转换函数：将接口返回的数据转换为组件需要的格式
  const transformApiData = (apiData: any): OrderTypeItem[] => {
    if (!apiData) return [];

    // 如果接口返回的是数组格式
    if (Array.isArray(apiData)) {
      return apiData.map((item: ApiOrderTypeItem) => ({
        label: item.label || '',
        value: item.value ?? '',
        tooltip: item.tooltip,
        num: item.num ?? 0,
        show_num: item.show_num ?? false,
      }));
    }

    // 如果接口返回的是对象格式（key-value 形式的统计数据）
    if (typeof apiData === 'object') {
      // 假设接口返回格式为 { "全部": 100, "待受理": 20, ... }
      // 需要根据实际接口格式调整这部分逻辑
      const defaultOrderTypes = [];

      return defaultOrderTypes.map((item) => ({
        ...item,
        num: apiData[item.value] ?? 0,
        show_num: item.show_num ?? false,
      }));
    }

    return [];
  };

  // 获取线索统计数据
  const fetchOrderStatistics = async () => {
    try {
      loading.value = true;
      error.value = '';

      const response = await request('/api/thread/threadStatistics/', {});

      if (response && response.data) {
        const transformedData = transformApiData(response.data);

        if (transformedData.length > 0) {
          dynamicDataList.value = transformedData;
        } else {
          error.value = '接口返回的数据格式不正确';
          dynamicDataList.value = [];
        }
      } else {
        error.value = '接口返回数据为空';
        dynamicDataList.value = [];
      }
    } catch (err: any) {
      error.value = err.message || '获取线索统计数据失败';
      console.error('获取线索统计数据失败:', err);
      dynamicDataList.value = [];
    } finally {
      loading.value = false;
    }
  };

  // 处理内部值变化
  const handleChange = (value: string | number) => {
    const validValue = getValidValue(value);
    internalValue.value = validValue;
    emit('update:modelValue', validValue);
    emit('change', validValue);
  };

  // 监听外部 modelValue 变化
  watch(
    () => props.modelValue,
    (newValue) => {
      const validValue = getValidValue(newValue ?? '');
      if (internalValue.value !== validValue) {
        internalValue.value = validValue;
      }
    },
    { immediate: true }
  );

  // 监听数据列表变化，重新验证当前值
  watch(
    dataList,
    () => {
      nextTick(() => {
        const validValue = getValidValue(internalValue.value);
        if (internalValue.value !== validValue) {
          internalValue.value = validValue;
          emit('update:modelValue', validValue);
        }
      });
    },
    { deep: true }
  );

  // 监听加载状态变化，数据加载完成后重新验证值
  watch(loading, (newLoading, oldLoading) => {
    // 当加载完成时（从 true 变为 false）
    if (oldLoading && !newLoading) {
      nextTick(() => {
        const validValue = getValidValue(props.modelValue ?? '');
        if (internalValue.value !== validValue) {
          internalValue.value = validValue;
          emit('update:modelValue', validValue);
        }
      });
    }
  });

  // 需要着重展示的类型
  const importantOrderTypes = [
    7, // 待受理
    1, // "待联系"
    8, // '待审核'
  ];

  // 组件挂载时获取数据
  onMounted(() => {
    // 只有在没有传入 dataList 时才调用接口
    if (!props.dataList || props.dataList.length === 0) {
      fetchOrderStatistics();
    }
  });

  // 手动设置值的方法
  const setValue = (value: string | number) => {
    const validValue = getValidValue(value);
    internalValue.value = validValue;
    emit('update:modelValue', validValue);
    emit('change', validValue);
  };

  // 获取当前值的方法
  const getValue = () => {
    return internalValue.value;
  };

  // 检查当前值是否有效的方法
  const isCurrentValueValid = () => {
    return isValidValue(internalValue.value);
  };

  // 暴露组件方法
  defineExpose({
    refresh: fetchOrderStatistics,
    setValue,
    getValue,
    isCurrentValueValid,
    getValidValue,
    isValidValue,
  });
</script>

<style scoped lang="less">
  .order-type-radio-container {
    min-height: 20px;
  }

  // 状态样式
  .loading-state,
  .error-state,
  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: var(--color-text-3);
    font-size: 14px;
    min-height: 60px;
  }

  .loading-state {
    .loading-text {
      margin-left: 8px;
    }
  }

  .error-state {
    flex-direction: column;
    gap: 8px;

    .error-icon {
      font-size: 18px;
      color: var(--color-danger);
    }

    .error-text {
      color: var(--color-danger);
    }
  }

  .empty-state {
    flex-direction: column;
    gap: 8px;

    .empty-icon {
      font-size: 18px;
      color: var(--color-text-4);
    }

    .empty-text {
      color: var(--color-text-4);
    }
  }

  // 原有样式
  .custom-radio-card {
    padding: 8px 16px 8px;
    border: 1px solid var(--color-border-2);
    border-radius: 4px;
    box-sizing: border-box;
    min-width: 110px;
  }

  .custom-radio-card-mask {
    height: 14px;
    width: 14px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 100%;
    border: 1px solid var(--color-border-2);
    box-sizing: border-box;
  }

  .custom-radio-card-mask-dot {
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }

  .custom-radio-card-title {
    color: var(--color-text-1);
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .custom-radio-card-subtitle {
    color: var(--color-text-2);
    font-size: 12px;
    display: flex;
    align-items: baseline;
    &.hide {
      visibility: hidden;
      opacity: 0;
    }
    &.weight {
      color: var(--color-text-1);
      font-weight: bold;
    }
    .custom-radio-card-subtitle-value {
      font-size: 12px;
    }
    .custom-radio-card-subtitle-unit {
      font-size: 10px;
      margin-left: 2px;
    }
  }

  .custom-radio-card:hover,
  .custom-radio-card-checked,
  .custom-radio-card:hover .custom-radio-card-mask,
  .custom-radio-card-checked .custom-radio-card-mask {
    border-color: rgb(var(--primary-6));
  }

  .custom-radio-card-mask {
    margin-top: 1px;
  }

  .custom-radio-card-checked {
    background-color: var(--color-primary-light-2);
  }

  .custom-radio-card:hover .custom-radio-card-title,
  .custom-radio-card-checked .custom-radio-card-title {
    color: rgb(var(--primary-6));
  }

  .custom-radio-card-checked .custom-radio-card-mask-dot {
    background-color: rgb(var(--primary-6));
  }

  :deep(.arco-radio) {
    margin-right: 10px;
  }
</style>
