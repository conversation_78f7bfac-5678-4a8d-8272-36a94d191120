<template>
  <button class="w100p upload-box" type="button" @paste="pasteImgAction">
    <a-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      :accept="fileType"
      action="/api/uploadFile"
      :data="sendParams"
      :headers="headers"
      :limit="limit"
      :list-type="listType"
      image-preview
      :multiple="multiple"
      :disabled="disabled"
      v-bind="$attrs"
      :on-before-remove="beforeRemove"
      @success="handleSuccess"
      @error="uploadError"
    >
    </a-upload>
  </button>
</template>

<script lang="ts" setup>
  import { getToken } from '@/utils/auth';
  import { ref, watch } from 'vue';
  import { FileItem, Message } from '@arco-design/web-vue';
  import { isArray, toString } from 'lodash';
  import { UploadInstance } from '@arco-design/web-vue/es/upload';

  const props = defineProps({
    modelValue: {
      type: [Array, String],
      default: () => [],
    },
    fileType: {
      type: String,
      default: '',
    },
    sendParams: {
      type: Object,
      default: () => ({}),
    },
    multiple: {
      type: Boolean,
      default: () => true,
    },
    disabled: {
      type: Boolean,
      default: () => false,
    },
    limit: {
      type: Number,
      default: () => 99,
    },
    listType: {
      type: String,
      default: 'picture-card',
    },
  });

  const headers = { Authorization: getToken() };
  const emits = defineEmits(['update:modelValue', 'success']);
  const fileList = ref<FileItem[]>([]);
  const uploadRef = ref<UploadInstance>();

  watch(
    () => (props.multiple ? props.modelValue?.length : props.modelValue),
    () => {
      if (props.multiple) {
        if (isArray(props.modelValue)) {
          if (
            props.modelValue?.length !==
            fileList.value.filter((item: FileItem) => item.status === 'done')
              .length
          ) {
            let needUpdateUrls = props.modelValue.filter(
              (item: string) =>
                !fileList.value.find((val: FileItem) => val.url !== item)
            );
            fileList.value = fileList.value.filter(
              (item: FileItem) =>
                item.status !== 'done' ||
                (item.url && props.modelValue?.includes(item.url))
            );
            fileList.value.push(
              ...needUpdateUrls.map(
                (item: string, index: number) =>
                  ({
                    uid: toString(index),
                    status: 'done',
                    url: item,
                  } as FileItem)
              )
            );
          }
        } else {
          fileList.value = [];
        }
      } else if (
        !fileList.value.length ||
        fileList.value[0].response?.data?.url !== props.modelValue
      ) {
        if (props.modelValue) {
          fileList.value = [
            {
              uid: '1',
              status: 'done',
              url: props.modelValue as string,
            },
          ];
        } else {
          fileList.value = [];
        }
      }
    },
    {
      immediate: true,
    }
  );

  function handleSuccess(fileItem: FileItem) {
    if (fileItem.response?.code === 0) {
      fileItem.url = fileItem.response.data.url;
      emits(
        'update:modelValue',
        props.multiple
          ? [...(props.modelValue || []), fileItem.response.data.url]
          : fileItem.response.data.url
      );
      emits('success', fileItem);
    } else {
      fileList.value = fileList.value.filter(
        (item: FileItem) => item.uid !== fileItem.uid
      );
      Message.error(fileItem.response?.msg || '上传失败，请重试');
    }
  }
  function uploadError(fileItem: FileItem) {
    if (fileItem.response?.code === 1) {
      fileList.value = fileList.value.filter(
        (item: FileItem) => item.uid !== fileItem.uid
      );
      Message.error(fileItem.response?.msg || '上传失败，请重试');
    } else {
      Message.error('上传失败，请重试');
    }
  }

  const beforeRemove = (file: FileItem) => {
    return new Promise((resolve, reject) => {
      if (file.status === 'done') {
        if (props.multiple) {
          if (isArray(props.modelValue)) {
            emits(
              'update:modelValue',
              props.modelValue.filter((item: string) => item !== file.url)
            );
          }
        } else {
          emits('update:modelValue', '');
        }
      }
      resolve(true);
    });
  };

  function pasteImgAction(event: ClipboardEvent) {
    // @ts-ignore
    const { items } = event.clipboardData || window.clipboardData;
    for (let i = 0; i < items.length; i += 1) {
      if (items[i].type.indexOf('image') === 0) {
        let blob = items[i].getAsFile();
        uploadRef.value?.upload([blob]);
      }
    }
  }
</script>

<style lang="less" scoped>
  .upload-box {
    display: inline-block;
    outline: 0;
    background: transparent;
    border: 1px solid var(--color-border-1);
    padding: 5px;
    border-radius: 4px;
    &:focus {
      border: 1px solid rgb(var(--primary-6));
    }
    :deep(.arco-upload-drag) {
      padding: 20px 0;
      .arco-icon-plus {
        margin-bottom: 10px;
      }
    }
  }
</style>
