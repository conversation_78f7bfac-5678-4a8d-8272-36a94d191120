<template>
  <div class="navbar">
    <div class="left-side">
      <a-space>
        <img
          alt="logo"
          src="@/assets/images/logo-txt.png"
          style="width: 130px"
        />
        <icon-menu-fold
          v-if="appStore.device === 'mobile'"
          style="font-size: 22px; cursor: pointer"
          @click="toggleDrawerMenu"
        />
      </a-space>
    </div>
    <div class="center-side">
      <Menu
        v-if="topMenu && appStore.device === 'desktop'"
        :is-need-collapsed="false"
        mode="horizontal"
      />
    </div>
    <ul class="right-side">
      <!--<li>
        <a-tooltip :content="$t('settings.search')">
          <a-button class="nav-btn" type="outline" :shape="'circle'">
            <template #icon>
              <icon-search />
            </template>
          </a-button>
        </a-tooltip>
      </li>-->
      <!--多语言切换-->
      <!--<li>
        <a-tooltip :content="$t('settings.language')">
          <a-button
            class="nav-btn"
            type="outline"
            :shape="'circle'"
            @click="setDropDownVisible"
          >
            <template #icon>
              <icon-language />
            </template>
          </a-button>
        </a-tooltip>
        <a-dropdown trigger="click" @select="changeLocale as any">
          <div ref="triggerBtn" class="trigger-btn"></div>
          <template #content>
            <a-doption
              v-for="item in locales"
              :key="item.value"
              :value="item.value"
            >
              <template #icon>
                <icon-check v-show="item.value === currentLocale" />
              </template>
              {{ item.label }}
            </a-doption>
          </template>
        </a-dropdown>
      </li>-->
      <li>
        <a-tooltip
          :content="
            theme === 'light'
              ? $t('settings.navbar.theme.toDark')
              : $t('settings.navbar.theme.toLight')
          "
        >
          <a-button
            class="nav-btn"
            type="outline"
            :shape="'circle'"
            @click="handleToggleTheme"
          >
            <template #icon>
              <icon-sun-fill v-if="theme === 'dark'" />
              <icon-moon-fill v-else />
            </template>
          </a-button>
        </a-tooltip>
      </li>
      <li>
        <a-tooltip
          :content="
            isFullscreen
              ? $t('settings.navbar.screen.toExit')
              : $t('settings.navbar.screen.toFull')
          "
        >
          <a-button
            class="nav-btn"
            type="outline"
            :shape="'circle'"
            @click="toggleFullScreen"
          >
            <template #icon>
              <icon-fullscreen-exit v-if="isFullscreen" />
              <icon-fullscreen v-else />
            </template>
          </a-button>
        </a-tooltip>
      </li>
      <!--消息通知-->
      <!--<li>
        <a-tooltip :content="$t('settings.navbar.alerts')">
          <div class="message-box-trigger">
            <a-badge :count="9" dot>
              <a-button
                class="nav-btn"
                type="outline"
                :shape="'circle'"
                @click="setPopoverVisible"
              >
                <icon-notification />
              </a-button>
            </a-badge>
          </div>
        </a-tooltip>
        <a-popover
          trigger="click"
          :arrow-style="{ display: 'none' }"
          :content-style="{ padding: 0, minWidth: '400px' }"
          content-class="message-popover"
        >
          <div ref="refBtn" class="ref-btn"></div>
          <template #content>
            <message-box />
          </template>
        </a-popover>
      </li>-->

      <!--页面布局相关设置-->
      <!--<li>
        <a-tooltip :content="$t('settings.title')">
          <a-button
            class="nav-btn"
            type="outline"
            :shape="'circle'"
            @click="setVisible"
          >
            <template #icon>
              <icon-settings />
            </template>
          </a-button>
        </a-tooltip>
      </li>-->
      <li
        v-if="userStore.hasPermission(3) && userStore.service_status === '在线'"
      >
        <a-badge status="success" text="在线" />
      </li>
      <li
        v-if="userStore.hasPermission(3) && userStore.service_status === '离线'"
      >
        <a-badge status="danger" text="离线" />
      </li>
      <li>
        <UserGuide></UserGuide>
      </li>
      <!-- <li>
        <RelationGuide></RelationGuide>
      </li>-->
      <!-- <li>
        <a-dropdown trigger="hover" position="br">
          <a-avatar
            :size="30"
            :style="{
              marginRight: '8px',
              cursor: 'pointer',
              backgroundColor: '#0063fa',
            }"
          >
            <IconUser />
          </a-avatar>
          <template #content>
            <a-doption>
              <a-space @click="switchRoles">
                <icon-tag />
                <span>
                  {{ $t('messageBox.switchRoles') }}
                </span>
              </a-space>
            </a-doption>
            <a-doption>
              <a-space @click="$router.push({ name: 'Info' })">
                <icon-user />
                <span>
                  {{ $t('messageBox.userCenter') }}
                </span>
              </a-space>
            </a-doption>
            <a-doption>
              <a-space @click="$router.push({ name: 'Setting' })">
                <icon-settings />
                <span>
                  {{ $t('messageBox.userSettings') }}
                </span>
              </a-space>
            </a-doption>
            <div class="userinfo-box">
              <IconUser /> {{ userStore.user_name }}({{ userStore.account_name }})
            </div>
            <div class="userinfo-box">
              <icon-robot /> {{ versionStore.versionStr }}
            </div>
            <a-doption @click="handleLogout">
              <a-link>
                <template #icon>
                  <icon-export />
                </template>
                {{ $t('messageBox.logout') }}
              </a-link>
            </a-doption>
          </template>
        </a-dropdown>
      </li> -->
    </ul>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, inject, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { useDark, useToggle, useFullscreen } from '@vueuse/core';
  import { useAppStore, useUserStore } from '@/store';
  import { LOCALE_OPTIONS } from '@/locale';
  import useLocale from '@/hooks/locale';
  import useUser from '@/hooks/user';
  import Menu from '@/components/menu/index.vue';
  import { useVersionStore } from '@/hooks/check-version';
  import MessageBox from '../message-box/index.vue';
  import VipButton from './vip-button.vue';
  import UserGuide from './user-guide.vue';
  import RelationGuide from './relation-guide.vue';

  const router = useRouter();

  const appStore = useAppStore();
  const userStore = useUserStore();
  const versionStore = useVersionStore();
  const { logout } = useUser();
  const { changeLocale, currentLocale } = useLocale();
  const { isFullscreen, toggle: toggleFullScreen } = useFullscreen();
  const locales = [...LOCALE_OPTIONS];
  const avatar = computed(() => {
    return userStore.avatar;
  });
  const theme = computed(() => {
    return appStore.theme;
  });
  const topMenu = computed(() => appStore.topMenu && appStore.menu);
  const isDark = useDark({
    selector: 'body',
    attribute: 'arco-theme',
    valueDark: 'dark',
    valueLight: 'light',
    storageKey: 'arco-theme',
    onChanged(dark: boolean) {
      // overridden default behavior
      appStore.toggleTheme(dark);
    },
  });
  const toggleTheme = useToggle(isDark);
  const handleToggleTheme = () => {
    toggleTheme();
  };
  const setVisible = () => {
    appStore.updateSettings({ globalSettings: true });
  };
  const refBtn = ref();
  const triggerBtn = ref();

  const setPopoverVisible = () => {
    const event = new MouseEvent('click', {
      view: window,
      bubbles: true,
      cancelable: true,
    });
    refBtn.value.dispatchEvent(event);
  };
  const handleLogout = () => {
    logout();
  };
  const setDropDownVisible = () => {
    const event = new MouseEvent('click', {
      view: window,
      bubbles: true,
      cancelable: true,
    });
    triggerBtn.value.dispatchEvent(event);
  };
  const switchRoles = async () => {
    // const res = await userStore.switchRoles();
    // Message.success(res as string);
  };
  const toggleDrawerMenu = inject('toggleDrawerMenu') as () => void;

  // 任务中心点击跳转导航
  const gotoTaskRoute = (key: string) => {
    router.push({
      name: 'task-manage',
      query: {
        type: key,
      },
    });
  };
</script>

<style scoped lang="less">
  .navbar {
    display: flex;
    justify-content: space-between;
    height: 100%;
    transition: all 0.2s;
    //background-color: var(--color-bg-2);
    //border-bottom: 1px solid var(--color-border);
    &.hasbg {
      background-color: var(--color-bg-2);
      box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    }
  }

  .left-side {
    display: flex;
    align-items: center;
    padding: 0;
    width: 224px;
    justify-content: center;
  }

  .center-side {
    flex: 1;
    padding-left: 20px;
  }

  .right-side {
    display: flex;
    padding-right: 10px;
    list-style: none;
    :deep(.locale-select) {
      border-radius: 20px;
    }
    li {
      display: flex;
      align-items: center;
      padding: 0 10px;
    }

    a {
      color: var(--color-text-1);
      text-decoration: none;
    }
    .nav-btn {
      border-color: rgb(var(--gray-2));
      color: rgb(var(--gray-8));
      font-size: 16px;
    }
    .trigger-btn,
    .ref-btn {
      position: absolute;
      bottom: 14px;
    }
    .trigger-btn {
      margin-left: 14px;
    }
  }
  .userinfo-box {
    padding: 10px 15px;
    font-size: 14px;
    opacity: 0.8;
    color: var(--color-text-1);
  }
  .logo-icon {
    height: 12px;
  }
  .logo-text {
    padding-left: 3px;
    font-weight: 500;
    transition: all 0.2s linear;
    &:hover {
      text-decoration: underline;
    }
  }
  .task-center {
    cursor: pointer;
    .task-down-icon {
      transition: all 0.2s linear;
    }
    &:hover {
      .task-down-icon {
        transform: rotate(180deg);
      }
    }
  }
</style>

<style lang="less">
  .message-popover {
    .arco-popover-content {
      margin-top: 0;
    }
  }
</style>
