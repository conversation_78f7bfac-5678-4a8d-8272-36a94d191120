<template>
  <a-dropdown trigger="hover" position="bottom">
    <div class="user-box bar">
      <div class="left-box">
        <a-avatar :size="32" :style="{ backgroundColor: '#2878FF' }">
          <IconUser />
        </a-avatar>
      </div>
      <div class="center-box">
        <div class="username">
          {{ userStore.user_name }}
        </div>
        <div class="intro">{{ userStore.account_name }} </div>
      </div>
      <div class="right-box">
        <icon-caret-down class="caret-down" />
      </div>
      <div class="userinfo-box"> </div>
    </div>
    <template #content>
      <div class="hover-content">
        <div class="hover-title"> 当前登录账户 </div>
        <div class="user-box">
          <div class="left-box">
            <a-avatar :size="32" :style="{ backgroundColor: '#2878FF' }">
              <IconUser />
            </a-avatar>
          </div>
          <div class="center-box">
            <div class="username">
              {{ userStore.user_name }}
            </div>
            <div class="intro">{{ userStore.account_name }} </div>
          </div>
          <div class="right-box"> </div>
          <div class="userinfo-box"> </div>
        </div>
        <a-divider :margin="10" style="margin-bottom: 0" />
        <div class="menu-box">
          <div
            v-if="userStore.hasPermission(3)"
            class="menu-item"
            @click="switchStatus"
          >
            <icon-customer-service />
            <span class="ml-5">
              {{
                userStore.service_status === '在线' ? '我要离线' : '我要上线'
              }}
            </span>
          </div>
          <div class="login-out" @click="handleLogout">
            <icon-poweroff />
            <span class="ml-5">退出登录</span>
          </div>
        </div>
      </div>
    </template>
  </a-dropdown>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { useAppStore, useUserStore } from '@/store';
  import request from '@/api/request';
  import useUser from '@/hooks/user';
  import { Message } from '@arco-design/web-vue';

  const emit = defineEmits(['update:modelValue']);

  const userStore = useUserStore();
  const { logout } = useUser();
  const guide = ref();

  const props = defineProps({
    modelValue: {
      type: [String, Number, Array],
      default: () => '',
    },
  });
  const handleLogout = () => {
    logout();
  };
  let loading = false;
  const switchStatus = () => {
    if (!loading) {
      loading = true;
      let res = userStore.service_status === '在线' ? '离线' : '在线';
      request('/api/user/updateServiceStatus', {
        user_id: userStore.id,
        status: res,
      })
        .then(() => {
          Message.success('操作成功');
          userStore.service_status = res;
        })
        .finally(() => {
          loading = false;
        });
    }
  };
</script>

<style lang="less" scoped>
  .user-box {
    display: flex;
    align-items: center;
    padding: 5px 10px;
    cursor: pointer;
    transition: all 0.1s linear;
    .caret-down {
      transition: all 0.2s linear;
      color: var(--color-text-2);
      margin-left: 3px;
    }
    &.bar {
      &:hover {
        border-radius: 25px;
        background: var(--color-fill-3);
        .caret-down {
          transform: rotate(180deg);
        }
      }
    }

    .left-box {
      margin-right: 5px;
    }
    .center-box {
      display: flex;
      flex-direction: column;
      .username {
        font-size: 12px;
        color: var(--color-text-1);
      }
      .intro {
        font-size: 12px;
        color: var(--color-text-3);
        margin-top: 2px;
      }
    }
  }

  .hover-content {
    .hover-title {
      color: var(--color-text-3);
      font-size: 12px;
      padding: 10px 10px;
    }
    .login-out {
      color: var(--color-text-1);
      padding: 10px 10px 10px 10px;
      cursor: pointer;
      &:hover {
        background: var(--color-fill-2);
      }
    }
    .menu-box {
      .menu-item {
        color: var(--color-text-1);
        cursor: pointer;
        padding: 8px 10px;
        &:hover {
          background: var(--color-fill-2);
        }
      }
    }
  }
</style>
