<script lang="tsx">
  import { defineComponent, ref, h, compile, computed } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { useRoute, useRouter } from 'vue-router';
  import type { RouteMeta } from 'vue-router';
  import { useAppStore } from '@/store';
  import { listenerRouteChange } from '@/utils/route-listener';
  import { openWindow, regexUrl } from '@/utils';
  import { AppRouteRecordRaw } from '@/router/routes/types';
  import useMenuTree from './use-menu-tree';

  export default defineComponent({
    emit: ['collapse'],
    props: {
      mode: {
        type: String,
        default: 'vertical',
      },
      isSubMenu: {
        type: Boolean,
        default: false,
      },
      isNeedCollapsed: {
        type: Boolean,
        default: true,
      },
    },
    setup(props) {
      const { t } = useI18n();
      const appStore = useAppStore();
      const router = useRouter();
      const route = useRoute();
      const { menuTree, subMenuTree } = useMenuTree();
      const collapsed = computed({
        get() {
          if (appStore.device === 'desktop' && props.isNeedCollapsed)
            return appStore.menuCollapse;
          return false;
        },
        set(value: boolean) {
          appStore.updateSettings({ menuCollapse: value });
        },
      });

      const topMenu = computed(() => appStore.topMenu);
      const openKeys = ref<string[]>([]);
      const selectedKey = ref<string[]>([]);

      const goto = (item: AppRouteRecordRaw) => {
        // Open external link
        if (item.meta?.link || regexUrl.test(item.path)) {
          openWindow((item.meta?.link || item.path) as string, {
            target: '_blank',
          });
          return;
        }
        // Eliminate external link side effects
        const { hideInMenu, activeMenu } = item.meta as RouteMeta;
        if (route.name === item.name && !hideInMenu && !activeMenu) {
          selectedKey.value = [item.name as string];
          return;
        }
        // Trigger router change
        router.push({
          name: item.name,
        });
      };

      // 处理子菜单点击事件
      const handleSubMenuClick = (item: AppRouteRecordRaw, event: Event) => {
        // 检查是否配置了重定向路由
        if (item.redirect) {
          // 阻止默认的子菜单展开/收起行为
          event.preventDefault();
          event.stopPropagation();

          // 执行路由跳转
          router.push(item.redirect);
        }
        // 如果没有重定向配置，保持默认的子菜单展开/收起行为
      };
      const findMenuOpenKeys = (target: string) => {
        const result: string[] = [];
        let isFind = false;
        const backtrack = (item: AppRouteRecordRaw, keys: string[]) => {
          if (item.name === target) {
            isFind = true;
            result.push(...keys);
            return;
          }
          if (item.children?.length) {
            item.children.forEach((el) => {
              backtrack(el, [...keys, el.name as string]);
            });
          }
        };
        menuTree.value.forEach((el: AppRouteRecordRaw) => {
          if (isFind) return; // Performance optimization
          backtrack(el, [el.name as string]);
        });
        return result;
      };
      listenerRouteChange((newRoute) => {
        const { requiresAuth, activeMenu, hideInMenu } = newRoute.meta;
        if (!hideInMenu || activeMenu) {
          const menuOpenKeys = findMenuOpenKeys(
            (activeMenu || newRoute.name) as string
          );

          const keySet = new Set([...menuOpenKeys, ...openKeys.value]);
          openKeys.value = [...keySet];

          selectedKey.value = [
            activeMenu || menuOpenKeys[menuOpenKeys.length - 1],
          ];
        }
      }, true);
      const setCollapse = (val: boolean) => {
        if (appStore.device === 'desktop')
          appStore.updateSettings({ menuCollapse: val });
      };

      const renderSubMenu = () => {
        function travel(_route: AppRouteRecordRaw[], nodes = []) {
          if (_route) {
            _route.forEach((element) => {
              let icon = element?.meta?.icon ? `<${element?.meta?.icon}/>` : ``;
              // 优先支持 iconFont 配置
              if (element?.meta?.iconFont) {
                icon = `<IconFont type="${element?.meta?.iconFont}"/>`;
              }
              const node =
                element?.children && element?.children.length !== 0 ? (
                  <a-sub-menu
                    key={element?.name}
                    popup-max-height={false}
                    v-slots={{
                      icon: () => h(compile(icon)),
                      title: () => h(compile(t(element?.meta?.locale || ''))),
                    }}
                    onClick={(event: Event) =>
                      handleSubMenuClick(element, event)
                    }
                  >
                    {travel(element?.children)}
                  </a-sub-menu>
                ) : (
                  <a-menu-item
                    key={element?.name}
                    v-slots={{ icon: () => h(compile(icon)) }}
                    onClick={() => goto(element)}
                  >
                    {element.meta?.link || regexUrl.test(element.path) ? (
                      <a
                        class="initA"
                        href={(element.meta?.link || element.path) as string}
                        target="_blank"
                        style="pointer-events: none;"
                      >
                        {
                          // 暂时无法解决点击事件和a标签的跳转同时触发的问题
                        }
                        {t(element?.meta?.locale || '')}
                      </a>
                    ) : (
                      <router-link
                        className="initA"
                        to={{ name: element.name }}
                        style="pointer-events: none;"
                      >
                        {t(element?.meta?.locale || '')}
                      </router-link>
                    )}
                  </a-menu-item>
                );
              nodes.push(node as never);
            });
          }
          return nodes;
        }
        if (props.isSubMenu) {
          return travel(subMenuTree.value);
        }
        return travel(menuTree.value);
      };
      return () => (
        <a-menu
          mode={props.mode}
          v-model:collapsed={collapsed.value}
          v-model:open-keys={openKeys.value}
          show-collapse-button={
            appStore.device !== 'mobile' && props.isNeedCollapsed
          }
          auto-open={false}
          selected-keys={selectedKey.value}
          auto-open-selected={true}
          level-indent={34}
          class="dmenu"
          popup-max-height={false}
          onCollapse={setCollapse}
        >
          {renderSubMenu()}
        </a-menu>
      );
    },
  });
</script>

<style lang="less" scoped>
  .dmenu {
    height: 100%;
    width: 100%;
    background-color: transparent;

    :deep(.arco-menu-inner) {
      .arco-menu-inline-header {
        display: flex;
        align-items: center;
        background-color: transparent;
        font-weight: 700;
      }
      .arco-icon {
        &:not(.arco-icon-down) {
          font-size: 18px;
        }
      }
      .arco-menu-pop-header,
      .arco-menu-item {
        background-color: transparent;
        font-weight: 700;
        margin-left: 0;
        border-radius: 8px;
        line-height: 36px;
        margin-bottom: 10px;
        &:hover {
          background-color: var(--color-bg-2);
          opacity: 0.6;
        }
      }
      .arco-menu-inline-content .arco-menu-item {
        .arco-menu-icon {
          display: none;
        }
      }
    }
    // 纵向菜单
    &.arco-menu-vertical {
      :deep(.arco-menu-inner) {
        .arco-menu-item {
          &.arco-menu-selected {
            background: var(--color-bg-2);
          }
        }
      }
    }
    // 横向菜单
    &.arco-menu-horizontal {
      padding: 12px 0 0;
      :deep(.arco-menu-inner) {
        overflow: hidden;
        padding: 0;
        height: auto;
        .arco-menu-icon {
          display: none;
        }
        .arco-menu-selected-label {
          bottom: 0px;
          left: 20px;
          right: 20px;
          border-radius: 3px;
          display: none;
        }
        .arco-menu-pop-header {
          padding: 0 15px;
        }
      }
    }
  }
  .arco-layout-sider-collapsed {
    .dmenu {
    }
  }
  // 亮色主题的导航
  .arco-menu-light.dmenu {
    :deep(.arco-menu-inner) {
      .arco-menu-pop-header:hover {
        background-color: var(--color-bg-1);
      }
    }
  }

  .initA {
    display: inline-block;
    width: 100%;
    height: 100%;
  }
</style>
