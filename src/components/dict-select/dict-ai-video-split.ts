export const colors = {
  primary: 'rgb(var(--arcoblue-6))', // 全局主色
  link: 'rgb(var(--arcoblue-6))', // 链接色
  success: 'rgb(var(--green-4))', // 成功色
  warning: 'rgb(var(--orange-6))', // 警告色
  info: 'rgb(var(--green-4))', // 信息
  error: 'rgb(var(--red-6))', // 错误色
  disable: 'var(--color-neutral-4)', // 禁用
};

export const VIDEO_SPLIT_STATUS = [
  { label: '排队中', value: 'wait', color: '' },
  { label: '进行中', value: 'processing', color: colors.primary },
  { label: '已完成', value: 'done', color: colors.success },
  { label: '失败', value: 'failed', color: colors.error },
];
