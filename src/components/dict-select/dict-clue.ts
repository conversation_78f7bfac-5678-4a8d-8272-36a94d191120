// 线索来源类型
import { colors } from '@/components/dict-select/dict-common';

export const clueSuorceM = [
  { label: '爬虫关键词配置', value: '爬虫关键词配置' },
  { label: '商业化抖音号配置', value: '商业化抖音号配置' },
  { label: '直播间监控', value: '直播间监控' },
  { label: '客服新增', value: '客服新增' },
  { label: '直播间私信', value: '直播间私信' },
  { label: '短视频私信', value: '短视频私信' },
  // { label: '接粉专员新增', value: '接粉专员新增' },
];

// 线索分配规则
export const clueAssignM = [{ label: '线索补齐分配', value: '线索补齐分配' }];

// 销售线索状态
export const clueStatusM = [
  { label: '待分配', value: '待分配', color: '' },
  { label: '已分配', value: '已分配', color: colors.primary_new },
  {
    label: '流转二级线索池',
    value: '流转二级线索池',
    color: colors.success_new,
  },
  { label: '作废', value: '作废', color: colors.error_new },
];

// 二级线索状态
export const clueTwoStatusM = [
  { label: '待分配', value: '待分配' },
  { label: '已分配', value: '已分配', color: colors.success_new },
  { label: '已受理', value: '已受理', color: colors.primary_new },
  { label: '已添加微信', value: '已添加微信', color: colors.primary_new },
  {
    label: '已介绍产品及公司',
    value: '已介绍产品及公司',
    color: colors.primary_new,
  },
  // { label: '已询价待支付', value: '已询价待支付', color: colors.success },
  { label: '已询价', value: '已询价', color: colors.primary_new },
  { label: '作废', value: '作废', color: colors.error_new },
];

// 二级线索状态
export const msgStatusM = [
  { label: '是', value: '是' },
  { label: '否', value: '否' },
  { label: '发送中', value: '发送中' },
  { label: '发送失败', value: '发送失败' },
];

// AI意向等级
export const intentionalityIntentM = [
  { label: '有意向', value: 'positive' },
  { label: '可能有意向', value: 'potential_positive' },
  { label: '可能无意向', value: 'potential_negative' },
  { label: '无意向', value: 'negative' },
];

// 回复模板宏
export const replayTemplateM = [
  { label: '用户昵称', value: '{用户昵称}' },
  { label: '关键词', value: '{关键词}' },
];

export const clueTypeM = [
  { label: '全部', value: 0 },
  { label: '待受理', value: 8 },
  { label: '已受理', value: 1 },
  // { label: '待添加微信', value: 1, tooltip: '已受理且未添加微信的线索' },
  // {
  //  label: '待介绍产品及公司',
  //  value: 2,
  //  tooltip: '状态为已添加微信的线索',
  // },
  // { label: '待询价', value: 3, tooltip: '状态为已介绍产品及公司的线索' },
  // { label: '已询价待支付', value: 5, tooltip: '已询价待支付' }, // 已废弃
  { label: '已询价并转单', value: 4, tooltip: '状态为已询价的线索' },
  // {
  //  label: '出行中/已核销',
  //  value: 6,
  //  tooltip: '二级线索产生的订单，客户已出行或者已核销',
  // },
  { label: '已作废', value: 7, tooltip: '' },
];

// 回复模板状态
export const replayTemplateStateM = [
  { label: '已挑选可用', value: 1 },
  { label: '新润色待挑选', value: 2 },
];

// 回复模板类型
export const replayTemplateTypeM = [
  { label: '爬虫关键词', value: 'keyword' },
  { label: '抖音号', value: 'tk_account' },
  { label: '直播间', value: 'live_room' },
];

export const clueConfigStateM = [
  { label: '有效', value: 1 },
  { label: '无效', value: -1 },
  { label: '全部', value: '' },
];

// 订单支付方式
export const depositPayTypeM = [
  { label: '小程序支付', value: 1 },
  { label: '对公账户', value: -1 },
];

// 1抖音来客2小程序支付3对公账户
export const depositPayTypeM2 = [
  { label: '抖音来客', value: 1, hide: true },
  { label: '小程序支付', value: 2 },
  { label: '对公账户', value: 3 },
];

// 线索类型
export const clueTypeListM = [
  { label: '公海线索', value: '公海线索' },
  { label: '内容号线索', value: '内容号线索' },
  { label: '店铺线索', value: '店铺线索' },
  { label: '直播账号线索', value: '直播账号线索' },
  { label: '其他线索', value: '其他线索' },
];
// 线索类型 公海
export const clueSeasTypeM = [
  { label: '爬虫关键词', value: '爬虫关键词' },
  { label: '直播间监控', value: '直播间监控' },
  { label: '商业化账号', value: '商业化账号' },
];

// 线索细分类型
export const clueDetailTypeM = [{ label: '客户转介绍', value: '客户转介绍' }];

// 是否投流
export const isFlowM = [
  { label: '是', value: 1 },
  { label: '否', value: 2 },
];
