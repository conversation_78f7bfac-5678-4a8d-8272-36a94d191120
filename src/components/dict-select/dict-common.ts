export const colors = {
  primary: 'rgb(var(--arcoblue-6))', // 全局主色
  link: 'rgb(var(--arcoblue-6))', // 链接色
  success: 'rgb(var(--green-4))', // 成功色
  warning: 'rgb(var(--orange-6))', // 警告色
  info: 'rgb(var(--green-4))', // 信息
  error: 'rgb(var(--red-6))', // 错误色
  disable: 'var(--color-neutral-4)', // 禁用

  // 按钮颜色
  info_new: 'green', // 信息
  primary_new: 'arcoblue',
  success_new: 'green',
  error_new: 'red',
  warning_new: 'orange',
};

// 通用
export const switchM = [
  { value: 'off', label: '不启用' },
  { value: 'on', label: '启用' },
];

// 通用
export const stateM = [
  { value: -1, label: '停用' },
  { value: 1, label: '正常' },
];

// 直播间类型
export const liveRoomTypeM = [
  { value: 'full', label: '全职直播间' },
  { value: 'inside', label: '内部KOC直播间' },
  { value: 'without', label: '外部KOC直播间' },
];
