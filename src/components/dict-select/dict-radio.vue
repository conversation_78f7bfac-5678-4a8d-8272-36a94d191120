<template>
  <a-radio-group
    v-model="curVal"
    :type="type"
    size="large"
    :disabled="disabled"
    @change="handleChange"
  >
    <a-radio
      v-for="item in showList"
      :key="item[valueKey]"
      :value="item[valueKey]"
      :disabled="
        disabledValues.includes(item[valueKey]) ||
        Boolean(
          enabledValues?.length && !enabledValues.includes(item[valueKey])
        )
      "
    >
      {{ getLabel ? getLabel(item) : getItemLabel(item) }}
      <div v-if="item.tips" style="white-space: nowrap">{{ item.tips }}</div>
      <a-tooltip v-if="item.tooltip">
        <icon-question-circle />
        <template #content>
          <div style="white-space: pre-line">
            {{ item.tooltip }}
          </div>
        </template>
      </a-tooltip>
    </a-radio>
  </a-radio-group>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';

  const props = defineProps({
    modelValue: {
      type: [String, Number],
      default: () => '',
    },
    valueKey: {
      type: [String, Number],
      default: () => 'value',
    },
    labelKey: {
      type: [String, Number],
      default: () => 'label',
    },
    labelsKey: {
      type: [String, Number],
      default: () => 'labels',
    },
    dataList: {
      type: Array,
      default: () => [],
      required: true,
    },
    typeKey: {
      type: String,
      default: '',
    },
    // 需要禁用的值
    disabledValues: {
      type: Array,
      default: () => [],
    },
    // 需要启用的值
    enabledValues: {
      type: Array,
      default: () => [],
    },
    // 列表范围
    dataScope: {
      type: Array,
      default: null,
    },
    getLabel: {
      type: Function,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: () => false,
    },
    type: {
      type: String,
      default: 'button',
    },
  });
  const emit = defineEmits(['input', 'change', 'update:modelValue']);
  const curVal = ref<string | number>('');

  const showList = computed(() =>
    props.dataScope?.length
      ? props.dataList?.filter((item: any) =>
          props.dataScope?.includes(item[props.valueKey])
        )
      : props.dataList.filter((item: any) => !item.hide) || []
  );
  const getItemLabel = (item: any) => {
    if (props.typeKey && item[props.labelsKey]?.[props.typeKey]) {
      return item[props.labelsKey][props.typeKey];
    }
    return item[props.labelKey];
  };

  watch(
    () => props.modelValue,
    (newVal) => {
      curVal.value = newVal;
    },
    {
      immediate: true,
    }
  );
  const handleChange = (val: any) => {
    emit('update:modelValue', val);
    emit('change', val);
  };
</script>

<style scoped></style>
