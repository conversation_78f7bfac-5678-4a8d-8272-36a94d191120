<template>
  <BaseSelect
    v-model="curVal"
    style="width: 100%"
    class="map-select"
    v-bind="props"
    @change="handleChange"
  ></BaseSelect>
</template>

<script lang="ts" setup>
  import { ref, watch, onBeforeMount } from 'vue';
  import BaseSelect from '../select/base-select.vue';

  const props = defineProps({
    placeholder: {
      type: String,
      default: '请选择',
    },
    modelValue: {
      type: [String, Number, Array],
      default: () => null,
    },
    // id的键值
    valueKey: {
      type: [String, Number],
      default: () => 'value',
    },
    labelKey: {
      type: [String, Number],
      default: () => 'label',
    },
    multiple: {
      type: [Boolean],
      default: () => false,
    },
    dataList: {
      type: Array,
      default: () => [],
      required: true,
    },
    allowClear: {
      type: Boolean,
      default: true,
    },
  });
  const emit = defineEmits(['input', 'change', 'update:modelValue']);
  const curVal = ref<any>(null);
  // const dataList = ref<any>([]);
  watch(
    () => props.modelValue,
    (newVal) => {
      curVal.value = newVal;
    },
    {
      immediate: true,
    }
  );
  function handleChange() {
    // @ts-ignore
    // eslint-disable-next-line
    emit('update:modelValue', arguments[0]);
    // @ts-ignore
    // eslint-disable-next-line
    emit('change', ...arguments);
  }
</script>

<style lang="less" scoped></style>
