import { colors } from './dict-common';

export const contactWayListM = [
  { label: '抖音', value: '抖音' },
  { label: '视频号', value: '视频号' },
  { label: '小红书', value: '小红书' },
  { label: '其他', value: '其他' },
  // { label: '线下', value: '线下' },
];

export const priceStatusListM = [
  { label: '待跟进', value: '待跟进' },
  { label: '已支付订金', value: '已支付订金' },
  { label: '已作废', value: '已作废' },
];

export const orderStatusListM = [
  {
    label: '已支付订金（1级）',
    value: '已支付订金',
    color: colors.primary_new,
  },
  { label: '已受理', value: '已受理', color: colors.primary_new },
  {
    label: '待计调审核',
    value: '待计调审核',
    color: colors.success_new,
  },
  {
    label: '已发确认件（3级）',
    value: '已发确认件',
    color: colors.warning_new,
  },
  { label: '已出行', value: '已出行', color: colors.info_new },
  { label: '已核销', value: '已核销', color: colors.success },
  { label: '已完成', value: '已完成', color: colors.success_new },
  // { label: '已回访', value: '已回访', color: colors.success_new },
  { label: '已退款', value: '已退款', color: colors.warning_new },
  // { label: '已反馈', value: '已反馈', color: colors.info_new },
  { label: '已作废', value: '已作废', color: colors.error_new },
  { label: '取消预约', value: '取消预约', color: colors.error_new },
  { label: '终止出行', value: '终止出行', color: colors.error_new },
];

export const customerServiceStatusListM = [
  { label: '在线', value: '在线' },
  { label: '离线', value: '离线' },
  { label: '离职', value: '离职' },
];

export const trackingTypeListM = [
  { label: '订单', value: '订单' },
  { label: '询价单', value: '询价单' },
];

export const travelTypeListM = [
  { label: '亲子', value: '亲子' },
  { label: '团建', value: '团建' },
  { label: '朋友', value: '朋友' },
];

export const yesOrNo = [
  { label: '是', value: '是' },
  { label: '否', value: '否' },
];

export const yesOrNo2 = [
  { label: '是', value: 1 },
  { label: '否', value: -1 },
];
export const yesOrNo3 = [
  { label: '是', value: 1 },
  { label: '否', value: 2 },
];

export const yesOrNoOrNull = [
  { label: '是', value: '是' },
  { label: '否', value: '否' },
  { label: '未选择', value: '未选择' },
];

export const payTypeM = [
  { label: '线上', value: '线上' },
  { label: '线下', value: '线下' },
];

export const orderTypeM = [
  { label: '全部', value: '' },
  {
    label: '待受理',
    value: 7,
  },
  {
    label: '待联系',
    value: 1,
    tooltip:
      '符合以下任意一个条件\n' +
      '条件1：已受理且未添加微信的订单\n' +
      '条件2：已添加微信、已退款且未标注为退款有效客户',
  },
  {
    label: '跟进中',
    value: 2,
    tooltip:
      '符合以下任意一个条件：\n条件1：已添加微信，状态为已受理或者待计调审核，待出确认件的订单。\n条件2：已添加微信、订单状态为已退款且标注为退款有效客户的订单',
  },
  { label: '待出行', value: 3, tooltip: '状态为已发确认件' },
  { label: '出行中/已核销', value: 4, tooltip: '状态为已出行或者已核销' },
  { label: '挽单池', value: 5 },
  { label: '已作废', value: 6, tooltip: '已作废的订单' },
];

export const orderIntentionalityM = [
  { label: 'A：确定出行时间和人数', value: 'A' },
  { label: 'B：确定出行时间或人数', value: 'B' },
  { label: 'C：出行时间和人数都不确定', value: 'C' },
  { label: 'D：无意向，且非空号', value: 'D' },
  { label: 'E：无有效沟通', value: 'E' },
];

export const cancelReasonM = [
  { label: '手机号为空号', value: '手机号为空号' },
  { label: '手机停机', value: '手机停机' },
  { label: '其他', value: '其他' },
  { label: '客户多次秒挂断', value: '客户多次秒挂断' },
  { label: '客户多次明确表示未下单', value: '客户多次明确表示未下单' },
];

// 订单类型
export const orderTypeListM = [
  { label: '公海线索订单', value: '公海线索订单' },
  { label: '内容号线索订单', value: '内容号线索订单' },
  { label: '店铺线索订单', value: '店铺线索订单' },
  { label: '直播账号线索订单', value: '直播账号线索订单' },
  { label: '其他线索订单', value: '其他线索订单' },
  { label: '全职直播间订单', value: '全职直播间订单' },
  { label: '内部KOC直播间订单', value: '内部KOC直播间订单' },
  { label: '外部KOC直播间订单', value: '外部KOC直播间订单' },
];

// 客资类型
export const customerEquityTypeM = [
  { label: '线索订单', value: '线索订单' },
  { label: '直播间订单', value: '直播间订单' },
];

// 客资类型
export const redemptionTypeM = [
  { label: '单量核销率', value: '单量核销率' },
  { label: 'GMV核销率', value: 'GMV核销率' },
];

// 客资类型
export const orderCancelTypeM = [
  { label: '作废', value: 'discard' },
  { label: '流入挽单池', value: 'circulation' },
];

export const ticketingPlatformM = [
  { label: '去哪儿机票', value: '去哪儿机票' },
  { label: '飞猪阿里旅游', value: '飞猪阿里旅游' },
  { label: '航空公司官网', value: '航空公司官网' },
  { label: '携程机票', value: '携程机票' },
  { label: '通怡票务', value: '通怡票务' },
  { label: '熊浩票务', value: '熊浩票务' },
  { label: '乐乐票务', value: '乐乐票务' },
  { label: '万晟票务', value: '万晟票务' },
  { label: '西米票务', value: '西米票务' },
  { label: '去旅行票务', value: '去旅行票务' },
  { label: '华立火车票', value: '华立火车票' },
  { label: '阳鑫票务', value: '阳鑫票务' },
];
