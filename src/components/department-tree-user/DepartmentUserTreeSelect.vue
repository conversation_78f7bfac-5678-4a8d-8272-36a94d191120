<template>
  <div class="department-user-tree-select">
    <a-popover
      v-model:popup-visible="popoverVisible"
      trigger="click"
      position="bottom"
      :arrow-style="{ display: 'none' }"
      class="no-padding-popover"
      content-class="department-tree-popover"
    >
      <template #content>
        <div class="tree-select-content">
          <!-- 搜索框 -->
          <div class="search-wrapper">
            <a-input
              v-model="searchKeyword"
              placeholder="搜索部门或人员"
              allow-clear
              size="small"
            >
              <template #prefix>
                <icon-search />
              </template>
            </a-input>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="loading-wrapper">
            <a-spin />
            <span class="loading-text">加载中...</span>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="error-wrapper">
            <icon-exclamation-circle-fill />
            <span class="error-text">{{ error }}</span>
            <a-button size="mini" type="text" @click="fetchData">
              重试
            </a-button>
          </div>

          <!-- 树形结构或搜索结果 -->
          <div v-else class="tree-wrapper">
            <!-- 搜索结果 -->
            <div v-if="searchKeyword" class="search-results">
              <div v-if="!filteredData.length" class="no-data">
                暂无匹配结果
              </div>
              <div v-else class="search-list">
                <div
                  v-for="item in filteredData"
                  :key="`${item.type}-${item.id}`"
                  class="search-item"
                  :class="{ selected: isSelected(item) }"
                  @click="toggleSelection(item)"
                >
                  <a-checkbox
                    :model-value="isSelected(item)"
                    @click.stop
                    @change="toggleSelection(item)"
                  />
                  <span class="item-icon">
                    <icon-user v-if="!item.is_department" />
                    <icon-folder v-else />
                  </span>
                  <span class="item-name">{{ item.name }}</span>
                  <span class="item-type">
                    {{ item.is_department ? '部门' : '人员' }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 树形结构 -->
            <div v-else class="tree-container">
              <a-tree
                v-if="treeData.length"
                :data="treeData"
                :checkable="true"
                :checked-keys="checkedKeys"
                :half-checked-keys="halfCheckedKeys"
                :field-names="fieldNames"
                :default-expand-all="true"
                size="small"
                @check="handleTreeCheck"
              >
                <template #title="nodeData">
                  <div
                    class="tree-node-title"
                    @click="handleNodeTitleClick(nodeData)"
                  >
                    <span class="node-icon">
                      <icon-user v-if="!nodeData.is_department" />
                      <icon-folder v-else />
                    </span>
                    <span class="node-name">{{ nodeData.name }}</span>
                  </div>
                </template>
              </a-tree>
              <div v-else class="no-data"> 暂无数据 </div>
            </div>
          </div>

          <!-- 底部操作栏 -->
          <div class="footer-actions">
            <div class="selected-count">
              已选择 {{ selectedItems.length }} 项
            </div>
            <div class="action-buttons">
              <a-button size="mini" type="text" @click="clearAll">
                清空
              </a-button>
              <a-button size="mini" type="primary" @click="closePopover">
                关闭
              </a-button>
            </div>
          </div>
        </div>
      </template>

      <!-- 触发器 -->
      <div
        class="select-trigger"
        :class="{ disabled, focused: popoverVisible }"
      >
        <div class="trigger-content">
          <div v-if="!selectedItems.length" class="placeholder">
            {{ placeholder }}
          </div>
          <div v-else class="selected-display">
            <div class="selected-tags">
              <a-tag
                v-for="item in displayItems"
                :key="`${item.type}-${item.value}`"
                size="small"
                closable
                @close="removeItem(item)"
              >
                <span class="tag-icon">
                  <icon-user v-if="item.type === 'user'" />
                  <icon-folder v-else />
                </span>
                {{ item.label }}
              </a-tag>
              <span
                v-if="selectedItems.length > maxDisplayCount"
                class="more-count"
              >
                +{{ selectedItems.length - maxDisplayCount }}
              </span>
            </div>
          </div>
        </div>
        <div class="trigger-suffix">
          <icon-loading v-if="loading" spin />
          <icon-close
            v-else-if="selectedItems.length && !disabled"
            class="clear-icon"
            @click.stop="clearAll"
          />
          <icon-down class="arrow-icon" :class="{ expanded: popoverVisible }" />
        </div>
      </div>
    </a-popover>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, watch, onMounted, nextTick } from 'vue';
  import request from '@/api/request';
  import {
    IconSearch,
    IconUser,
    IconFolder,
    IconDown,
    IconClose,
    IconLoading,
    IconExclamationCircleFill,
  } from '@arco-design/web-vue/es/icon';

  // 接口数据类型定义
  interface TreeNode {
    id: number;
    parent_id?: number;
    name: string;
    is_department: boolean;
    children?: TreeNode[];
  }

  // 带唯一标识符的树节点（用于树组件）
  interface TreeNodeWithUniqueKey extends TreeNode {
    uniqueKey: string;
    children?: TreeNodeWithUniqueKey[];
  }

  interface ApiResponse {
    code: number;
    msg: string;
    data: TreeNode[];
    retCode: number;
  }

  // 选择项类型定义
  interface SelectedItem {
    value: number;
    type: 'department' | 'user';
    label: string;
  }

  // 扁平化数据类型
  interface FlatItem extends TreeNode {
    type: 'department' | 'user';
  }

  // 查询参数类型定义
  interface SendParams {
    [key: string]: any;
  }

  // Props 定义
  interface Props {
    modelValue?: SelectedItem[];
    placeholder?: string;
    disabled?: boolean;
    maxDisplayCount?: number;
    sendParams?: SendParams;
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => [],
    placeholder: '请选择部门或人员',
    disabled: false,
    maxDisplayCount: 3,
    sendParams: () => ({}),
  });

  // Emits 定义
  const emit = defineEmits<{
    'update:modelValue': [value: SelectedItem[]];
    'change': [value: SelectedItem[]];
  }>();

  // 响应式数据
  const popoverVisible = ref(false);
  const loading = ref(false);
  const error = ref('');
  const searchKeyword = ref('');
  const rawData = ref<TreeNode[]>([]);
  const selectedItems = ref<SelectedItem[]>([]);
  const halfCheckedItems = ref<SelectedItem[]>([]); // 半选状态的项目

  // 计算属性
  // 为树组件添加唯一标识符
  const treeData = computed(() => {
    const addUniqueKey = (nodes: TreeNode[]): TreeNodeWithUniqueKey[] => {
      return nodes.map((node) => ({
        ...node,
        uniqueKey: `${node.is_department ? 'department' : 'user'}-${node.id}`,
        children: node.children ? addUniqueKey(node.children) : undefined,
      }));
    };
    return addUniqueKey(rawData.value);
  });

  const fieldNames = computed(() => ({
    key: 'uniqueKey',
    title: 'name',
    children: 'children',
  }));

  // 扁平化所有数据用于搜索
  const flatData = computed<FlatItem[]>(() => {
    const result: FlatItem[] = [];

    const flatten = (nodes: TreeNode[]) => {
      nodes.forEach((node) => {
        result.push({
          ...node,
          type: node.is_department ? 'department' : 'user',
        });
        if (node.children?.length) {
          flatten(node.children);
        }
      });
    };

    flatten(rawData.value);
    return result;
  });

  // 搜索过滤结果
  const filteredData = computed(() => {
    if (!searchKeyword.value) return [];

    const keyword = searchKeyword.value.toLowerCase();
    return flatData.value.filter((item) =>
      item.name.toLowerCase().includes(keyword)
    );
  });

  // 已选中的键值（用于树组件）
  const checkedKeys = computed(() => {
    return selectedItems.value.map((item) => `${item.type}-${item.value}`);
  });

  // 半选状态的键值（用于树组件）
  const halfCheckedKeys = computed(() => {
    return halfCheckedItems.value.map((item) => `${item.type}-${item.value}`);
  });

  // 显示的标签项（限制显示数量）
  const displayItems = computed(() => {
    return selectedItems.value.slice(0, props.maxDisplayCount);
  });

  // 方法定义
  // 获取数据
  const fetchData = async () => {
    loading.value = true;
    error.value = '';

    try {
      // 构建请求参数，合并 sendParams
      const requestParams = {
        ...props.sendParams,
      };

      const response: ApiResponse = await request(
        '/api/user/userTreeList',
        requestParams
      );
      rawData.value = response.data || [];
    } catch (err: any) {
      error.value = err.message || '数据加载失败';
      console.error('Failed to fetch department user tree data:', err);
    } finally {
      loading.value = false;
    }
  };

  // 检查是否已选中
  const isSelected = (item: FlatItem): boolean => {
    return selectedItems.value.some(
      (selected) => selected.value === item.id && selected.type === item.type
    );
  };

  // 触发选择变更事件
  const emitSelectionChange = () => {
    emit('update:modelValue', [...selectedItems.value]);
    emit('change', [...selectedItems.value]);
  };

  // 查找节点的所有子节点（递归）
  const findAllChildren = (nodeId: number, nodes: TreeNode[]): TreeNode[] => {
    const children: TreeNode[] = [];

    const findChildren = (currentNodes: TreeNode[]) => {
      currentNodes.forEach((node) => {
        if (node.parent_id === nodeId) {
          children.push(node);
          if (node.children?.length) {
            findChildren(node.children);
          }
        } else if (node.children?.length) {
          findChildren(node.children);
        }
      });
    };

    findChildren(nodes);
    return children;
  };

  // 查找节点的所有父节点（递归）
  const findAllParents = (nodeId: number, nodes: TreeNode[]): TreeNode[] => {
    const parents: TreeNode[] = [];

    const findParent = (currentNodeId: number) => {
      const flatNodes = flatData.value;
      const currentNode = flatNodes.find((n) => n.id === currentNodeId);
      if (currentNode?.parent_id) {
        const parent = flatNodes.find((n) => n.id === currentNode.parent_id);
        if (parent) {
          parents.push(parent);
          findParent(parent.id);
        }
      }
    };

    findParent(nodeId);
    return parents;
  };

  // 辅助方法：添加到选择列表
  const addToSelection = (item: FlatItem) => {
    if (!isSelected(item)) {
      selectedItems.value.push({
        value: item.id,
        type: item.type,
        label: item.name,
      });
    }
  };

  // 辅助方法：从选择列表移除
  const removeFromSelection = (item: SelectedItem | FlatItem) => {
    const index = selectedItems.value.findIndex(
      (selected) => selected.value === item.id && selected.type === item.type
    );
    if (index > -1) {
      selectedItems.value.splice(index, 1);
    }
  };

  // 辅助方法：添加到半选列表
  const addToHalfChecked = (item: SelectedItem) => {
    const exists = halfCheckedItems.value.some(
      (halfChecked) =>
        halfChecked.value === item.value && halfChecked.type === item.type
    );
    if (!exists) {
      halfCheckedItems.value.push(item);
    }
  };

  // 辅助方法：从半选列表移除
  const removeFromHalfChecked = (item: SelectedItem | FlatItem) => {
    const index = halfCheckedItems.value.findIndex(
      (halfChecked) =>
        halfChecked.value === item.id && halfChecked.type === item.type
    );
    if (index > -1) {
      halfCheckedItems.value.splice(index, 1);
    }
  };

  // 更新半选状态
  const updateHalfCheckedStates = () => {
    // 清空当前半选状态
    halfCheckedItems.value = [];

    // 遍历所有部门，检查是否应该设为半选状态
    const departments = flatData.value.filter((item) => item.is_department);
    departments.forEach((dept) => {
      const children = findAllChildren(dept.id, rawData.value);
      if (children.length > 0) {
        const selectedChildrenCount = children.filter((child) => {
          const childType = child.is_department ? 'department' : 'user';
          return selectedItems.value.some(
            (selected) =>
              selected.value === child.id && selected.type === childType
          );
        }).length;

        // 如果有部分子节点被选中，但不是全部，则设为半选状态
        if (
          selectedChildrenCount > 0 &&
          selectedChildrenCount < children.length
        ) {
          const isDeptSelected = selectedItems.value.some(
            (selected) =>
              selected.value === dept.id && selected.type === 'department'
          );
          if (!isDeptSelected) {
            addToHalfChecked({
              value: dept.id,
              type: 'department',
              label: dept.name,
            });
          }
        }
      }
    });
  };

  // 检查是否有父节点被选中
  const checkHasSelectedParent = (item: FlatItem): boolean => {
    const allParents = findAllParents(item.id, rawData.value);
    return allParents.some((parent) => {
      const parentType = parent.is_department ? 'department' : 'user';
      return selectedItems.value.some(
        (selected) =>
          selected.value === parent.id && selected.type === parentType
      );
    });
  };

  // 处理当父节点已选中时点击子节点的情况
  const handleChildClickWhenParentSelected = (item: FlatItem) => {
    // 1. 找到所有被选中的父节点
    const allParents = findAllParents(item.id, rawData.value);
    const selectedParents = allParents.filter((parent) => {
      const parentType = parent.is_department ? 'department' : 'user';
      return selectedItems.value.some(
        (selected) =>
          selected.value === parent.id && selected.type === parentType
      );
    });

    // 2. 移除所有被选中的父节点
    selectedParents.forEach((parent) => {
      const parentItem = {
        id: parent.id,
        type: parent.is_department ? 'department' : 'user',
        label: parent.name,
      } as SelectedItem;
      removeFromSelection(parentItem);
    });

    // 3. 现在父节点已被移除，可以正常执行子节点的选择逻辑
    // 这里不需要调用 handleDepartmentSelection 或 handleUserSelection
    // 因为那些方法会再次处理父子关系，而我们已经处理过了
    // 直接根据节点类型执行简单的选择逻辑

    if (item.is_department) {
      // 选择部门：直接选中该部门
      addToSelection(item);
    } else {
      // 选择用户：根据特殊逻辑，选中同级的其他所有节点，但不选中当前用户
      const directParent = flatData.value.find(
        (parentItem) =>
          parentItem.id === item.parent_id && parentItem.is_department
      );

      if (directParent) {
        const allSiblings = findAllChildren(directParent.id, rawData.value);
        allSiblings.forEach((sibling) => {
          // 选中除当前用户外的所有兄弟节点
          if (
            sibling.id !== item.id ||
            sibling.is_department !== item.is_department
          ) {
            const siblingItem = {
              id: sibling.id,
              type: sibling.is_department ? 'department' : 'user',
              label: sibling.name,
            } as FlatItem;
            addToSelection(siblingItem);
          }
        });
      }
    }

    // 4. 重新计算半选状态
    updateHalfCheckedStates();
  };

  // 处理部门选择
  const handleDepartmentSelection = (department: FlatItem) => {
    // 1. 移除所有子节点的选择
    const allChildren = findAllChildren(department.id, rawData.value);
    allChildren.forEach((child) => {
      const childItem = {
        id: child.id,
        type: child.is_department ? 'department' : 'user',
        label: child.name,
      } as SelectedItem;
      removeFromSelection(childItem);
    });

    // 2. 移除所有父节点的选择
    const allParents = findAllParents(department.id, rawData.value);
    allParents.forEach((parent) => {
      const parentItem = {
        id: parent.id,
        type: parent.is_department ? 'department' : 'user',
        label: parent.name,
      } as SelectedItem;
      removeFromSelection(parentItem);
    });

    // 3. 添加当前部门的选择
    addToSelection(department);

    // 4. 重新计算半选状态
    updateHalfCheckedStates();
  };

  // 处理人员选择（实现特殊逻辑）
  const handleUserSelection = (user: FlatItem) => {
    // 找到用户的直接父部门
    const directParent = flatData.value.find(
      (item) => item.id === user.parent_id && item.is_department
    );

    if (directParent) {
      // 1. 移除父部门的完全选中状态
      const parentItem = {
        id: directParent.id,
        type: 'department',
        label: directParent.name,
      } as SelectedItem;
      removeFromSelection(parentItem);

      // 2. 选中该部门下的所有其他子项（除了当前点击的用户）
      const allSiblings = findAllChildren(directParent.id, rawData.value);
      allSiblings.forEach((sibling) => {
        if (
          sibling.id !== user.id ||
          sibling.is_department !== user.is_department
        ) {
          const siblingItem = {
            id: sibling.id,
            type: sibling.is_department ? 'department' : 'user',
            label: sibling.name,
          } as FlatItem;
          addToSelection(siblingItem);
        }
      });
    }

    // 3. 移除所有祖先节点的选择
    const allParents = findAllParents(user.id, rawData.value);
    allParents.forEach((parent) => {
      const parentItem = {
        id: parent.id,
        type: parent.is_department ? 'department' : 'user',
        label: parent.name,
      } as SelectedItem;
      removeFromSelection(parentItem);
    });

    // 4. 确保当前用户不被选中（这是特殊逻辑的要求）
    removeFromSelection(user);

    // 5. 重新计算半选状态
    updateHalfCheckedStates();
  };

  // 简化的选择切换逻辑
  const toggleSelectionWithMutualExclusion = (item: FlatItem) => {
    const isCurrentlySelected = isSelected(item);

    if (isCurrentlySelected) {
      // 如果当前已选中，直接取消选择
      removeFromSelection(item);
    } else {
      // 如果当前未选中，直接选中
      addToSelection(item);

      // 如果选中的是部门，取消其所有子节点的选择（父子互斥）
      if (item.is_department) {
        const allChildren = findAllChildren(item.id, rawData.value);
        allChildren.forEach((child) => {
          const childItem = {
            id: child.id,
            type: child.is_department ? 'department' : 'user',
            label: child.name,
          } as SelectedItem;
          removeFromSelection(childItem);
        });
      }

      // 如果选中的是子节点，取消其所有父节点的选择（父子互斥）
      const allParents = findAllParents(item.id, rawData.value);
      allParents.forEach((parent) => {
        const parentItem = {
          id: parent.id,
          type: parent.is_department ? 'department' : 'user',
          label: parent.name,
        } as SelectedItem;
        removeFromSelection(parentItem);
      });
    }

    // 重新计算半选状态
    updateHalfCheckedStates();

    // 立即触发选择变更事件
    emitSelectionChange();
  };

  // 处理节点标题点击
  const handleNodeTitleClick = (nodeData: TreeNodeWithUniqueKey) => {
    const item: FlatItem = {
      id: nodeData.id,
      name: nodeData.name,
      is_department: nodeData.is_department,
      type: nodeData.is_department ? 'department' : 'user',
      parent_id: nodeData.parent_id,
      children: nodeData.children,
    };

    toggleSelectionWithMutualExclusion(item);
  };

  // 切换选择状态（不带互斥逻辑，用于搜索结果）
  const toggleSelection = (item: FlatItem) => {
    const index = selectedItems.value.findIndex(
      (selected) => selected.value === item.id && selected.type === item.type
    );

    if (index > -1) {
      selectedItems.value.splice(index, 1);
    } else {
      selectedItems.value.push({
        value: item.id,
        type: item.type,
        label: item.name,
      });
    }

    // 立即触发选择变更事件
    emitSelectionChange();
  };

  // 处理树组件的选择
  const handleTreeCheck = (keys: string[], event: any) => {
    const { checked, node } = event;

    const item: FlatItem = {
      id: node.id,
      name: node.name,
      is_department: node.is_department,
      type: node.is_department ? 'department' : 'user',
      parent_id: node.parent_id,
      children: node.children,
    };

    // 使用互斥逻辑处理选择
    if (checked) {
      // 如果是选中操作，使用互斥逻辑
      toggleSelectionWithMutualExclusion(item);
    } else {
      // 如果是取消选中操作，直接移除
      const index = selectedItems.value.findIndex(
        (selected) => selected.value === item.id && selected.type === item.type
      );
      if (index > -1) {
        selectedItems.value.splice(index, 1);
        // 重新计算半选状态
        updateHalfCheckedStates();
        // 立即触发选择变更事件
        emitSelectionChange();
      }
    }
  };

  // 移除单个选择项
  const removeItem = (item: SelectedItem) => {
    const index = selectedItems.value.findIndex(
      (selected) => selected.value === item.value && selected.type === item.type
    );
    if (index > -1) {
      selectedItems.value.splice(index, 1);
      // 重新计算半选状态
      updateHalfCheckedStates();
      // 立即触发选择变更事件
      emitSelectionChange();
    }
  };

  // 清空所有选择
  const clearAll = () => {
    selectedItems.value = [];
    // 重新计算半选状态
    updateHalfCheckedStates();
    // 立即触发选择变更事件
    emitSelectionChange();
  };

  // 关闭弹窗
  const closePopover = () => {
    popoverVisible.value = false;
  };

  // 监听 modelValue 变化
  watch(
    () => props.modelValue,
    (newValue) => {
      selectedItems.value = [...(newValue || [])];
    },
    { immediate: true, deep: true }
  );

  // 监听弹窗显示状态
  watch(popoverVisible, (visible) => {
    if (visible && !rawData.value.length && !loading.value) {
      fetchData();
    }
  });

  // 监听 sendParams 变化，重新获取数据
  watch(
    () => props.sendParams,
    () => {
      // 当 sendParams 发生变化时，清空现有数据并重新获取
      if (rawData.value.length || loading.value) {
        rawData.value = [];
        fetchData();
      }
    },
    { deep: true }
  );

  // 组件挂载时获取数据
  onMounted(() => {
    if (props.modelValue?.length) {
      selectedItems.value = [...props.modelValue];
    }
  });
</script>

<style lang="less" scoped>
  .department-user-tree-select {
    width: 100%;

    .select-trigger {
      display: flex;
      align-items: center;
      min-height: 32px;
      padding: 4px 12px;
      border: 1px solid var(--color-border-1);
      border-radius: 6px;
      background-color: var(--color-fill-2);
      cursor: pointer;
      transition: all 0.2s ease;

      // &:hover:not(.disabled) {
      //   border-color: var(--color-border-1);
      // }

      &.focused {
        border-color: rgb(var(--primary-6));
        background-color: var(--color-bg-1);
        :deep(.arco-tag) {
          background-color: var(--color-fill-2) !important;
        }
      }

      &.disabled {
        background-color: var(--color-bg-1);
        border-color: var(--color-border-1);
        cursor: not-allowed;
        opacity: 0.6;
      }

      .trigger-content {
        flex: 1;
        min-width: 0;

        .placeholder {
          color: var(--color-text-3);
          font-size: 14px;
        }

        .selected-display {
          .selected-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            align-items: center;

            // 设置 a-tag 组件的白色背景
            :deep(.arco-tag) {
              background-color: var(--color-bg-white);
            }

            .tag-icon {
              margin-right: 4px;
              font-size: 12px;
            }

            .more-count {
              color: var(--color-text-3);
              font-size: 12px;
              margin-left: 4px;
            }
          }
        }
      }

      .trigger-suffix {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-left: 8px;

        .clear-icon {
          color: var(--color-text-3);
          cursor: pointer;
          transition: color 0.2s ease;

          &:hover {
            color: var(--color-text-2);
          }
        }

        .arrow-icon {
          color: var(--color-text-3);
          transition: transform 0.2s ease;

          &.expanded {
            transform: rotate(180deg);
          }
        }
      }
    }
  }
</style>
