// 部门人员树状选择组件类型定义

// 接口数据类型定义
export interface TreeNode {
  id: number;
  parent_id?: number;
  name: string;
  is_department: boolean;
  children?: TreeNode[];
}

// 带唯一标识符的树节点（用于树组件）
export interface TreeNodeWithUniqueKey extends TreeNode {
  uniqueKey: string;
  children?: TreeNodeWithUniqueKey[];
}

export interface ApiResponse {
  code: number;
  msg: string;
  data: TreeNode[];
  retCode: number;
}

// 选择项类型定义
export interface SelectedItem {
  value: number;
  type: 'department' | 'user';
  label: string;
}

// 扁平化数据类型
export interface FlatItem extends TreeNode {
  type: 'department' | 'user';
}

// 组件 Props 类型定义
export interface DepartmentUserTreeSelectProps {
  modelValue?: SelectedItem[];
  placeholder?: string;
  disabled?: boolean;
  maxDisplayCount?: number;
}

// 组件 Emits 类型定义
export interface DepartmentUserTreeSelectEmits {
  'update:modelValue': [value: SelectedItem[]];
  'change': [value: SelectedItem[]];
}
