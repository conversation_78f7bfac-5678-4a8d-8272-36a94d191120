<!--弹窗表格-无操作-->
<template>
  <d-modal
    v-model:visible="visible"
    :title="showTitle"
    width="80vw"
    unmount-on-close
    :footer="null"
  >
    <base-table
      :columns-config="showColumns"
      :data-config="dataList"
      no-pagination
    >
    </base-table>
  </d-modal>
</template>

<script lang="ts" setup>
  import DModal from '@/components/d-modal/d-modal.vue';
  import { computed, reactive, ref } from 'vue';
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';

  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    columns: {
      type: Array,
      default: null,
    },
    list: {
      type: Array,
      default: null,
    },
  });

  const visible = ref(false);
  const info = reactive({
    title: '',
    columns: [],
    list: [],
  });

  const dataList = computed(() => props.list || info.list || []);
  const showTitle = computed(() => props.list || info.title || '');
  const showColumns = computed(() => props.columns || info.columns || '');
  const show = (data: any) => {
    visible.value = true;
    Object.assign(info, data);
  };

  defineExpose({
    show,
  });
</script>

<style scoped></style>
