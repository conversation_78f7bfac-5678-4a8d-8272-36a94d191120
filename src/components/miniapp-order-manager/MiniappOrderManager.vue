<template>
  <div class="miniapp-order-manager">
    <!-- 外部展示区域 -->
    <div class="order-display" @click="openModal">
      <span class="order-value">
        <a-button
          v-if="!localOrders.length"
          type="text"
          size="small"
          @click="openModal"
        >
          <template #icon>
            <icon-edit />
          </template>
          <span v-if="!localOrders.length"> 添加 </span>
          <span v-else> 编辑 </span>
        </a-button>
        <span v-else>{{ displayValue }}</span>
      </span>
      <a-button
        v-if="localOrders.length > 0"
        type="text"
        size="small"
        @click="openModal"
      >
        <template #icon>
          <icon-edit />
        </template>
        <span> 编辑 </span>
      </a-button>
    </div>

    <!-- 编辑模态框 -->
    <a-modal
      v-model:visible="modalVisible"
      title="关联小程序订单号"
      width="400px"
      class="no-bottom-modal-body"
      :mask-closable="false"
      :on-before-ok="handleConfirm"
      @cancel="handleCancel"
    >
      <div class="miniapp-order-section">
        <!-- 订单号输入框 -->
        <div class="miniapp-order-input-section">
          <a-input
            v-model="newOrderNo"
            placeholder="请输入小程序订单号，按回车键添加"
            allow-clear
            :loading="orderValidating"
            @keyup.enter="addOrder"
            @blur="addOrder"
          >
          </a-input>
        </div>

        <!-- 订单卡片列表 -->
        <div v-if="localOrders.length > 0" class="miniapp-orders-list">
          <div
            v-for="(order, index) in localOrders"
            :key="order.order_no"
            class="miniapp-order-card"
            :class="{
              loading: order.loading,
              error: order.error,
            }"
          >
            <!-- 卡片头部 -->
            <div class="order-card-header">
              <div class="order-number">
                <span class="label">订单编号：</span>
                <span class="value">{{ order.order_no }}</span>
              </div>
              <a-button
                v-if="order.error"
                type="text"
                size="small"
                status="danger"
                @click="retryValidateOrder(index)"
              >
                <template #icon>
                  <icon-refresh />
                </template>
              </a-button>
              <a-popconfirm
                v-if="!order.error"
                content="确定要删除这个小程序订单号吗？"
                position="left"
                type="warning"
                @ok="removeOrder(index)"
              >
                <a-button type="text" size="small" status="danger">
                  <template #icon>
                    <icon-delete />
                  </template>
                </a-button>
              </a-popconfirm>
              <a-button
                v-else
                type="text"
                size="small"
                status="danger"
                @click="removeOrder(index)"
              >
                <template #icon>
                  <icon-delete />
                </template>
              </a-button>
            </div>

            <!-- 加载状态 -->
            <div v-if="order.loading" class="order-loading">
              <a-spin />
              <span>正在获取订单信息...</span>
            </div>

            <!-- 错误状态 -->
            <div v-else-if="order.error" class="order-error">
              <div class="error-content">
                <icon-exclamation-circle-fill />
                <span>{{ order.error }}</span>
              </div>
            </div>

            <!-- 订单详情 -->
            <div v-else-if="order.details" class="order-details">
              <div class="detail-row">
                <span class="label">支付方式：</span>
                <span class="value">{{
                  order.details.payment_method || '-'
                }}</span>
              </div>
              <div class="detail-row">
                <span class="label">联系人：</span>
                <span class="value">
                  {{ order.details.contact_name || '-' }}
                  <span v-if="order.details.contact_phone" class="phone">
                    ({{ order.details.contact_phone }})
                  </span>
                </span>
              </div>
              <div class="detail-row">
                <span class="label">出行人数：</span>
                <span class="value">{{ order.details.buy_num || 0 }}人</span>
              </div>
              <div class="detail-row">
                <span class="label">支付时间：</span>
                <span class="value">{{ order.details.pay_time || '-' }}</span>
              </div>
              <div class="detail-row">
                <span class="label">订金金额：</span>
                <span class="value amount"
                  >¥{{ order.details.total_amount || '0.00' }}</span
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态提示 -->
        <!-- <div v-else class="empty-orders">
          <icon-plus-circle />
          <span>录入小程序订单号，可获取订单信息</span>
        </div> -->
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, watch, nextTick } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { debounce } from 'lodash';
  import request from '@/api/request';
  import {
    IconEdit,
    IconDelete,
    IconExclamationCircleFill,
    IconPlusCircle,
    IconRefresh,
  } from '@arco-design/web-vue/es/icon';

  // 订单类型定义
  export interface MiniappOrder {
    order_no: string;
    loading: boolean;
    error?: string;
    details?: {
      payment_method?: string;
      contact_name?: string;
      contact_phone?: string;
      buy_num?: number;
      pay_time?: string;
      total_amount?: string;
    };
  }

  // 简化格式的订单数据类型（用于兼容性）
  interface SimplifiedOrderData {
    order_no: string;
    contact_name?: string;
    contact_phone?: string;
    adult_num?: number;
    children_num?: number;
    payment_method?: string;
    pay_time?: string;
    total_amount?: string;
    buy_num?: number;
    [key: string]: any; // 允许其他字段
  }

  // 媒体订单信息类型
  export interface MediaOrderInfo {
    media_order_no: string;
    advance_price: number | null;
    order_create_time: string;
  }

  // Props 定义
  interface Props {
    modelValue: MiniappOrder[] | SimplifiedOrderData[] | any[];
    mediaOrderInfo?: MediaOrderInfo;
    clueId?: number;
    disabled?: boolean;
  }

  // Emits 定义
  interface Emits {
    (e: 'update:modelValue', value: MiniappOrder[]): void;
    (e: 'update:mediaOrderInfo', value: MediaOrderInfo): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => [],
    disabled: false,
  });

  const emit = defineEmits<Emits>();

  // 数据格式检测和转换工具函数
  const isStandardMiniappOrder = (data: any): data is MiniappOrder => {
    return (
      data &&
      typeof data === 'object' &&
      typeof data.order_no === 'string' &&
      typeof data.loading === 'boolean' &&
      (data.error === undefined || typeof data.error === 'string') &&
      (data.details === undefined || typeof data.details === 'object')
    );
  };

  const isSimplifiedOrderData = (data: any): data is SimplifiedOrderData => {
    return (
      data &&
      typeof data === 'object' &&
      typeof data.order_no === 'string' &&
      // 检查是否包含简化格式的特征字段
      (data.contact_name !== undefined ||
        data.contact_phone !== undefined ||
        data.adult_num !== undefined ||
        data.children_num !== undefined) &&
      // 确保不是标准格式（没有 loading 字段）
      data.loading === undefined
    );
  };

  const convertSimplifiedToStandard = (
    simplified: SimplifiedOrderData
  ): MiniappOrder => {
    return {
      order_no: simplified.order_no,
      loading: false,
      error: undefined,
      details: {
        payment_method: simplified.payment_method || '小程序支付',
        contact_name: simplified.contact_name,
        contact_phone: simplified.contact_phone,
        buy_num: simplified.buy_num,
        pay_time: simplified.pay_time,
        total_amount: simplified.total_amount,
      },
    };
  };

  const normalizeOrderData = (data: any[]): MiniappOrder[] => {
    if (!Array.isArray(data)) {
      console.warn('MiniappOrderManager: 传入的数据不是数组格式', data);
      return [];
    }

    return data.map((item) => {
      if (isStandardMiniappOrder(item)) {
        // 已经是标准格式，直接返回
        return item;
      }
      if (isSimplifiedOrderData(item)) {
        // 是简化格式，进行转换
        console.log('MiniappOrderManager: 检测到简化格式数据，正在转换', item);
        return convertSimplifiedToStandard(item);
      }
      // 未知格式，尝试基本转换
      console.warn(
        'MiniappOrderManager: 检测到未知格式数据，尝试基本转换',
        item
      );
      return {
        order_no: item.order_no || '',
        loading: false,
        error: item.order_no ? undefined : '订单号缺失',
        details: item.order_no
          ? {
              payment_method: '小程序支付',
              contact_name: item.contact_name,
              contact_phone: item.contact_phone,
              buy_num: item.buy_num || 0,
              pay_time: item.pay_time,
              total_amount: item.total_amount,
            }
          : undefined,
      };
    });
  };

  // 响应式状态
  const modalVisible = ref(false);
  const newOrderNo = ref('');
  const orderValidating = ref(false);
  const localOrders = ref<MiniappOrder[]>([]);

  // 计算属性
  const displayValue = computed(() => {
    const validOrders = props.modelValue.filter(
      (order) => order.details && !order.error
    );
    if (validOrders.length === 0) return '';
    return validOrders.map((order) => order.order_no).join(', ');
  });

  // 监听 props 变化，支持数据格式转换
  watch(
    () => props.modelValue,
    (newValue) => {
      // 使用数据格式检测和转换
      const normalizedData = normalizeOrderData(newValue as any[]);
      localOrders.value = [...normalizedData];

      // 如果检测到数据格式转换，发出更新事件以同步标准格式
      const hasConversion = newValue.some((item, index) => {
        const normalized = normalizedData[index];
        return !isStandardMiniappOrder(item) && normalized;
      });

      if (hasConversion) {
        console.log('MiniappOrderManager: 数据格式已转换，同步到父组件');
        // 延迟发出更新事件，避免在 watch 中直接修改 props
        nextTick(() => {
          emit('update:modelValue', normalizedData);
        });
      }
    },
    { immediate: true, deep: true }
  );

  // 验证单个小程序订单
  const validateOrder = async (index: number) => {
    const order = localOrders.value[index];
    if (!order) return;

    order.loading = true;
    order.error = undefined;

    try {
      const response = await request('/api/travel/miniOrderThreadInfo', {
        order_no: order.order_no,
        clue_id: props.clueId,
      });

      if (response.data) {
        // 如果绑定过其他线索，提示错误
        if (response.data.is_already_bind_order) {
          order.error = response.data.tip_msg;
          return;
        }
        order.details = {
          payment_method:
            response.data.payment_method || response.data.pay_type_text,
          contact_name: response.data.contact_name,
          contact_phone: response.data.contact_phone,
          adult_num: response.data.adult_num,
          children_num: response.data.children_num,
          pay_time: response.data.pay_time,
          total_amount: response.data.total_amount,
        };
      } else {
        order.error = '未找到订单信息';
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        order.error = error.response.data.message;
      } else {
        order.error = '获取订单信息失败，请稍后重试';
      }
      console.error('验证订单失败:', error);
    } finally {
      order.loading = false;
      newOrderNo.value = ''; // 清空输入框
    }
  };

  // 直接添加订单号（用于确认时自动添加）
  const addOrderDirectly = async (orderNo: string) => {
    if (!orderNo.trim()) return;

    // 检查是否已存在
    if (localOrders.value.some((order) => order.order_no === orderNo)) {
      // Message.warning('该订单号已存在');
      return;
    }

    // 添加到列表开头并开始验证
    const newOrder: MiniappOrder = {
      order_no: orderNo,
      loading: true,
      error: undefined,
      details: undefined,
    };

    localOrders.value.unshift(newOrder);

    // 验证订单（新订单在索引0位置）
    await validateOrder(0);
  };

  // 打开模态框
  const openModal = () => {
    if (props.disabled) return;
    // 使用数据格式转换确保本地数据是标准格式
    const normalizedData = normalizeOrderData(props.modelValue as any[]);
    localOrders.value = [...normalizedData];
    modalVisible.value = true;
  };

  // 确认操作
  const handleConfirm = async () => {
    // 检查输入框中是否还有未添加的订单号
    const pendingOrderNo = newOrderNo.value.trim();
    if (pendingOrderNo) {
      // 检查是否已存在
      const isDuplicate = localOrders.value.some(
        (order) => order.order_no === pendingOrderNo
      );

      if (isDuplicate) {
        Message.warning('输入框中的订单号已存在，请清空后再确认');
        return false;
      }

      // 自动添加输入框中的订单号
      try {
        orderValidating.value = true;
        await addOrderDirectly(pendingOrderNo);

        // 检查刚添加的订单是否验证成功（新订单在索引0位置）
        const addedOrder = localOrders.value[0];
        if (addedOrder && addedOrder.error) {
          // Message.error(`订单验证失败：${addedOrder.error}`);
          return false; // 阻止模态框关闭
        }

        // newOrderNo.value = ''; // 清空输入框
      } catch (error) {
        console.error('自动添加订单号失败:', error);
        Message.error('添加订单号时发生错误，请重试');
        return false; // 阻止模态框关闭
      } finally {
        orderValidating.value = false;
      }
    }

    // 检查所有订单是否都验证成功
    // const hasErrorOrders = localOrders.value.some((order) => order.error);
    // if (hasErrorOrders) {
    //   Message.warning('存在验证失败的订单，请处理后再确认');
    //   return false; // 阻止模态框关闭
    // }

    emit('update:modelValue', [...localOrders.value]);

    // 更新媒体订单信息
    if (props.mediaOrderInfo) {
      const validOrderNos = localOrders.value
        .filter((order) => order.details && !order.error)
        .map((order) => order.order_no);

      if (validOrderNos.length > 0) {
        const updatedMediaOrderInfo = {
          ...props.mediaOrderInfo,
          media_order_no: validOrderNos.join(','),
        };

        // 如果是第一个订单，更新订金和时间信息
        const firstOrder = localOrders.value.find(
          (order) => order.details && !order.error
        );
        if (firstOrder?.details) {
          updatedMediaOrderInfo.advance_price = parseFloat(
            firstOrder.details.total_amount || '0'
          );
          updatedMediaOrderInfo.order_create_time =
            firstOrder.details.pay_time || '';
        }

        emit('update:mediaOrderInfo', updatedMediaOrderInfo);
      }
    }

    modalVisible.value = false;
  };

  // 取消操作
  const handleCancel = () => {
    // 使用数据格式转换确保本地数据是标准格式
    const normalizedData = normalizeOrderData(props.modelValue as any[]);
    localOrders.value = [...normalizedData];
    newOrderNo.value = '';
    modalVisible.value = false;
  };

  // 防抖添加订单号
  const debouncedAddOrder = debounce(addOrderDirectly, 300);

  // 添加小程序订单号
  const addOrder = async () => {
    const orderNo = newOrderNo.value.trim();
    if (!orderNo) return;

    orderValidating.value = true;
    try {
      await debouncedAddOrder(orderNo);
    } finally {
      orderValidating.value = false;
    }
  };

  // 删除小程序订单
  const removeOrder = (index: number) => {
    localOrders.value.splice(index, 1);
  };

  // 重试验证订单
  const retryValidateOrder = async (index: number) => {
    const order = localOrders.value[index];
    if (!order) return;

    // 清除错误状态
    order.error = undefined;

    // 重新验证订单
    await validateOrder(index);
  };
</script>

<style lang="less" scoped>
  .miniapp-order-manager {
    width: 100%;
  }

  .order-display {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--color-fill-2);
    padding: 3px 10px;
    border-radius: 5px;
    cursor: pointer;
    &:hover {
      background: var(--color-fill-3);
    }

    .order-value {
      color: var(--color-text-1);
      font-size: 14px;
      word-break: break-all;
      line-height: 1.4;
      text-decoration: underline;
      cursor: pointer;

      &:empty::before {
        content: '未设置';
        color: var(--color-text-3);
        font-style: italic;
      }
    }
  }

  // 小程序订单管理样式
  .miniapp-order-section {
    width: 100%;
  }

  .miniapp-order-input-section {
    margin-bottom: 16px;
  }

  .miniapp-orders-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 12px;
    max-height: 480px;
    overflow-y: auto;

    .miniapp-order-card {
      border: 1px solid var(--color-border-2);
      border-radius: 8px;
      padding: 14px;
      background: var(--color-bg-1);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);

      &:hover {
        box-shadow: 0 3px 12px rgba(0, 0, 0, 0.06);
        // transform: translateY(-1px);
      }

      &.loading {
        border-color: var(--color-primary-4);
        background: linear-gradient(
          135deg,
          var(--color-primary-light-1),
          var(--color-bg-1)
        );
        box-shadow: 0 2px 6px rgba(var(--primary-6), 0.12);
      }

      &.error {
        border-color: var(--color-danger-4);
        background: linear-gradient(
          135deg,
          var(--color-danger-light-1),
          var(--color-bg-1)
        );
        box-shadow: 0 2px 6px rgba(var(--danger-6), 0.12);
      }

      .order-card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
        padding-bottom: 8px;
        border-bottom: 1px solid var(--color-fill-2);

        .order-number {
          flex: 1;
          min-width: 0;

          .label {
            color: var(--color-text-3);
            font-size: 11px;
            font-weight: 400;
            margin-bottom: 2px;
            display: block;
            line-height: 1.3;
          }

          .value {
            color: var(--color-text-1);
            font-size: 13px;
            font-weight: 600;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            word-break: break-all;
            line-height: 1.3;
          }
        }

        .arco-popconfirm {
          flex-shrink: 0;
          margin-left: 10px;
        }
      }

      .order-loading {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 12px 12px;
        font-size: 13px;
        border-radius: 6px;
        margin: 6px 0;
        color: var(--color-primary-6);
        background: var(--color-primary-light-2);
        border: 1px solid var(--color-primary-3);

        .arco-spin {
          color: var(--color-primary-6);
        }
      }

      .order-error {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px;
        font-size: 13px;
        border-radius: 6px;
        margin: 6px 0;
        color: var(--color-danger-6);
        background: var(--color-danger-light-2);
        border: 1px solid var(--color-danger-3);

        .error-content {
          display: flex;
          align-items: center;
          gap: 8px;
          flex: 1;
          min-width: 0;

          .arco-icon {
            font-size: 14px;
            flex-shrink: 0;
          }

          span {
            word-break: break-word;
            line-height: 1.4;
          }
        }

        .refresh-button {
          flex-shrink: 0;
          margin-left: 8px;
          color: var(--color-danger-6);

          &:hover {
            background: var(--color-danger-light-3);
          }

          .arco-icon {
            font-size: 12px;
          }
        }
      }

      .order-details {
        .detail-row {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          padding: 6px 0;
          border-bottom: 1px solid var(--color-fill-1);

          &:last-child {
            border-bottom: none;
            padding-bottom: 0;
          }

          .label {
            color: var(--color-text-3);
            font-size: 12px;
            font-weight: 500;
            min-width: 80px;
            flex-shrink: 0;
            line-height: 1.3;
          }

          .value {
            color: var(--color-text-1);
            font-size: 12px;
            text-align: right;
            flex: 1;
            min-width: 0;
            line-height: 1.3;
            word-break: break-all;

            &.amount {
              color: var(--color-success-6);
              font-weight: 600;
              font-size: 13px;
            }

            .phone {
              color: var(--color-text-3);
              font-size: 11px;
              margin-left: 3px;
            }
          }
        }
      }
    }
  }

  .empty-orders {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: var(--color-text-3);
    font-size: 14px;
    border: 1px dashed var(--color-border-2);
    border-radius: 8px;
    background: var(--color-fill-1);

    .arco-icon {
      font-size: 24px;
      margin-bottom: 8px;
      color: var(--color-text-4);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .miniapp-orders-list {
      grid-template-columns: 1fr;
      gap: 10px;

      .miniapp-order-card {
        padding: 12px;
        border-radius: 6px;

        .order-card-header {
          margin-bottom: 8px;
          padding-bottom: 6px;

          .order-number {
            .label {
              font-size: 10px;
            }

            .value {
              font-size: 12px;
            }
          }
        }

        .order-details .detail-row {
          padding: 5px 0;

          .label {
            min-width: 65px;
            font-size: 11px;
          }

          .value {
            font-size: 11px;

            &.amount {
              font-size: 12px;
            }

            .phone {
              font-size: 10px;
            }
          }
        }

        .order-loading {
          padding: 12px 10px;
          font-size: 12px;
          margin: 4px 0;
        }

        .order-error {
          padding: 10px;
          font-size: 12px;
          margin: 4px 0;

          .error-content {
            gap: 6px;

            span {
              font-size: 12px;
            }
          }

          .refresh-button {
            margin-left: 6px;

            .arco-icon {
              font-size: 11px;
            }
          }
        }
      }
    }
  }

  @media (max-width: 480px) {
    .miniapp-orders-list {
      gap: 8px;

      .miniapp-order-card {
        padding: 10px;

        .order-card-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 6px;
          margin-bottom: 6px;
          padding-bottom: 4px;

          .arco-popconfirm {
            margin-left: 0;
            align-self: flex-end;
          }
        }

        .order-details .detail-row {
          padding: 4px 0;

          .label {
            min-width: 60px;
            font-size: 10px;
          }

          .value {
            font-size: 10px;

            &.amount {
              font-size: 11px;
            }
          }
        }

        .order-error {
          flex-direction: column;
          align-items: stretch;
          gap: 8px;
          padding: 8px;

          .error-content {
            justify-content: center;

            span {
              font-size: 11px;
              text-align: center;
            }
          }

          .refresh-button {
            margin-left: 0;
            align-self: center;
            font-size: 11px;
          }
        }
      }
    }
  }
</style>
