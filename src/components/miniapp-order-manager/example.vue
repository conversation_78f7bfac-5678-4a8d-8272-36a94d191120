<template>
  <div class="example-page">
    <h2>数据格式兼容性示例</h2>
    
    <div class="example-section">
      <h3>场景1：简化格式数据</h3>
      <p>模拟从后端接收到的简化格式数据</p>
      <miniapp-order-manager
        v-model="simplifiedOrders"
        :media-order-info="mediaOrderInfo1"
        @update:media-order-info="handleUpdateMediaOrderInfo1"
      />
      
      <div class="data-preview">
        <h4>当前数据：</h4>
        <pre>{{ JSON.stringify(simplifiedOrders, null, 2) }}</pre>
      </div>
    </div>

    <div class="example-section">
      <h3>场景2：混合格式数据</h3>
      <p>包含简化格式、标准格式和未知格式的混合数据</p>
      <miniapp-order-manager
        v-model="mixedOrders"
        :media-order-info="mediaOrderInfo2"
        @update:media-order-info="handleUpdateMediaOrderInfo2"
      />
      
      <div class="data-preview">
        <h4>当前数据：</h4>
        <pre>{{ JSON.stringify(mixedOrders, null, 2) }}</pre>
      </div>
    </div>

    <div class="example-section">
      <h3>场景3：空数据和错误数据</h3>
      <p>测试组件对异常数据的处理能力</p>
      <miniapp-order-manager
        v-model="errorOrders"
        :media-order-info="mediaOrderInfo3"
        @update:media-order-info="handleUpdateMediaOrderInfo3"
      />
      
      <div class="data-preview">
        <h4>当前数据：</h4>
        <pre>{{ JSON.stringify(errorOrders, null, 2) }}</pre>
      </div>
    </div>

    <div class="controls">
      <h3>控制面板</h3>
      <a-space wrap>
        <a-button @click="loadSimplifiedData">加载简化格式数据</a-button>
        <a-button @click="loadMixedData">加载混合格式数据</a-button>
        <a-button @click="loadErrorData">加载异常数据</a-button>
        <a-button @click="clearAllData">清空所有数据</a-button>
      </a-space>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import MiniappOrderManager, { 
    type MediaOrderInfo 
  } from './MiniappOrderManager.vue';

  // 场景1：简化格式数据
  const simplifiedOrders = ref([
    {
      order_no: "PRO03601653716494",
      customer_name: "彭于晏",
      contact_phone: "15738519353",
      adult_num: 2,
      children_num: 1,
      payment_method: "小程序支付",
      pay_time: "2024-01-15 10:30:00",
      total_amount: "1500.00"
    }
  ]);

  const mediaOrderInfo1 = ref<MediaOrderInfo>({
    media_order_no: '',
    advance_price: null,
    order_create_time: '',
  });

  // 场景2：混合格式数据
  const mixedOrders = ref([]);
  const mediaOrderInfo2 = ref<MediaOrderInfo>({
    media_order_no: '',
    advance_price: null,
    order_create_time: '',
  });

  // 场景3：异常数据
  const errorOrders = ref([]);
  const mediaOrderInfo3 = ref<MediaOrderInfo>({
    media_order_no: '',
    advance_price: null,
    order_create_time: '',
  });

  // 事件处理
  const handleUpdateMediaOrderInfo1 = (info: MediaOrderInfo) => {
    mediaOrderInfo1.value = { ...info };
    console.log('场景1 - 媒体订单信息已更新:', info);
  };

  const handleUpdateMediaOrderInfo2 = (info: MediaOrderInfo) => {
    mediaOrderInfo2.value = { ...info };
    console.log('场景2 - 媒体订单信息已更新:', info);
  };

  const handleUpdateMediaOrderInfo3 = (info: MediaOrderInfo) => {
    mediaOrderInfo3.value = { ...info };
    console.log('场景3 - 媒体订单信息已更新:', info);
  };

  // 控制方法
  const loadSimplifiedData = () => {
    simplifiedOrders.value = [
      {
        order_no: "PRO03601653716494",
        customer_name: "彭于晏",
        contact_phone: "15738519353",
        adult_num: 2,
        children_num: 1,
        payment_method: "小程序支付",
        pay_time: "2024-01-15 10:30:00",
        total_amount: "1500.00"
      },
      {
        order_no: "PRO14619643712006",
        customer_name: "low",
        contact_phone: "13213213213",
        adult_num: 1,
        children_num: 0,
        payment_method: "小程序支付",
        pay_time: "2024-01-16 14:20:00",
        total_amount: "800.00"
      }
    ];
  };

  const loadMixedData = () => {
    mixedOrders.value = [
      // 简化格式
      {
        order_no: 'SIMPLE001',
        customer_name: '简化格式用户',
        contact_phone: '13900139001',
        adult_num: 1,
        children_num: 1,
      },
      // 标准格式
      {
        order_no: 'STANDARD001',
        loading: false,
        details: {
          payment_method: '小程序支付',
          customer_name: '标准格式用户',
          contact_phone: '13900139002',
          travel_count: 3,
          pay_time: '2024-01-17 09:00:00',
          total_amount: '2000.00',
        },
      },
      // 未知格式
      {
        order_no: 'UNKNOWN001',
        name: '未知格式用户',
        phone: '13900139003',
        some_field: 'test',
      },
    ];
  };

  const loadErrorData = () => {
    errorOrders.value = [
      // 缺少订单号
      {
        customer_name: '无订单号用户',
        contact_phone: '13900139004',
      },
      // 空对象
      {},
      // null 值
      null,
      // 字符串（错误类型）
      'invalid_data',
    ];
  };

  const clearAllData = () => {
    simplifiedOrders.value = [];
    mixedOrders.value = [];
    errorOrders.value = [];
  };
</script>

<style lang="less" scoped>
  .example-page {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .example-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid var(--color-border-2);
    border-radius: 8px;
    background: var(--color-bg-1);

    h3 {
      margin-top: 0;
      margin-bottom: 8px;
      color: var(--color-text-1);
      font-size: 16px;
      font-weight: 600;
    }

    p {
      margin-bottom: 16px;
      color: var(--color-text-3);
      font-size: 14px;
    }
  }

  .data-preview {
    margin-top: 16px;
    
    h4 {
      margin-bottom: 8px;
      color: var(--color-text-2);
      font-size: 14px;
      font-weight: 500;
    }

    pre {
      background: var(--color-fill-2);
      border: 1px solid var(--color-border-3);
      border-radius: 4px;
      padding: 12px;
      font-size: 12px;
      line-height: 1.4;
      overflow-x: auto;
      color: var(--color-text-2);
      max-height: 200px;
      overflow-y: auto;
    }
  }

  .controls {
    padding: 20px;
    background: var(--color-fill-1);
    border-radius: 8px;

    h3 {
      margin-top: 0;
      margin-bottom: 16px;
      color: var(--color-text-1);
      font-size: 16px;
      font-weight: 600;
    }
  }
</style>
