# MiniappOrderManager 组件

一个用于管理小程序订单号的通用 Vue 组件，支持多种数据格式的自动检测和转换。

## 功能特性

- ✅ **外部展示**：显示订单号列表，支持编辑按钮
- ✅ **模态框管理**：完整的订单添加、验证、删除功能
- ✅ **数据格式兼容**：自动检测和转换不同格式的订单数据
- ✅ **实时验证**：支持 API 验证订单信息
- ✅ **错误处理**：完善的错误提示和重试机制
- ✅ **响应式设计**：适配不同屏幕尺寸
- ✅ **TypeScript 支持**：完整的类型定义

## 数据格式支持

### 1. 标准格式（MiniappOrder）
```typescript
interface MiniappOrder {
  order_no: string;
  loading: boolean;
  error?: string;
  details?: {
    payment_method?: string;
    customer_name?: string;
    contact_phone?: string;
    travel_count?: number;
    pay_time?: string;
    total_amount?: string;
  };
}
```

### 2. 简化格式（SimplifiedOrderData）
```typescript
interface SimplifiedOrderData {
  order_no: string;
  customer_name?: string;
  contact_phone?: string;
  adult_num?: number;
  children_num?: number;
  payment_method?: string;
  pay_time?: string;
  total_amount?: string;
  travel_count?: number;
  [key: string]: any;
}
```

### 3. 自动转换示例

**输入（简化格式）：**
```json
{
  "order_no": "PRO03601653716494",
  "customer_name": "彭于晏",
  "contact_phone": "15738519353",
  "adult_num": 2,
  "children_num": 1,
  "payment_method": "小程序支付",
  "pay_time": "2024-01-15 10:30:00",
  "total_amount": "1500.00"
}
```

**输出（标准格式）：**
```json
{
  "order_no": "PRO03601653716494",
  "loading": false,
  "error": undefined,
  "details": {
    "payment_method": "小程序支付",
    "customer_name": "彭于晏",
    "contact_phone": "15738519353",
    "travel_count": 3,
    "pay_time": "2024-01-15 10:30:00",
    "total_amount": "1500.00"
  }
}
```

## 使用方法

### 基本使用

```vue
<template>
  <a-form-item label="小程序订单号">
    <miniapp-order-manager
      v-model="orders"
      :media-order-info="mediaOrderInfo"
      :clue-id="clueId"
      @update:media-order-info="handleUpdateMediaOrderInfo"
    />
  </a-form-item>
</template>

<script setup>
import MiniappOrderManager from '@/components/miniapp-order-manager/MiniappOrderManager.vue';

const orders = ref([]);
const mediaOrderInfo = ref({
  media_order_no: '',
  advance_price: null,
  order_create_time: '',
});
const clueId = ref(123);

const handleUpdateMediaOrderInfo = (info) => {
  console.log('媒体订单信息已更新:', info);
};
</script>
```

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | `MiniappOrder[] \| SimplifiedOrderData[] \| any[]` | `[]` | 订单数据（支持多种格式） |
| mediaOrderInfo | `MediaOrderInfo` | - | 媒体订单信息 |
| clueId | `number` | - | 线索ID（用于API验证） |
| disabled | `boolean` | `false` | 是否禁用 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | `MiniappOrder[]` | 订单数据更新 |
| update:mediaOrderInfo | `MediaOrderInfo` | 媒体订单信息更新 |

## 数据转换逻辑

组件会自动检测传入数据的格式：

1. **标准格式检测**：检查是否包含 `loading` 字段和完整的数据结构
2. **简化格式检测**：检查是否包含 `customer_name`、`contact_phone`、`adult_num` 等特征字段
3. **自动转换**：将简化格式转换为标准格式
4. **错误处理**：对于未知格式，尝试基本转换并设置错误状态

## 转换规则

- `adult_num + children_num` → `travel_count`
- 简化格式字段 → `details` 对象
- 设置默认值：`loading: false`、`payment_method: '小程序支付'`
- 缺少订单号时设置错误状态

## 测试文件

- `test.vue`：基本功能测试
- `example.vue`：数据格式兼容性示例

## 注意事项

1. 组件会在检测到数据格式转换时自动同步到父组件
2. 使用 `nextTick` 避免在 watch 中直接修改 props
3. 支持混合格式数据（同一数组中包含不同格式）
4. 对异常数据有容错处理机制
