<template>
  <div class="test-page">
    <h2>小程序订单管理组件测试</h2>

    <div class="test-section">
      <h3>基本使用</h3>
      <a-form>
        <a-form-item label="小程序订单号">
          <miniapp-order-manager
            v-model="orders"
            :media-order-info="mediaOrderInfo"
            @update:media-order-info="handleUpdateMediaOrderInfo"
          />
        </a-form-item>
      </a-form>
    </div>

    <div class="test-section">
      <h3>当前数据</h3>
      <div class="data-display">
        <h4>订单列表：</h4>
        <pre>{{ JSON.stringify(orders, null, 2) }}</pre>

        <h4>媒体订单信息：</h4>
        <pre>{{ JSON.stringify(mediaOrderInfo, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>操作按钮</h3>
      <a-space wrap>
        <a-button @click="addTestOrder">添加标准格式测试订单</a-button>
        <a-button type="primary" @click="loadSimplifiedData">
          加载简化格式数据
        </a-button>
        <a-button status="warning" @click="addMixedData">
          加载混合格式数据
        </a-button>
        <a-button @click="clearOrders">清空订单</a-button>
        <a-button @click="resetMediaOrderInfo">重置媒体订单信息</a-button>
      </a-space>
    </div>

    <div class="test-section">
      <h3>数据格式说明</h3>
      <div class="format-description">
        <h4>简化格式示例：</h4>
        <pre>{{ JSON.stringify(simplifiedTestData[0], null, 2) }}</pre>

        <h4>标准格式示例：</h4>
        <pre>{{ JSON.stringify(standardFormatExample, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import MiniappOrderManager, {
    type MiniappOrder,
    type MediaOrderInfo,
  } from './MiniappOrderManager.vue';

  // 测试数据
  const orders = ref<any[]>([]);

  const mediaOrderInfo = ref<MediaOrderInfo>({
    media_order_no: '',
    advance_price: null,
    order_create_time: '',
  });

  // 简化格式测试数据
  const simplifiedTestData = [
    {
      order_no: 'PRO03601653716494',
      customer_name: '彭于晏',
      contact_phone: '15738519353',
      adult_num: 2,
      children_num: 1,
      payment_method: '小程序支付',
      pay_time: '2024-01-15 10:30:00',
      total_amount: '1500.00',
    },
    {
      order_no: 'PRO14619643712006',
      customer_name: 'low',
      contact_phone: '13213213213',
      adult_num: 1,
      children_num: 0,
      payment_method: '小程序支付',
      pay_time: '2024-01-16 14:20:00',
      total_amount: '800.00',
    },
  ];

  // 标准格式示例
  const standardFormatExample = {
    order_no: 'TEST123456789',
    loading: false,
    error: undefined,
    details: {
      payment_method: '小程序支付',
      customer_name: '测试用户',
      contact_phone: '13800138000',
      travel_count: 2,
      pay_time: '2024-01-15 10:30:00',
      total_amount: '1000.00',
    },
  };

  // 处理媒体订单信息更新
  const handleUpdateMediaOrderInfo = (info: MediaOrderInfo) => {
    mediaOrderInfo.value = { ...info };
    console.log('媒体订单信息已更新:', info);
  };

  // 添加标准格式测试订单
  const addTestOrder = () => {
    const testOrder: MiniappOrder = {
      order_no: `TEST${Date.now()}`,
      loading: false,
      details: {
        payment_method: '小程序支付',
        customer_name: '测试用户',
        contact_phone: '13800138000',
        travel_count: 2,
        pay_time: new Date().toISOString(),
        total_amount: '1000.00',
      },
    };
    orders.value.push(testOrder);
  };

  // 加载简化格式数据
  const loadSimplifiedData = () => {
    console.log('加载简化格式数据:', simplifiedTestData);
    orders.value = [...simplifiedTestData];
  };

  // 加载混合格式数据
  const addMixedData = () => {
    const mixedData = [
      // 简化格式
      {
        order_no: 'SIMPLE001',
        customer_name: '简化格式用户1',
        contact_phone: '13900139001',
        adult_num: 1,
        children_num: 1,
      },
      // 标准格式
      {
        order_no: 'STANDARD001',
        loading: false,
        details: {
          payment_method: '小程序支付',
          customer_name: '标准格式用户1',
          contact_phone: '13900139002',
          travel_count: 3,
          pay_time: '2024-01-17 09:00:00',
          total_amount: '2000.00',
        },
      },
      // 未知格式
      {
        order_no: 'UNKNOWN001',
        name: '未知格式用户1',
        phone: '13900139003',
        some_other_field: 'test',
      },
    ];
    console.log('加载混合格式数据:', mixedData);
    orders.value = mixedData;
  };

  // 清空订单
  const clearOrders = () => {
    orders.value = [];
  };

  // 重置媒体订单信息
  const resetMediaOrderInfo = () => {
    mediaOrderInfo.value = {
      media_order_no: '',
      advance_price: null,
      order_create_time: '',
    };
  };
</script>

<style lang="less" scoped>
  .test-page {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .test-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid var(--color-border-2);
    border-radius: 8px;
    background: var(--color-bg-1);

    h3 {
      margin-top: 0;
      margin-bottom: 16px;
      color: var(--color-text-1);
      font-size: 16px;
      font-weight: 600;
    }

    h4 {
      margin-top: 16px;
      margin-bottom: 8px;
      color: var(--color-text-2);
      font-size: 14px;
      font-weight: 500;
    }
  }

  .data-display {
    pre {
      background: var(--color-fill-2);
      border: 1px solid var(--color-border-3);
      border-radius: 4px;
      padding: 12px;
      font-size: 12px;
      line-height: 1.4;
      overflow-x: auto;
      color: var(--color-text-2);
    }
  }
</style>
