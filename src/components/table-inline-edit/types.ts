/**
 * 表格内联编辑组件类型定义
 */

export interface TableInlineEditProps {
  /** 绑定值 */
  modelValue?: string | null;
  /** 占位符 */
  placeholder?: string;
  /** 最大显示行数 */
  maxLines?: number;
  /** 最大字符长度 */
  maxLength?: number;
  /** 是否显示字符计数 */
  showWordLimit?: boolean;
  /** 是否显示复制按钮 */
  showCopy?: boolean;
  /** 编辑模式：click-点击编辑，dblclick-双击编辑 */
  editMode?: 'click' | 'dblclick';
  /** 保存模式：manual-手动保存，auto-自动保存 */
  saveMode?: 'manual' | 'auto';
  /** 自动保存延迟时间(ms) */
  autoSaveDelay?: number;
  /** 最小行数 */
  minRows?: number;
  /** 最大行数 */
  maxRows?: number;
  /** 自定义保存函数 */
  onSave?: (value: string) => Promise<void>;
  /** 是否禁用编辑 */
  disabled?: boolean;
}

export interface TableInlineEditEmits {
  (e: 'update:modelValue', value: string): void;
  (e: 'save', value: string): void;
  (e: 'cancel'): void;
  (e: 'edit-start'): void;
  (e: 'edit-end'): void;
}

export interface TableInlineEditInstance {
  /** 开始编辑 */
  startEdit: () => void;
  /** 保存编辑 */
  handleSave: () => Promise<void>;
  /** 取消编辑 */
  handleCancel: () => void;
}

/**
 * 表格内联编辑配置选项
 */
export interface TableInlineEditConfig {
  /** API保存函数 */
  saveApi?: (params: any) => Promise<any>;
  /** 保存参数构造函数 */
  buildSaveParams?: (value: string, record: any) => any;
  /** 成功消息 */
  successMessage?: string;
  /** 失败消息 */
  errorMessage?: string;
  /** 是否在保存失败时恢复原值 */
  revertOnError?: boolean;
}
