<template>
  <div class="table-inline-edit" :class="{ 'has-copy': showCopy }">
    <!-- 显示模式 -->
    <div v-if="!isEditing" class="display-mode">
      <a-tooltip v-if="shouldShowTooltip" :content="fullText" position="top">
        <div
          class="text-content"
          :class="{ [`lines-${maxLines}`]: true }"
          @click="handleEditTrigger"
          @dblclick="handleEditTrigger"
        >
          {{ displayText }}
        </div>
      </a-tooltip>
      <div
        v-else
        class="text-content"
        :class="{ [`lines-${maxLines}`]: true }"
        @click="handleEditTrigger"
        @dblclick="handleEditTrigger"
      >
        {{ displayText }}
        <a-button type="text" size="mini" @click="handleEditTrigger">
          <template #icon>
            <icon-edit />
          </template>
        </a-button>
      </div>

      <!-- 操作按钮容器 - 垂直排列 -->
      <!-- <div class="action-buttons">
        <a-button
          v-if="showCopy && fullText"
          type="text"
          size="mini"
          @click="handleCopy"
        >
          <template #icon>
            <icon-copy />
          </template>
        </a-button>

        <a-button type="text" size="mini" @click="handleEditTrigger">
          <template #icon>
            <icon-edit />
          </template>
        </a-button>
      </div> -->
    </div>

    <!-- 编辑模式 -->
    <div v-else class="edit-mode">
      <!-- 根据 type 动态选择输入组件 -->
      <a-input
        v-if="type === 'input'"
        ref="inputRef"
        v-model="editValue"
        :placeholder="placeholder"
        :max-length="maxLength"
        :show-word-limit="showWordLimit"
        class="edit-input"
        @blur="handleBlur"
        @keydown="handleKeydown"
        @press-enter="handleSave"
      />
      <a-textarea
        v-else
        ref="textareaRef"
        v-model="editValue"
        :placeholder="placeholder"
        :auto-size="{ minRows: minRows, maxRows: maxRows }"
        :max-length="maxLength"
        :show-word-limit="showWordLimit"
        class="edit-textarea"
        @blur="handleBlur"
        @keydown="handleTextareaKeydown"
      />

      <div class="edit-actions">
        <a-button
          type="primary"
          size="mini"
          :loading="saving"
          @click="handleSave"
        >
          <template #icon>
            <icon-check />
          </template>
        </a-button>
        <a-button size="mini" @click="handleCancel">
          <template #icon>
            <icon-close />
          </template>
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, nextTick, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useClipboard } from '@vueuse/core';

  export interface TableInlineEditProps {
    /** 绑定值 */
    modelValue?: string | null;
    /** 输入组件类型：input-单行输入框，textarea-多行文本框 */
    type?: 'input' | 'textarea';
    /** 占位符 */
    placeholder?: string;
    /** 最大显示行数 */
    maxLines?: number;
    /** 最大字符长度 */
    maxLength?: number;
    /** 是否显示字符计数 */
    showWordLimit?: boolean;
    /** 是否显示复制按钮 */
    showCopy?: boolean;
    /** 编辑模式：click-点击编辑，dblclick-双击编辑 */
    editMode?: 'click' | 'dblclick';
    /** 保存模式：manual-手动保存，auto-自动保存 */
    saveMode?: 'manual' | 'auto';
    /** 自动保存延迟时间(ms) */
    autoSaveDelay?: number;
    /** 最小行数 */
    minRows?: number;
    /** 最大行数 */
    maxRows?: number;
    /** 自定义保存函数 */
    onSave?: (value: string) => Promise<void>;
    /** 是否禁用编辑 */
    disabled?: boolean;
  }

  export interface TableInlineEditEmits {
    (e: 'update:modelValue', value: string): void;
    (e: 'save', value: string): void;
    (e: 'cancel'): void;
    (e: 'editStart'): void;
    (e: 'editEnd'): void;
  }

  const props = withDefaults(defineProps<TableInlineEditProps>(), {
    modelValue: '',
    type: 'textarea',
    placeholder: '点击编辑',
    maxLines: 2,
    maxLength: 500,
    showWordLimit: false,
    showCopy: true,
    editMode: 'click',
    saveMode: 'manual',
    autoSaveDelay: 1000,
    minRows: 2,
    maxRows: 6,
    disabled: false,
  });

  const emit = defineEmits<TableInlineEditEmits>();

  const { copy } = useClipboard();

  // 状态管理
  const isEditing = ref(false);
  const editValue = ref('');
  const saving = ref(false);
  const textareaRef = ref();
  const inputRef = ref();

  // 计算属性
  const fullText = computed(() => {
    if (
      !props.modelValue ||
      props.modelValue === null ||
      props.modelValue === undefined
    ) {
      return '';
    }
    return String(props.modelValue).trim();
  });

  const displayText = computed(() => {
    if (!fullText.value) {
      return '-';
    }
    return fullText.value;
  });

  const shouldShowTooltip = computed(() => {
    if (!fullText.value) return false;
    // 简单的长度判断，实际可以根据需要调整
    const estimatedLinesLength = props.maxLines * 8; // 假设每行约30个字符
    return fullText.value.length > estimatedLinesLength;
  });

  // 监听modelValue变化
  watch(
    () => props.modelValue,
    (newValue) => {
      if (!isEditing.value) {
        editValue.value = newValue || '';
      }
    },
    { immediate: true }
  );

  // 开始编辑
  const startEdit = async () => {
    if (props.disabled || isEditing.value) return;

    isEditing.value = true;
    editValue.value = fullText.value;
    emit('editStart');

    await nextTick();
    // 根据 type 聚焦到对应的输入组件
    if (props.type === 'input') {
      inputRef.value?.focus();
    } else {
      textareaRef.value?.focus();
    }
  };

  // 保存
  const handleSave = async () => {
    if (saving.value) return;

    try {
      saving.value = true;

      // 如果有自定义保存函数，调用它
      if (props.onSave) {
        await props.onSave(editValue.value);
      }

      // 更新值
      emit('update:modelValue', editValue.value);
      emit('save', editValue.value);

      // 结束编辑
      isEditing.value = false;
      emit('editEnd');
    } catch (error) {
      console.error('保存失败:', error);
      // Message.error('保存失败');
    } finally {
      saving.value = false;
    }
  };

  // 取消编辑
  const handleCancel = () => {
    editValue.value = fullText.value;
    isEditing.value = false;
    emit('cancel');
    emit('editEnd');
  };

  // 编辑触发处理
  const handleEditTrigger = (event: Event) => {
    if (props.disabled) return;

    const isClickMode = props.editMode === 'click' && event.type === 'click';
    const isDblClickMode =
      props.editMode === 'dblclick' && event.type === 'dblclick';

    if (isClickMode || isDblClickMode) {
      startEdit();
    }
  };

  // 处理失焦
  const handleBlur = () => {
    if (props.saveMode === 'auto') {
      setTimeout(() => {
        if (isEditing.value) {
          handleSave();
        }
      }, props.autoSaveDelay);
    }
  };

  // 处理键盘事件
  const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      handleCancel();
    } else if (event.key === 'Enter') {
      handleSave();
    }
  };

  // 处理textarea键盘事件
  const handleTextareaKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      handleCancel();
      // ctrl + enter 保存 或者 Command + enter 保存
    } else if (
      (event.key === 'Enter' && event.ctrlKey) ||
      (event.key === 'Enter' && event.metaKey)
    ) {
      handleSave();
    }
  };

  // 复制功能
  const handleCopy = async () => {
    if (!fullText.value) return;

    try {
      await copy(fullText.value);
      Message.success('复制成功');
    } catch (error) {
      console.error('复制失败:', error);
      Message.error('复制失败');
    }
  };

  // 暴露方法
  defineExpose({
    startEdit,
    handleSave,
    handleCancel,
  });
</script>

<style scoped lang="less">
  .table-inline-edit {
    position: relative;
    width: 100%;

    .display-mode {
      position: relative;
      display: flex;
      align-items: flex-start;
      gap: 8px;

      .text-content {
        flex: 1;
        white-space: pre-line;
        word-wrap: break-word;
        cursor: pointer;
        transition: background-color 0.2s ease;
        padding: 2px 4px;
        border-radius: 4px;
        display: flex;
        align-items: center;

        &:hover {
          background-color: var(--color-fill-2);
        }

        // 多行文本截断
        &.lines-1 {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        &.lines-2 {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        &.lines-3 {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      // 按钮容器 - 垂直排列
      .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 4px;
        flex-shrink: 0;
        align-items: center;
      }

      .copy-button,
      .edit-button {
        width: 20px;
        height: 20px;
        padding: 0;
        opacity: 0.6;
        transition: opacity 0.2s ease;

        :deep(.arco-btn-icon) {
          font-size: 12px;
          color: var(--color-text-3);
        }

        &:hover {
          opacity: 1;

          :deep(.arco-btn-icon) {
            color: rgb(var(--primary-6));
          }
        }
      }
    }

    &:hover .action-buttons .copy-button,
    &:hover .action-buttons .edit-button {
      opacity: 1;
    }

    .edit-mode {
      width: 100%;

      .edit-input,
      .edit-textarea {
        width: 100%;
        margin-bottom: 8px;
      }

      .edit-actions {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
      }
    }
  }

  // 兼容不支持 line-clamp 的浏览器
  @supports not (-webkit-line-clamp: 2) {
    .text-content {
      &.lines-1 {
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      &.lines-2,
      &.lines-3 {
        position: relative;
        max-height: 2.8em;
        overflow: hidden;

        &::after {
          content: '...';
          position: absolute;
          bottom: 0;
          right: 0;
          background: white;
          padding-left: 20px;
          background: linear-gradient(to right, transparent, white 50%);
        }
      }
    }
  }
</style>
