<template>
  <div class="common-columns-select">
    <a-dropdown v-model:popup-visible="menuVisible" :hide-on-select="false">
      <a-button style="min-width: 120px">
        <icon-common />
        <span class="ml-5 mr-5">
          {{ checkedTmp?.name || '自定义列' }}
        </span>
        <icon-down />
      </a-button>
      <template #content>
        <template v-if="fieldsData.length > 0">
          <a-spin
            v-for="item in fieldsData"
            :key="item.id"
            :loading="!!item.loading"
            style="display: block"
          >
            <a-doption
              :key="item.id"
              class="field-item"
              @click="useFieldCheck(item)"
            >
              <span class="label">
                {{ item.name }}
              </span>
              <span class="field-item-tool">
                <icon-check-circle
                  v-if="item.id === checkedTmp?.id"
                  class="is-checked"
                />
                <span class="opt-box">
                  <icon-edit
                    class="mr-5 action-btn"
                    @click.stop="selectColumns(item)"
                  />
                  <!-- <icon-heart
                    class="mr-5 action-btn"
                    @click.stop="setDefault(item)"
                  /> -->
                  <a-popconfirm
                    trigger="click"
                    :content="`确定删除【${item.name}】吗?`"
                    @ok="removeTheField(item)"
                  >
                    <icon-delete class="action-btn" @click.stop />
                  </a-popconfirm>
                </span>
              </span>
            </a-doption>
          </a-spin>
        </template>
        <a-empty v-else></a-empty>
        <a-divider :margin="4" />
        <a-doption key="add" class="field-item" @click="selectColumns(null)">
          <span>
            <icon-plus class="mr-10" />
            <span>自定义列</span>
          </span>
        </a-doption>
      </template>
    </a-dropdown>

    <!--列选择弹窗-->
    <check-columns
      ref="popCheckColumnsRef"
      :field-map="theFieldMap"
      :need-format-column="needFormatColumn"
      :is-save="true"
      :media-id="mediaId"
      :interfaces="saveInterface"
      :send-params="sendParams"
      :local-data="localData"
      @over-handle="selectColumnsOver"
      @refresh-handle="saveAndRefreshHandle"
    ></check-columns>
  </div>
</template>

<script lang="ts" setup>
  import { getColumnsFormMap } from '@/utils/table-utils/columns-setting';

  import request from '@/api/request';
  import { cloneDeep } from 'lodash';
  import { onBeforeMount, onMounted, ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import CheckColumns from './check-columns.vue';

  const props = defineProps({
    defaultColumns: {
      type: Array,
      default: () => {
        return [];
      },
    },
    mediaId: {
      type: [String, Number],
      default: () => '',
    },
    setDefaultInterface: {
      type: String,
      // default: '/api/userReportFieldsDefaultSave',
      default: '/api/showFieldsSort',
    },
    listInterface: {
      type: String,
      default: '/api/showFieldsList',
    },
    saveInterface: {
      type: String,
      default: '/api/showFieldsSave',
    },
    delInterface: {
      type: String,
      default: '/api/showFieldsDel',
    },
    theFieldMap: {
      type: Array,
      default: () => [],
    },
    sendParams: {
      type: Object,
      default: () => ({}),
    },
    needFormatColumn: {
      type: Boolean,
      default: true,
    },
    // 本地处理 不请求接口
    localData: {
      type: Boolean,
      default: false,
    },
  });

  const checkedColumns = ref<any[]>([]); // 当前已选中的列
  const menuVisible = ref(false);
  const fieldsData = ref<any[]>([]); // 保存的列数据
  const checkedTmp = ref<any>({});
  const popCheckColumnsRef = ref();
  const emits = defineEmits(['selectColumnsOk']);

  function initData() {
    checkedColumns.value = cloneDeep(props.defaultColumns);
  }
  function setSelectInfo(info: any) {
    info = info || {};
    checkedTmp.value = info;
  }
  // 列选择弹窗保存时的回调
  function selectColumnsOver(keys: any, selectInfo?: any) {
    setSelectInfo(selectInfo);
    if (props.needFormatColumn) {
      emits('selectColumnsOk', keys);
    } else {
      emits('selectColumnsOk', getColumnsFormMap(props.theFieldMap, keys));
    }
    checkedColumns.value = keys;
  }

  // 请求列数据
  async function getFieldList() {
    let resData = { data: [] };
    if (props.localData) {
      resData.data =
        JSON.parse(localStorage.getItem(props.sendParams.type) || '[]') || [];
    } else {
      resData = await request(props.listInterface, {
        ...props.sendParams,
      });
    }
    let checkedColumnsTemp = checkedColumns.value; // 默认使用初始化或者最后一次保存的列配置
    let tempData = resData.data || [];
    fieldsData.value = tempData;
    // 用户有自定义列，并且有默认选中或者刚新建的列配置时，切换默认的列选择配置
    if (tempData.length > 0) {
      let findDefaultData = tempData.find((item: any) => item.is_default === 1);
      let findCurrentData = tempData.find(
        (item: any) => item.id === checkedTmp.value.id
      );
      findCurrentData = findCurrentData || findDefaultData || tempData[0]; // 找用户默认配置或者第一个
      setSelectInfo(findCurrentData);
      checkedColumnsTemp = findCurrentData?.fields || [];
      selectColumnsOver(checkedColumnsTemp, findCurrentData);
    } else {
      selectColumnsOver(checkedColumnsTemp);
    }
  }

  // 列选择保存和刷新列数据时回调
  function saveAndRefreshHandle(itemData: any) {
    if (props.localData) {
      let targetValue = fieldsData.value.find(
        (item) => item.id === itemData.id
      );
      if (targetValue) {
        Object.assign(targetValue, itemData);
      } else {
        fieldsData.value.push(itemData);
      }
      localStorage.setItem(
        props.sendParams.type,
        JSON.stringify(fieldsData.value)
      );
    } else {
      getFieldList();
    }
  }
  // 设置默认列
  async function setDefault(editItem: any) {
    await request(props.setDefaultInterface, {
      id: editItem.id,
    });
    Message.success('设置成功');
  }
  // 点击列表使用列选择
  function useFieldCheck(item: any) {
    if (item && item.fields) {
      selectColumnsOver(item.fields, item);
      setDefault(item);
    }
  }

  // 删除自定义保存的列
  async function removeTheField(item: any) {
    if (props.localData) {
      fieldsData.value = fieldsData.value.filter(
        (citem) => citem.id !== item.id
      );
      localStorage.setItem(
        props.sendParams.type,
        JSON.stringify(fieldsData.value)
      );
    } else {
      item.loading = true;
      request(props.delInterface, {
        id: item.id,
        ...props.sendParams,
      }).finally(() => {
        item.loading = false;
        getFieldList();
      });
    }

    // 如果删除的是当前已选中的，则取消选中信息
    if (item.id === checkedTmp.value.id) {
      selectColumnsOver(cloneDeep(props.defaultColumns));
    }
  }

  // 自定义列选择打开
  function selectColumns(editItem: any) {
    let tempChecked;
    // 编辑列字段内容
    if (editItem && editItem.id) {
      tempChecked = editItem.fields;
    } else {
      tempChecked = checkedColumns.value;
    }
    popCheckColumnsRef.value?.show(cloneDeep(tempChecked), cloneDeep(editItem));
  }

  onMounted(() => {
    initData();
    getFieldList();
  });
</script>

<style lang="less" scoped>
  .field-item {
    position: relative;
    display: flex;
    width: 160px;
    .label {
      width: 100px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    :deep(.arco-dropdown-option-content) {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .field-item-tool {
      position: relative;
      display: flex;
      align-items: center;
      .opt-box {
        position: relative;
        display: none;
        .action-btn:hover {
          color: rgb(var(--primary-6));
        }
      }
    }

    .is-checked {
      color: #52c41a;
      display: flex;
    }
    &:hover {
      .is-checked {
        display: none;
      }

      .opt-box {
        display: flex;
        align-items: center;
      }
    }
  }
</style>
