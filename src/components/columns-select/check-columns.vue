<template>
  <d-modal
    :visible="visible"
    :loading="loading"
    title="选择数据列"
    width="1100px"
    :mask-closable="false"
    :esc-to-close="false"
    unmount-on-close
    @cancel="onClose"
  >
    <div class="search-tool mt-10">
      <a-input-search
        v-model="searchValue"
        allow-clear
        placeholder="可搜索列名称"
        style="width: 260px"
        @clear="onSearch"
        @search="onSearch"
        @keydown.enter="onSearch"
      />
    </div>
    <a-row :gutter="16">
      <a-col :span="18">
        <div class="columns-source">
          <div class="title">可添加的列</div>
          <div v-if="columnsList.length > 0" class="body">
            <div class="menu">
              <a-anchor
                :affix="false"
                :get-container="() => $refs['columnCheckListRef']"
                @click="handleClick"
              >
                <a-anchor-link
                  v-for="item in columnsList"
                  :key="item.dataIndex"
                  :href="`#${item.dataIndex}`"
                  :title="item.title"
                />
              </a-anchor>
            </div>
            <div ref="columnCheckListRef" class="list">
              <div v-for="item in columnsList" :key="item.dataIndex">
                <h4 :id="item.dataIndex" class="columns-title">
                  {{ item.title }}
                </h4>
                <ul class="column-list">
                  <li v-if="!item.isSearch" :key="item.dataIndex">
                    <a-checkbox
                      :model-value="item.checkAll"
                      :indeterminate="item.indeterminate"
                      @change="onCheckChange($event, item)"
                      >全选
                    </a-checkbox>
                  </li>
                  <li v-for="info in item.dataList" :key="info.key">
                    <a-checkbox
                      :key="info.key"
                      :model-value="checkedKeys.includes(info.key)"
                      :value="info.key"
                      :disabled="includeField.includes(info.key)"
                      @change="onChange($event, info.key)"
                      >{{ info.title }}
                      <a-tooltip v-if="info.description">
                        <template #content>
                          <div style="max-width: 200px">
                            {{ info.description }}
                          </div>
                        </template>
                        <icon-question-circle />
                      </a-tooltip>
                    </a-checkbox>
                  </li>
                </ul>
              </div>
              <div style="height: 250px"></div>
            </div>
          </div>
          <div v-else class="body">
            <a-empty class="empty-box"></a-empty>
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="columns-selected">
          <div class="title">
            <span>已选 {{ checkedKeys.length }} 列</span>
            <a-link @click="clearAll">清空全部</a-link>
          </div>
          <draggable
            v-if="checkedKeys.length > 0"
            v-model="checkedKeys"
            v-bind="dragOptions"
            class="body"
            :item-key="(element) => element"
            @end="drag = false"
            @start="drag = true"
          >
            <template #item="{ element }">
              <a-tag
                v-if="getTagTitle(element)"
                :key="element"
                :closable="!includeField.includes(element)"
                class="tag-item"
                @close="() => handleClose(element)"
              >
                <icon-menu style="color: #8c8c8c; font-size: 12px" />
                <span class="ml-5" style="flex: 1">
                  {{ getTagTitle(element) }}
                </span>
              </a-tag>
            </template>
          </draggable>
          <div v-else class="body">
            <a-empty class="empty-box" description="暂无选择"></a-empty>
          </div>
        </div>
      </a-col>
    </a-row>
    <template #footer>
      <div class="drawer-bottom-content">
        <div>
          <a-form
            ref="saveFormRef"
            class="drawer-bottom-form"
            :model="saveForm"
            layout="inline"
            style="margin-top: -5px"
          >
            <a-form-item v-if="isSave">
              <a-checkbox
                v-model:model-value="isSaveTheField"
                @change="saveForm.name = ''"
              >
                保存为常用自定义列
              </a-checkbox>
            </a-form-item>
            <a-form-item
              v-if="isSaveTheField && needName"
              :rules="{ required: true, message: '请输入', trigger: 'blur' }"
              field="name"
            >
              <a-input
                v-model="saveForm.name"
                placeholder="请输入自定义列名称"
                style="display: inline; width: 200px"
              ></a-input>
            </a-form-item>
          </a-form>
        </div>
        <a-space>
          <a-button @click="onClose">取消</a-button>
          <a-button :loading="loading" type="primary" @click="sendInfo">
            确 定
          </a-button>
        </a-space>
      </div>
    </template>
  </d-modal>
</template>

<script>
  import draggable from 'vuedraggable';
  import { cloneDeep } from 'lodash';
  import request from '@/api/request';
  import DModal from '@/components/d-modal/d-modal.vue';
  import { Message } from '@arco-design/web-vue';
  import {
    getColumns,
    getColumnTitle,
  } from '@/utils/table-utils/columns-setting';

  const tempForm = {
    id: '',
    name: '',
    fields: [],
  };
  export default {
    name: 'CheckColumns',
    components: {
      DModal,
      draggable,
    },
    props: {
      isSave: {
        default: false,
        type: Boolean,
      },
      customField: {
        type: Array,
        default: () => {
          return [];
        },
      },
      includeField: {
        type: Array,
        default: () => {
          return [];
        },
      },
      fieldMap: {
        default: () => {
          return [];
        },
        type: Array,
      },
      mediaId: {
        default: () => '',
        type: [String, Number],
      },
      fieldsId: {
        default: () => '',
        type: [String, Number],
      },
      interfaces: {
        type: String,
        default: '',
      },
      needName: {
        default: true,
        type: Boolean,
      },
      sendParams: {
        type: Object,
        default: () => ({}),
      },
      needFormatColumn: {
        type: Boolean,
        default: true,
      },
      // 本地处理 不请求接口
      localData: {
        type: Boolean,
        default: false,
      },
    },
    emits: ['overHandle', 'refreshHandle'],
    data() {
      return {
        drag: false,
        loading: false,
        isEditFlag: false,
        columnsList: [],
        cacheDataList: [],
        checkedKeys: [],
        visible: false,
        isSaveTheField: false, // 当填写模板名称时，认为要保存当前选择的列为模板
        saveForm: {
          ...tempForm,
        },
        searchValue: '',
      };
    },
    computed: {
      dragOptions() {
        return {
          animation: 200,
          group: 'description',
          disabled: false,
          ghostClass: 'ghost',
        };
      },
    },
    watch: {
      checkedKeys: {
        handler() {
          this.resetCheckAll();
        },
        immediate: true,
        deep: true,
      },
    },
    created() {},
    mounted() {},
    methods: {
      onClose() {
        this.visible = false;
      },
      onSearch() {
        if (!this.searchValue) {
          this.columnsList = cloneDeep(this.cacheDataList);
        } else {
          let tempArr = [];
          this.cacheDataList.forEach((item) => {
            let eItem = { ...item };
            let dataList = eItem.dataList.filter((ele) =>
              ele.title
                .toLocaleLowerCase()
                .includes(this.searchValue.toLocaleLowerCase())
            );
            eItem.isSearch = false;
            if (dataList.length > 0) {
              eItem.isSearch = true;
              eItem.dataList = [...dataList];
              tempArr.push(eItem);
            }
          });
          this.columnsList = tempArr;
        }
        // 搜索后重新检测全选与否
        this.resetCheckAll();
      },
      clearAll() {
        this.checkedKeys = [];
      },
      handleClick(e, link) {
        e.preventDefault();
      },
      // 初始化全选状态
      resetCheckAll() {
        Object.keys(this.columnsList).forEach((item) => {
          let itemInfo = this.columnsList[item];
          let itemKeys = itemInfo.keys;
          // 检查字段选中数量，选中数量对比得出是否全选，一个都没有选中则 indeterminate 取消声明
          let hasLen = this.getHasLength(itemKeys);
          itemInfo.checkAll = hasLen === itemKeys.length;
          itemInfo.indeterminate =
            hasLen === 0 ? undefined : hasLen !== itemKeys.length;
        });
      },
      // 检查arr中的项是不是都被选中了
      getHasLength(keysArr) {
        keysArr = keysArr || [];
        let len = 0;
        keysArr.forEach((val) => {
          if (this.checkedKeys.includes(val)) {
            len += 1;
          }
        });
        return len;
      },
      // 删除arr中包含的项目
      delTheCheckKeys(arr) {
        arr.forEach((item) => {
          this.handleClose(item);
        });
      },
      setTheCheckState(arr) {
        // 补充选中项，并去重
        this.checkedKeys = [...new Set([...this.checkedKeys, ...arr])];
      },
      // 全选触发事件
      onCheckChange(checked, item) {
        let checkAllState = checked;
        item.checkAll = checkAllState;
        if (checkAllState) {
          item.indeterminate = false;
          this.setTheCheckState(item.keys);
        } else {
          item.indeterminate = undefined;
          this.delTheCheckKeys(
            item.keys.filter((key) => !this.includeField.includes(key))
          );
        }
      },
      // 获取tag中文
      getTagTitle(key) {
        if (!this.needFormatColumn) {
          let title = '';
          // 遍历出key对应的title
          this.fieldMap.some((item) => {
            item.dataList.some((cItem) => {
              if (cItem.key === key) {
                title = cItem.title;
                return true;
              }
              return false;
            });
            return item.keys.includes(key);
          });
          return title;
        }
        return getColumnTitle(key, this.mediaId);
      },
      // tag 删除
      handleClose(removed) {
        this.checkedKeys.splice(
          this.checkedKeys.findIndex((item) => item === removed),
          1
        );
      },
      // checkbox 改变
      onChange(checked, key) {
        if (checked) {
          this.checkedKeys.push(key);
        } else {
          this.handleClose(key);
        }
      },
      // 确定
      sendInfo() {
        this.$refs.saveFormRef.validate(async (valid) => {
          if (!valid) {
            // 是否需要保存
            if (this.isSaveTheField) {
              if (this.checkedKeys.length === 0) {
                Message.error('保存为模板时，至少需要选择1个列');
                return;
              }
              await this.saveData();
            } else {
              this.$emit('overHandle', this.checkedKeys, { ...this.saveForm });
              this.visible = false;
            }
          }
        });
      },
      // 保存列数据
      async saveData() {
        this.loading = true;
        this.saveForm.fields = this.checkedKeys;
        this.saveForm.id = this.saveForm.id || this.fieldsId || '';
        if (!this.needName) {
          this.saveForm.name = '默认模板';
        }
        let resData = {};
        if (this.localData) {
          resData = {
            data: {
              id: this.saveForm.id || Date.now(),
              name: this.saveForm.name,
              fields: this.checkedKeys,
            },
          };
        } else {
          resData = await request(this.interfaces, {
            ...this.saveForm,
            ...this.sendParams,
          });
        }

        this.loading = false;
        // 保存了新的模板，刷新列选择数据
        this.$emit('refreshHandle', resData.data);
        this.$emit('overHandle', this.checkedKeys, {
          ...this.saveForm,
          id: resData.data.id,
        });
        this.visible = false;
      },
      /**
       * @description 描述信息
       * @param keys {string[]} 选中的列的key
       * @param checkItem {object} 编辑自定义的数据
       * @return {null}
       */
      async show(keys, checkItem) {
        this.visible = true;
        this.searchValue = '';
        this.saveForm = {
          ...tempForm,
        };
        // 编辑列选择模板
        if (checkItem && checkItem.id) {
          this.isSaveTheField = true;
          this.saveForm.name = checkItem.name;
          this.saveForm.id = checkItem.id;
        } else {
          this.isSaveTheField = false;
        }
        this.checkedKeys = keys || [];
        if (this.needFormatColumn) {
          this.columnsList = this.fieldMap.map((item) => {
            return {
              ...item,
              dataList: getColumns(item.keys, this.mediaId),
            };
          });
        } else {
          this.columnsList = cloneDeep(this.fieldMap);
        }

        if (this.customField.length) {
          this.columnsList.unshift({
            dataIndex: 'customField',
            dataList: this.customField.map((item) => {
              return {
                dataIndex: item.label,
                key: item.label,
                title: item.label,
              };
            }),
            keys: this.customField.map((item) => item.label),
            title: '自定义字段',
          });
        }
        this.cacheDataList = cloneDeep(this.columnsList);
      },
    },
  };
</script>

<!--suppress CssInvalidPropertyValue -->
<style lang="less" scoped>
  .column-list {
    list-style: none;
    margin: 10px 0 0;
    padding: 0;
  }

  .column-tab {
    padding-bottom: 10px;
  }

  .checked-columns {
    .ant-tag {
      margin-bottom: 5px;
    }
  }

  .ant-anchor-wrapper {
    margin-left: 0;
    padding-left: 0;
    padding-top: 10px;

    :deep(.ant-anchor-ink) {
      display: none;
    }
  }

  .search-tool {
    padding-bottom: 15px;
  }

  .columns-source {
    border: 1px solid #e4e9ed;

    .title {
      background-color: #f9fafd;
      border-bottom: 1px solid #e4e9ed;
      line-height: 23px;
      padding: 8px 10px;
    }

    .body {
      display: flex;
      height: 400px;
      justify-content: space-between;

      .menu {
        height: 100%;
        width: 230px;
        .arco-anchor {
          width: 180px;
        }
      }

      .list {
        border-left: 1px solid #e4e9ed;
        height: 100%;
        width: calc(100% - 141px);
        overflow: hidden;
        overflow-y: auto;

        .columns-title {
          margin: -1px 0 0 0;
          padding: 10px 15px;
          border-top: 1px solid #e4e9ed;
          font-size: 16px;
          font-weight: normal;
        }

        .column-list {
          margin: 0;
          padding: 0 15px 10px;

          li {
            display: inline-block;
            width: 50%;
            padding: 0 0 10px;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .columns-selected {
    border: 1px solid #e4e9ed;

    .title {
      background-color: #f9fafd;
      border-bottom: 1px solid #e4e9ed;
      line-height: 23px;
      padding: 8px 10px;
      display: flex;
      justify-content: space-between;
    }

    .body {
      position: relative;
      padding: 5px 5px 0;
      height: 400px;
      overflow: hidden;
      overflow-y: auto;
    }

    .tag-item {
      cursor: move;
      display: block;
      border-radius: 2px;
      padding: 14px 20px;
      margin-bottom: 5px;
      position: relative;
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      border: none;
      display: flex;
      align-items: center;
      user-select: none;
    }
  }

  .empty-box {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -70%);
  }

  .ghost {
    opacity: 0.5;
    background: #c8ebfb;
  }

  .drawer-bottom-content {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .drawer-bottom-form {
    :deep(.ant-form-explain) {
      display: inline-block;
      padding-left: 10px;
    }
  }
</style>
