<template>
  <div class="table-cell-text" :class="{ 'has-copy': showCopy }">
    <a-tooltip v-if="shouldShowTooltip" :content="fullText" position="top">
      <div class="text-content" :class="{ [`lines-${maxLines}`]: true }">
        {{ displayText }}
      </div>
    </a-tooltip>
    <div v-else class="text-content" :class="{ [`lines-${maxLines}`]: true }">
      {{ displayText }}
    </div>

    <a-button
      v-if="showCopy && fullText"
      type="text"
      size="mini"
      class="copy-button"
      @click="handleCopy"
    >
      <template #icon>
        <icon-copy />
      </template>
    </a-button>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useClipboard } from '@vueuse/core';

  interface Props {
    text?: string | null;
    maxLines?: number;
    maxLength?: number;
    showCopy?: boolean;
    placeholder?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    text: '',
    maxLines: 2,
    maxLength: 100,
    showCopy: true,
    placeholder: '-',
  });

  const { copy } = useClipboard();

  // 完整文本
  const fullText = computed(() => {
    if (!props.text || props.text === null || props.text === undefined) {
      return '';
    }
    return String(props.text).trim();
  });

  // 显示文本
  const displayText = computed(() => {
    if (!fullText.value) {
      return props.placeholder;
    }
    return fullText.value;
  });

  // 是否需要显示 tooltip（当文本超出时）
  const shouldShowTooltip = computed(() => {
    if (!fullText.value) return false;

    // 简单的长度判断，实际可以根据需要调整
    const estimatedLinesLength = props.maxLines * 10; // 假设每行约30个字符
    return fullText.value.length > estimatedLinesLength;
  });

  // 复制功能
  const handleCopy = async () => {
    if (!fullText.value) return;

    try {
      await copy(fullText.value);
      Message.success('复制成功');
    } catch (error) {
      console.error('复制失败:', error);
      Message.error('复制失败');
    }
  };
</script>

<style scoped lang="less">
  .table-cell-text {
    position: relative;
    width: 100%;
    min-height: 20px;

    &.has-copy {
      padding-right: 24px;
    }

    .text-content {
      word-break: break-word;
      word-wrap: break-word;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      line-height: 1.4;

      &.lines-1 {
        -webkit-line-clamp: 1;
        max-height: 1.4em;
      }

      &.lines-2 {
        -webkit-line-clamp: 2;
        max-height: 2.8em;
      }

      &.lines-3 {
        -webkit-line-clamp: 3;
        max-height: 4.2em;
      }
    }

    .copy-button {
      position: absolute;
      top: 0;
      right: 0;
      width: 20px;
      height: 20px;
      padding: 0;
      opacity: 0;
      transition: opacity 0.2s ease;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 2px;

      :deep(.arco-btn-icon) {
        font-size: 12px;
        color: var(--color-text-2);
      }

      &:hover {
        background: rgba(255, 255, 255, 1);

        :deep(.arco-btn-icon) {
          color: rgb(var(--primary-6));
        }
      }
    }

    &:hover .copy-button {
      opacity: 1;
    }
  }

  // 兼容不支持 line-clamp 的浏览器
  @supports not (-webkit-line-clamp: 2) {
    .text-content {
      &.lines-1 {
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      &.lines-2,
      &.lines-3 {
        position: relative;
        max-height: 2.8em;
        overflow: hidden;

        &::after {
          content: '...';
          position: absolute;
          bottom: 0;
          right: 0;
          background: white;
          padding-left: 20px;
          background: linear-gradient(to right, transparent, white 50%);
        }
      }
    }
  }
</style>
