import { App } from 'vue';
import { use } from 'echarts/core';
import { Icon } from '@arco-design/web-vue';
import { CanvasRenderer } from 'echarts/renderers';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'echarts/charts';
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  GraphicComponent,
  TitleComponent,
  MarkLineComponent,
  ToolboxComponent,
} from 'echarts/components';
import SearchForm from '@/components/juwei-compoents/search-form/SearchForm.vue';
import Chart from './chart/index.vue';
import Breadcrumb from './breadcrumb/index.vue';

import CRangePicker from './date-picker/RangePicker.vue';
import DictSelect from './dict-select/dict-select.vue';
import PlayPopover from './play-popover/index.vue';
import ImgPopover from './img-popover/index.vue';
import BaseTable from './juwei-compoents/base-table/base-table.vue';
import DepartmentUserTreeSelect from './department-tree-user/DepartmentUserTreeSelect.vue';

// Manually introduce ECharts modules to reduce packing size
const IconFont = Icon.addFromIconFontCn({
  src: '//at.alicdn.com/t/c/font_4144300_5zgj4celhy6.js',
});

const components: any = import.meta.globEager(
  './juwei-compoents/search-form/*.vue'
);
const componentEntries: any = Object.entries(components);

use([
  CanvasRenderer,
  BarChart,
  LineChart,
  PieChart,
  RadarChart,
  FunnelChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  GraphicComponent,
  TitleComponent,
  MarkLineComponent,
  ToolboxComponent,
]);

export default {
  install(Vue: App) {
    Vue.component('Chart', Chart);
    Vue.component('Breadcrumb', Breadcrumb);
    Vue.component('CRangePicker', CRangePicker);
    Vue.component('DictSelect', DictSelect);
    Vue.component('IconFont', IconFont);
    Vue.component('SearchForm', SearchForm);
    Vue.component('BaseTable', BaseTable);
    Vue.component('PlayPopover', PlayPopover);
    Vue.component('ImgPopover', ImgPopover);
    Vue.component('DepartmentUserTreeSelect', DepartmentUserTreeSelect);

    componentEntries.forEach(([path, component]: any) => {
      const name =
        path.match(/\.\/juwei-compoents\/search-form\/([\w-]+)\.vue$/)?.[1] ??
        'UnknownComponent';
      Vue.component(name, component.default);
    });
  },
};
