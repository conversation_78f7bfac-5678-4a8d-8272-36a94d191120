<template>
  <dict-select
    v-model="curVal"
    :data-list="filteredProductList"
    :loading="loading"
    :multiple="multiple"
    :max-tag-count="maxTagCount"
    :allow-clear="allowClear"
    :format-label="formatLabel"
    label-key="label"
    value-key="value"
    placeholder="请选择产品"
    v-bind="$attrs"
    @change="handleChange"
  />
</template>

<script lang="ts" setup>
  import { computed, ref, watch, inject } from 'vue';
  import { useDataCacheStore } from '@/store';

  const props = defineProps({
    modelValue: {
      type: [String, Number, Array],
      default: '',
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    maxTagCount: {
      type: Number,
      default: 0,
    },
    allowClear: {
      type: Boolean,
      default: true,
    },
    formatLabel: {
      type: Function,
      default: null,
    },
    // 地接社字段名，用于从表单数据中获取地接社值
    agencyField: {
      type: String,
      default: 'local_travel_agency',
    },
  });

  const emit = defineEmits(['update:modelValue', 'change']);
  const dataCacheStore = useDataCacheStore();
  const loading = ref(false);

  // 尝试从父组件注入表单数据
  const formData = inject('searchFormData', null) as any;

  // 从父组件获取表单数据的另一种方式
  const parentFormData = inject('theFormData', null) as any;

  const curVal = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val),
  });

  // 获取当前选中的地接社
  const selectedAgency = computed(() => {
    // 尝试从多个来源获取表单数据
    const data = formData || parentFormData;
    if (!data) return null;
    return data[props.agencyField] || null;
  });

  // 根据地接社过滤产品列表
  const filteredProductList = computed(() => {
    // 如果没有选择地接社，使用所有产品数据
    if (!selectedAgency.value) {
      // 使用 travelProducts 作为默认数据源
      return dataCacheStore.travelProducts || [];
    }

    // 使用更准确的数据源：根据地接社名称获取对应的产品
    const agencyList = dataCacheStore.getAgencyList();
    const selectedAgencyInfo = agencyList.find(
      (agency: any) => agency.agency_name === selectedAgency.value
    );

    if (!selectedAgencyInfo) {
      return [];
    }

    // 根据地接社ID获取产品列表
    const productList = dataCacheStore.getProductList(
      undefined,
      selectedAgencyInfo.agency_id
    );

    // 转换数据格式，确保包含 label 和 value 字段
    const formattedProducts = productList.map((product: any) => ({
      ...product,
      label:
        product.product_name || product.label || `产品${product.product_id}`,
      value: product.product_id || product.value,
    }));

    // 开发环境下添加调试信息
    if (process.env.NODE_ENV === 'development') {
      console.log('ProductSelectWithAgency - 地接社:', selectedAgency.value);
      console.log('ProductSelectWithAgency - 地接社信息:', selectedAgencyInfo);
      console.log(
        'ProductSelectWithAgency - 原始产品数量:',
        productList.length
      );
      console.log(
        'ProductSelectWithAgency - 格式化后产品数量:',
        formattedProducts.length
      );
      console.log(
        'ProductSelectWithAgency - 产品数据示例:',
        formattedProducts[0]
      );
    }

    return formattedProducts;
  });

  const handleChange = (val: any, option: any) => {
    emit('change', val, option);
  };

  // 监听地接社变化，清空产品选择
  watch(
    () => selectedAgency.value,
    (newAgency, oldAgency) => {
      // 当地接社发生变化时，清空当前产品选择
      if (newAgency !== oldAgency && props.modelValue) {
        emit('update:modelValue', props.multiple ? [] : '');
      }
    }
  );
</script>
