import { DEFAULT_LAYOUT } from '@/router/routes/base';
import { AppRouteRecordRaw } from '../types';

const RouterConfig: AppRouteRecordRaw = {
  path: '/product',
  name: 'product',
  redirect: '/product/product-manage',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '产品库',
    roles: ['*'],
    requiresAuth: true,
    order: 80,
  },
  children: [
    {
      path: 'product-manage',
      name: 'product-manage',
      component: () =>
        import('@/views/manage/product-manage/product-manage.vue'),
      meta: {
        locale: '产品管理',
        roles: ['*'],
        requiresAuth: true,
      },
    },
  ],
};
export default RouterConfig;
