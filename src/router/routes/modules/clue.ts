import { DEFAULT_LAYOUT } from '@/router/routes/base';
import { AppRouteRecordRaw } from '../types';

const RouterConfig: AppRouteRecordRaw = {
  path: '/clue',
  name: 'clue',
  redirect: '/clue/sale-clue-list-two',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '线索',
    roles: ['*'],
    requiresAuth: true,
    allow_out_user: true,
    order: 50,
  },
  children: [
    {
      path: 'all-clue-list',
      name: 'all-clue-list',
      component: () =>
        import('@/views/travel-clue/all-clue-list/all-clue-list.vue'),
      meta: {
        locale: '线索公海',
        roles: ['*'],
        requiresAuth: true,
      },
    },
    {
      path: 'sale-clue-list',
      name: 'sale-clue-list',
      component: () =>
        import('@/views/travel-clue/sale-clue-list/sale-clue-list.vue'),
      meta: {
        locale: '一级线索池',
        roles: ['*'],
        requiresAuth: true,
      },
    },
    {
      path: 'sale-clue-list-two',
      name: 'sale-clue-list-two',
      component: () =>
        import('@/views/travel-clue/sale-clue-list-two/sale-clue-list-two.vue'),
      meta: {
        locale: '二级线索池',
        roles: ['*'],
        requiresAuth: true,
        // 允许外部用户访问
        allow_out_user: true,
      },
    },
    {
      path: 'clue-setting',
      name: 'clue-setting',
      component: () =>
        import('@/views/travel-clue/clue-setting/clue-setting.vue'),
      meta: {
        locale: '线索收集配置',
        roles: ['*'],
        requiresAuth: true,
        // 不允许外部用户访问（默认为 false）
        allow_out_user: false,
      },
    },
    {
      path: 'image-manage',
      name: 'image-manage',
      component: () =>
        import('@/views/travel-clue/image-manage/image-manage.vue'),
      meta: {
        locale: '图片库',
        roles: ['*'],
        requiresAuth: true,
      },
    },
    {
      path: 'clue-report',
      name: 'clue-report',
      component: () =>
        import('@/views/travel-clue/clue-report/clue-report.vue'),
      meta: {
        locale: '线索概览',
        roles: ['*'],
        requiresAuth: true,
      },
    },
    {
      path: 'clue-goal-report',
      name: 'clue-goal-report',
      component: () =>
        import('@/views/travel-clue/clue-goal-report/clue-goal-report.vue'),
      meta: {
        locale: '线索目标复盘',
        roles: ['*'],
        requiresAuth: true,
      },
    },
  ],
};
export default RouterConfig;
