import { DEFAULT_LAYOUT } from '@/router/routes/base';
import { AppRouteRecordRaw } from '../types';

const RouterConfig: AppRouteRecordRaw = {
  path: '/manage',
  name: 'manage',
  redirect: '/manage/user-manage',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '管理',
    roles: ['*'],
    requiresAuth: true,
    order: 120,
  },
  children: [
    {
      path: 'user-manage',
      name: 'user-manage',
      component: () => import('@/views/manage/user-manage/user-manage.vue'),
      meta: {
        locale: '用户管理',
        roles: [1],
        requiresAuth: true,
      },
    },
    {
      path: 'department-manage',
      name: 'department-manage',
      component: () =>
        import('@/views/manage/department-manage/department-manage.vue'),
      meta: {
        locale: '部门管理',
        roles: [1],
        requiresAuth: true,
      },
    },
    {
      path: 'liveroom-manage',
      name: 'liveroom-manage',
      component: () =>
        import('@/views/manage/liveroom-manage/liveroom-manage.vue'),
      meta: {
        locale: '直播间管理',
        roles: ['*'],
        requiresAuth: true,
      },
    },
    {
      path: 'liveroom-schedule',
      name: 'liveroom-schedule',
      component: () =>
        import('@/views/manage/liveroom-manage/liveroom-schedule.vue'),
      meta: {
        locale: '直播间排班',
        roles: ['*'],
        requiresAuth: true,
        hideInMenu: true,
      },
    },

    {
      path: 'liveroom-product',
      name: 'liveroom-product',
      component: () =>
        import('@/views/manage/liveroom-product/liveroom-product.vue'),
      meta: {
        locale: '直播间套餐配置',
        roles: ['*'],
        requiresAuth: true,
      },
    },
    {
      path: 'account-manage',
      name: 'account-manage',
      component: () =>
        import('@/views/manage/account-manage/account-manage.vue'),
      meta: {
        locale: '内容号管理',
        roles: ['*'],
        requiresAuth: true,
      },
    },
    {
      path: 'shop-manage',
      name: 'shop-manage',
      component: () => import('@/views/manage/shop-manage/shop-manage.vue'),
      meta: {
        locale: '店铺管理',
        roles: ['*'],
        requiresAuth: true,
      },
    },
    {
      path: 'line-manage',
      name: 'line-manage',
      component: () => import('@/views/manage/line-manage/line-manage.vue'),
      meta: {
        locale: '旅游线路管理',
        roles: ['*'],
        requiresAuth: true,
      },
    },
    {
      path: 'action-log',
      name: 'action-log',
      component: () => import('@/views/manage/action-log/action-log.vue'),
      meta: {
        locale: '操作日志',
        roles: ['*'],
        requiresAuth: true,
      },
    },
    {
      path: 'assign-rule',
      name: 'assign-rule',
      component: () => import('@/views/manage/assign-rule/assign-rule.vue'),
      meta: {
        locale: '分配规则设置',
        roles: [1, 12],
        requiresAuth: true,
      },
    },
    {
      path: 'receive-payment',
      name: 'receive-payment',
      component: () =>
        import('@/views/manage/receive-payment/receive-payment.vue'),
      meta: {
        locale: '收款方管理',
        roles: ['*'],
        requiresAuth: true,
      },
    },
  ],
};
export default RouterConfig;
