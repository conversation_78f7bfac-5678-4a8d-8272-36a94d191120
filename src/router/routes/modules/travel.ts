import { DEFAULT_LAYOUT } from '@/router/routes/base';
import { AppRouteRecordRaw } from '../types';

const RouterConfig: AppRouteRecordRaw = {
  path: '/travel',
  name: 'travel',
  redirect: '/travel/travel-order-list',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '订单',
    roles: ['*'],
    requiresAuth: true,
    order: 30,
    allow_three_wood_customer_service: true,
  },
  children: [
    {
      path: 'travel-order-list',
      name: 'travel-order-list',
      component: () =>
        import('@/views/travel-order/travel-order-list/travel-order-list.vue'),
      meta: {
        locale: '订单列表',
        requiresAuth: false,
        allow_three_wood_customer_service: true,
      },
    },
    {
      path: 'travel-order-merge',
      name: 'travel-order-merge',
      component: () =>
        import(
          '@/views/travel-order/travel-order-merge/travel-order-merge.vue'
        ),
      meta: {
        locale: '合并订单列表',
        requiresAuth: false,
      },
    },

    {
      path: 'customer-service-report',
      name: 'customer-service-report',
      component: () =>
        import(
          '@/views/travel-order/customer-service/customer-service-report.vue'
        ),
      meta: {
        locale: '客服日报',
        requiresAuth: false,
      },
    },
    // {
    //   path: 'customer-service-bouns-report',
    //   name: 'customer-service-bouns-report',
    //   component: () =>
    //     import(
    //       '@/views/travel-order/customer-service/customer-service-bouns-report.vue'
    //     ),
    //   meta: {
    //     locale: '电销人效',
    //     requiresAuth: false,
    //   },
    // },
    // 电销人效
    {
      path: 'customer-analyze',
      name: 'customer-analyze',
      component: () =>
        import('@/views/travel-order/customer-analyze/index.vue'),
      meta: {
        locale: '转化率分析',
        roles: [1],
        requiresAuth: true,
      },
    },
    {
      path: 'live-room-report',
      name: 'live-room-report',
      component: () =>
        import('@/views/travel-order/live-room-report/live-room-report.vue'),
      meta: {
        locale: '直播间报表',
        requiresAuth: false,
      },
    },
    // {
    //  path: 'travel-price',
    //  name: 'travel-price',
    //  component: () =>
    //    import('@/views/travel-order/travel-price/travel-price.vue'),
    //  meta: {
    //    locale: '定价单',
    //    requiresAuth: false,
    //  },
    // },
    {
      path: 'redemption-rate-report',
      name: 'redemption-rate-report',
      component: () =>
        import(
          '@/views/travel-order/redemption-rate-report/redemption-rate-report.vue'
        ),
      meta: {
        locale: '核销率周报',
        requiresAuth: false,
      },
    },
    {
      path: 'business-data',
      name: 'business-data',
      component: () =>
        import('@/views/travel-order/business-data/business-data.vue'),
      meta: {
        locale: '经营数据',
        requiresAuth: false,
      },
    },
    {
      path: 'order-refund-report',
      name: 'order-refund-report',
      component: () =>
        import('@/views/travel-order/order-report/order-refund-report.vue'),
      meta: {
        locale: '退款数据',
        requiresAuth: false,
      },
    },
    {
      path: 'customer-service',
      name: 'customer-service',
      component: () =>
        import(
          '@/views/travel-order/customer-service/customer-service-list.vue'
        ),
      meta: {
        locale: '客服列表',
        requiresAuth: false,
      },
    },
    {
      path: 'customer-service-log',
      name: 'customer-service-log',
      component: () =>
        import(
          '@/views/travel-order/customer-service/customer-service-log.vue'
        ),
      meta: {
        locale: '客服登录日志',
        requiresAuth: false,
      },
    },
    {
      path: 'assign-customer-log',
      name: 'assign-customer-log',
      component: () =>
        import(
          '@/views/travel-order/assign-customer-log/assign-customer-log.vue'
        ),
      meta: {
        locale: '派单记录',
        requiresAuth: false,
      },
    },
    {
      path: 'performance-bonus',
      name: 'performance-bonus',
      component: () =>
        import('@/views/travel-order/performance-bonus/performance-bonus.vue'),
      meta: {
        roles: ['*'],
        locale: '绩效提成',
        requiresAuth: true,
      },
    },
  ],
};
export default RouterConfig;
