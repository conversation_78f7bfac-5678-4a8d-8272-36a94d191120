#!/usr/bin/env bash
npm run build --emptyOutDir

version="$(git symbolic-ref --short -q HEAD)"
hash="$(git rev-parse --short HEAD)"
date="$(git show -s --format=%cI)"
author="$(git show -s --format=%an)"
commit="$(git log --pretty=format:'%s' -1)"

filename="travel_dev"

cd ../travel-publish || exit
git pull
rm -rf $filename
cd ../travel-admin-frontend || exit
cp -rf ./dist/$filename ../travel-publish/

echo -----------------------------
echo $filename
echo -----------------------------

cd ../travel-publish || exit
git pull
git add .
git commit -m "$commit - $version:$hash|$author:$date"
git push
# 测试版本更新
curl -i "http://10.200.16.50:9161/sync.php?dir=$filename"
# 正式版本更新
#curl -i "https://travel.pengwin.com/sync.php?dir=$filename"

cd ../travel-admin-frontend || exit

echo "完成- $commit - $version:$hash|$author:$date -更新"


