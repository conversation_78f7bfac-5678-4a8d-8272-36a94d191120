#!/usr/bin/env bash
npm run build_tos --emptyOutDir

version="$(git symbolic-ref --short -q HEAD)"
hash="$(git rev-parse --short HEAD)"
date="$(git show -s --format=%cI)"
author="$(git show -s --format=%an)"
commit="$(git log --pretty=format:'%s' -1)"

if [[ $version =~ "/" ]] ; then
   filename="$(echo $version | awk -F/ '{print $NF}')"
    filename="${filename//./}"
else
    filename=$version
fi

if [[ $filename = "developer" ]] || [[ $filename = "master" ]] ; then
    filename="travel"
fi

cd ../travel-publish || exit
git pull
rm -rf $filename
cd ../travel-admin-frontend || exit
cp -rf ./dist/$filename ../travel-publish/

node upload_tos.js ../travel-publish/$filename codestatic/travel/$filename

echo -----------------------------
echo $filename
echo -----------------------------

cd ../travel-publish || exit
git pull
git add .
git commit -m "$commit - $version:$hash|$author:$date"
git push
# 测试版本更新
# curl -i "http://1.192.158.7:9051/sync_dist.php?dir=$filename"
curl -i "http://10.200.16.50:9161/sync_dist.php?dir=$filename"
# 正式版本更新
curl -i "https://travel.pengwin.com/syncDist?dir=$filename"

cd ../travel-admin-frontend || exit

echo "完成- $commit - $version:$hash|$author:$date -更新"


