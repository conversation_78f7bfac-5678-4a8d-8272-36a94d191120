{"name": "arco-design-pro-vue", "description": "Arco Design Pro for Vue", "version": "1.0.0", "private": true, "author": "ArcoDesign Team", "license": "MIT", "scripts": {"dev": "vite --config ./config/vite.config.dev.ts", "build": "vite build --config ./config/vite.config.prod.ts", "build_tos": "vite build --config ./config/vite.config.tos.ts", "report": "cross-env REPORT=true npm run build", "preview": "npm run build && vite preview --host", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint-staged": "npx lint-staged", "prepare": "husky install", "pub": "./pub.sh"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["prettier --write", "eslint --fix"], "*.vue": ["stylelint --fix", "prettier --write", "eslint --fix"], "*.{less,css}": ["stylelint --fix", "prettier --write"]}, "dependencies": {"@arco-design/web-vue": "^2.55.1", "@arco-themes/vue-juwei-common": "^0.0.1", "@volcengine/tos-sdk": "^2.6.11", "@vueuse/core": "^9.4.0", "axios": "^0.24.0", "dayjs": "^1.11.5", "echarts": "^5.4.0", "html-to-image": "^1.11.11", "lodash": "^4.17.21", "mitt": "^3.0.0", "nprogress": "^0.2.0", "numeral": "^2.0.6", "pinia": "^2.0.23", "query-string": "^8.0.3", "sortablejs": "^1.15.0", "spark-md5": "^3.0.2", "swiper": "^10.1.0", "vite-plugin-html-config": "^1.0.11", "vue": "^3.2.40", "vue-echarts": "^6.2.3", "vue-i18n": "^9.2.2", "vue-router": "^4.0.14", "vue3-marquee": "^4.0.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@arco-plugins/vite-vue": "^1.4.5", "@types/lodash": "^4.14.186", "@types/nprogress": "^0.2.0", "@types/numeral": "^2.0.2", "@types/sortablejs": "^1.15.0", "@types/spark-md5": "^3.0.5", "@typescript-eslint/eslint-plugin": "^5.40.0", "@typescript-eslint/parser": "^5.40.0", "@vitejs/plugin-vue": "^3.1.2", "@vitejs/plugin-vue-jsx": "^2.0.1", "@vue/babel-plugin-jsx": "^1.2.2", "consola": "^2.15.3", "cross-env": "^7.0.3", "eslint": "^8.25.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.6.0", "husky": "^8.0.1", "less": "^4.1.3", "lint-staged": "^13.0.3", "postcss-html": "^1.5.0", "prettier": "^2.7.1", "rollup": "^2.68.0", "rollup-plugin-visualizer": "^5.8.2", "stylelint": "^14.13.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^29.0.0", "stylelint-order": "^5.0.0", "typescript": "^4.8.4", "unplugin-vue-components": "^0.24.1", "vite": "^3.2.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-imagemin": "^0.6.1", "vite-svg-loader": "^3.6.0", "vue-tsc": "^1.0.14"}, "engines": {"node": ">=14.0.0"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china", "rollup": "^2.68.0", "gifsicle": "5.2.0"}}