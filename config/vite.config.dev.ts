import { mergeConfig } from 'vite';
import eslint from 'vite-plugin-eslint';
import baseConfig from './vite.config.base';

export default mergeConfig(baseConfig, {
  mode: 'development',
  server: {
    port: 4000,
    host: '0.0.0.0',
    open: true,
    fs: {
      strict: true,
    },
    // 设置反向代理，跨域
    proxy: {
      '/api': {
        // target: 'http://************:9161', // 测试环境后台
        // target: 'http://************:8096/', // 陈鑫本地
        target: 'https://travel.pengwin.com',
        changeOrigin: true,
      },
    },
  },
  plugins: [
    eslint({
      cache: false,
      include: ['src/**/*.ts', 'src/**/*.tsx', 'src/**/*.vue'],
      exclude: ['node_modules'],
    }),
  ],
});
