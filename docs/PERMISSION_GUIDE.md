# 外部用户权限控制指南

## 概述

本系统新增了外部用户权限控制功能，当检测到用户是外部用户（`out_user` 字段值为 1）时，会进行额外的权限验证。

## 核心概念

### 用户类型
- **内部用户**: `out_user` 不等于 1，保持原有权限控制逻辑
- **外部用户**: `out_user` 等于 1，需要额外检查 `allow_out_user` 字段

### 权限检查流程
1. 首先检查基础角色权限（roles）
2. 如果是外部用户，进一步检查目标功能的 `allow_out_user` 字段
3. 只有当 `allow_out_user` 为 true 时，外部用户才能访问该功能

## 使用方法

### 1. 路由权限配置

在路由的 `meta` 中添加 `allow_out_user` 字段：

```typescript
{
  path: 'sale-clue-list-two',
  name: 'sale-clue-list-two',
  component: () => import('@/views/travel-clue/sale-clue-list-two/sale-clue-list-two.vue'),
  meta: {
    locale: '二级线索池',
    roles: ['*'],
    requiresAuth: true,
    allow_out_user: true, // 允许外部用户访问
  },
}
```

### 2. 组件中使用权限指令

#### 原有权限指令（兼容性）
```vue
<!-- 原有指令仍然可用，但不支持外部用户权限控制 -->
<div v-permission="['admin', 'user']">
  内容
</div>
```

#### 增强权限指令（推荐）
```vue
<!-- 新的增强权限指令，支持外部用户权限控制 -->
<div v-permission-enhanced="{ roles: ['*'], allowOutUser: true }">
  所有用户可见，包括外部用户
</div>

<div v-permission-enhanced="{ roles: ['*'], allowOutUser: false }">
  所有内部用户可见，外部用户不可见
</div>

<div v-permission-enhanced="{ roles: [1], allowOutUser: true }">
  管理员可见，包括外部管理员
</div>
```

### 3. 使用 Hook 进行权限检查

```vue
<script setup lang="ts">
import { computed } from 'vue';
import { usePermission } from '@/hooks/usePermission';

const {
  userInfo,
  isExternalUser,
  checkPermission,
  checkOutUserPermission,
} = usePermission();

// 检查是否有权限
const canAccess = computed(() => 
  checkPermission(['admin'], true) // roles, allowOutUser
);

// 检查是否为外部用户
const isExternal = computed(() => isExternalUser.value);

// 检查外部用户权限
const canExternalAccess = computed(() => 
  checkOutUserPermission(true)
);
</script>

<template>
  <div v-if="canAccess">
    有权限的内容
  </div>
  
  <div v-if="isExternal">
    外部用户专用内容
  </div>
</template>
```

### 4. 使用工具函数

```typescript
import { hasPermission, isOutUser, hasOutUserPermission } from '@/utils/permission';

// 检查权限
const canAccess = hasPermission(['admin'], true);

// 检查是否为外部用户
const isExternal = isOutUser();

// 检查外部用户权限
const canExternalAccess = hasOutUserPermission(true);
```

### 5. 在 Store 中使用

```typescript
import { useUserStore } from '@/store';

const userStore = useUserStore();

// 检查外部用户权限
const canAccess = userStore.hasOutUserPermission(true);

// 综合权限检查
const hasFullAccess = userStore.hasFullPermission(['admin'], true);
```

## API 参考

### usePermission Hook

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `checkPermission` | `roles: (string\|number)[], allowOutUser?: boolean` | `boolean` | 检查是否有指定权限 |
| `checkOutUserPermission` | `allowOutUser?: boolean` | `boolean` | 检查外部用户权限 |
| `checkRoutePermission` | `route: any` | `boolean` | 检查路由权限 |
| `isExternalUser` | - | `ComputedRef<boolean>` | 是否为外部用户 |
| `userInfo` | - | `ComputedRef<UserPermissionInfo>` | 用户权限信息 |

### 权限工具函数

| 函数 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `hasPermission` | `roles: (string\|number)[], allowOutUser?: boolean` | `boolean` | 检查用户权限 |
| `isOutUser` | - | `boolean` | 检查是否为外部用户 |
| `hasOutUserPermission` | `allowOutUser?: boolean` | `boolean` | 检查外部用户权限 |
| `getUserPermissionInfo` | - | `UserPermissionInfo` | 获取用户权限信息 |

### UserStore 新增方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `hasOutUserPermission` | `allowOutUser?: boolean` | `boolean` | 检查外部用户权限 |
| `hasFullPermission` | `role: number\|string\|number[], allowOutUser?: boolean` | `boolean` | 综合权限检查 |

## 最佳实践

1. **路由配置**: 在需要外部用户访问的路由中明确设置 `allow_out_user: true`
2. **组件权限**: 优先使用 `v-permission-enhanced` 指令或 `usePermission` Hook
3. **默认策略**: 如果未设置 `allow_out_user`，外部用户将无法访问该功能
4. **权限粒度**: 可以在页面、组件、按钮等不同级别设置权限控制
5. **测试**: 确保在不同用户类型下测试功能的可访问性

## 迁移指南

### 从原有权限系统迁移

1. 保持现有的 `v-permission` 指令不变（向后兼容）
2. 对于需要外部用户访问的功能，使用新的 `v-permission-enhanced` 指令
3. 在路由配置中添加 `allow_out_user` 字段
4. 逐步将权限检查逻辑迁移到新的 Hook 和工具函数

### 注意事项

- 外部用户权限是在基础角色权限之上的额外检查
- 如果基础角色权限检查失败，外部用户权限检查不会执行
- 内部用户不受 `allow_out_user` 字段影响，保持原有逻辑
